import * as apiURL from "../../fixtures/dev/suche/api.json";

export class apiRequests{
    public getToken():Cypress.Chainable{
        const header = Cypress.env('header');
        const body = Cypress.env('body');
        return cy.request({
            method: 'POST',
            url: apiURL.URLs.tokenURL,
            headers: header,
            body: new URLSearchParams(body).toString(),
            failOnStatusCode: false
        })
    }

    public getStatisticsByYearAndState(access_token:string,URL:any,year:number,state:string):Cypress.Chainable{
        return cy.request({
            method: 'GET',
            url: URL,
            headers: {
                Authorization: `Bearer ${access_token}`
            },
            qs: {
                year: year,
                federalState: state
            }
        })
    }
    
    public getStatisticsByYearStateAndOffice(access_token:string,URL:any,year:number,state:string,officeName:string):Cypress.Chainable{
        return cy.request({
            method: 'GET',
            url: URL,
            headers: {
                Authorization: `Bearer ${access_token}`
            },
            qs: {
                year: year,
                federalState: state,
                office: officeName
            }
        })
    }

    public getStatisticsActiveBans(access_token:string,URL:any):Cypress.Chainable{
        return cy.request({
            method: 'GET',
            url: URL,
            headers: {
                Authorization: `Bearer ${access_token}`
            }
        })
    }
}