export class HeaderComponentCo {
  private readonly HEADER_COMPONENT: string = '[data-testid="header-component"]';
  private readonly HEADER_COMPONENT_LOGO: string = '[data-testid="logo"]';
  private readonly HEADER_COMPONENT_STATE_LOGO: string = '[data-testid="state-logo"]';
  private readonly HEADER_USER_INFO: string = '[data-testid="user-info"]';
  private readonly HEADER_USER_INFO_USERNAME: string = '[data-testid="info-username"]';
  private readonly HEADER_USER_INFO_OFFICE: string = '[data-testid="info-government-office"]';
  private readonly HEADER_USER_INFO_PROFILE_ICON: string = '[data-testid="profile-icon"]';
  private readonly HEADER_LOGOUT_BUTTON: string = '[data-testid=logout-button]';
  private readonly HEADER_DROPDOWN_MENU: string = '[data-testid="user-dropdown-menu-opener"]'
  private readonly HEADER_SERVICE_OVERVIEW_BUTTON: string = '[data-testid="service-overview-button"]';
  private readonly HEADER_STATISTICS_BUTTON: string = '[data-testid="header-reportings-button"]';
  private readonly HEADER_LICENCE_REQUESTS_BUTTON: string = '[data-testid="header-license-applications-button"]';
  private readonly TAB_GROUP: string = '[data-testid="tab-group"]';
  private readonly TAB_TITLE: string = '[data-testid="tab-title"]';

  public getHeaderComponent(): Cypress.Chainable {
    return cy.get(this.HEADER_COMPONENT);
  }
  
  public getHeaderLogo(): Cypress.Chainable {
    return this.getHeaderComponent().find(this.HEADER_COMPONENT_LOGO);
  }
  
  public getHeaderStateLogo(): Cypress.Chainable {
    return this.getHeaderComponent().find(this.HEADER_COMPONENT_STATE_LOGO).find('img');
  }
  
  public getHeaderUserInfo(): Cypress.Chainable {
    return this.getHeaderComponent().find(this.HEADER_USER_INFO);
  }
  
  public getHeaderDropdownMenu(): Cypress.Chainable {
    return this.getHeaderComponent().find(this.HEADER_DROPDOWN_MENU);
  }
  
  public getHeaderLogoutButton(): Cypress.Chainable {
    return cy.get(this.HEADER_LOGOUT_BUTTON);
  }
  
  public getHeaderUserInfoUsername(): Cypress.Chainable {
    return this.getHeaderUserInfo().find(this.HEADER_USER_INFO_USERNAME);
  }

  public getHeaderUserInfoOffice(): Cypress.Chainable {
    return this.getHeaderUserInfo().find(this.HEADER_USER_INFO_OFFICE);
  }
  
  public getHeaderUserInfoProfileIcon(): Cypress.Chainable {
    return this.getHeaderUserInfo().find(this.HEADER_USER_INFO_PROFILE_ICON);
  }
  
  public getHeaderUserInfoLogoutButton(): Cypress.Chainable {
    return this.getHeaderUserInfo().find('button');
  }

  public getServiceOverviewButton(): Cypress.Chainable{
    return cy.get(this.HEADER_SERVICE_OVERVIEW_BUTTON)
  }
  
  public getTabGroup(): Cypress.Chainable {
    return cy.get(this.TAB_GROUP);
  }

  public getAllTabTitle(): Cypress.Chainable {
    return this.getTabGroup().find(this.TAB_TITLE);
  }

  public getAllTabTitleState(): Cypress.Chainable {
    return this.getAllTabTitle().find('button');
  }

  public getStatisticsButton(): Cypress.Chainable{
    return this.getHeaderComponent().find(this.HEADER_STATISTICS_BUTTON)
  }

  public getRequestsButton(): Cypress.Chainable{
    return this.getHeaderComponent().find(this.HEADER_LICENCE_REQUESTS_BUTTON)
  }
}
