import { HeaderComponentCo } from "../../sharedComponents/header-component.co";
import { EditFooterComponentCo } from "../../sharedComponents/edit-footer-component.co";
import { TabPersonalDataCo } from "./tab-personal-data.co";
import { QualificationDataCo } from '../digitize-licence/tab-qualification-data.co';
import { PreviousPaymentsCo } from '../digitize-licence/tab-previous-payments.co';
import { TaxesCo } from '../digitize-licence/tab-taxes.co';
import { DigitalDocumentCo } from '../digitize-licence/tab-digital-documents.co';
import { DurationTabCo } from '../digitize-licence/tab-duration.co';
import { SpecialLicenceApprovalCo } from "./tab-special-licence-approval.co";

export class DigitizeLicensePo {
  private readonly DIGITIZE_LICENSE_PAGE: string = '[data-testid="digitize-license-page"]';
  
  private headerComponent: HeaderComponentCo = new HeaderComponentCo();
  private editFooterComponent: EditFooterComponentCo = new EditFooterComponentCo();
  private tabPersonalDataComponent: TabPersonalDataCo = new TabPersonalDataCo();
  private qualificationDataComponent: QualificationDataCo = new QualificationDataCo();
  private previousPaymentsComponent: PreviousPaymentsCo = new PreviousPaymentsCo();
  private taxesComponent: TaxesCo = new TaxesCo();
  private documentsComponent: DigitalDocumentCo = new DigitalDocumentCo();
  private durationComponent: DurationTabCo = new DurationTabCo();
  private specialLicenceComponent: SpecialLicenceApprovalCo = new SpecialLicenceApprovalCo();

  public getHeaderComponent(): HeaderComponentCo {
    return this.headerComponent;
  }

  public getEditFooterComponent(): EditFooterComponentCo {
    return this.editFooterComponent;
  }

  public getTabPersonalDataComponent(): TabPersonalDataCo {
    return this.tabPersonalDataComponent;
  }

  public getQualificationDataComponent(): QualificationDataCo {
    return this.qualificationDataComponent;
  }

  public getPreviousPaymentsComponent(): PreviousPaymentsCo {
    return this.previousPaymentsComponent;
  }

  public getTaxesComponent(): TaxesCo {
    return this.taxesComponent;
  }

  public getDocumentsComponent(): DigitalDocumentCo {
    return this.documentsComponent;
  }

  public getDurationComponent(): DurationTabCo {
    return this.durationComponent;
  }

  public getSpecialLicenceComponent(): SpecialLicenceApprovalCo {
    return this.specialLicenceComponent;
  }

  public getDigitizeLicensePage(): Cypress.Chainable {
    return cy.get(this.DIGITIZE_LICENSE_PAGE);
  }
}
