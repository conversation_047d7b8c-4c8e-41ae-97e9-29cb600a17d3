export class SpecialLicenceApprovalCo{
    private readonly SPECIAL_LICENCE_APPROVAL_PAGE: string = '[data-testid="limited-license-approval-step"]';
    private readonly SPECIAL_LICENCE_APPROVAL_FORM: string = '[data-testid="limited-license-approval-form"]';
    private readonly SPECIAL_LICENCE_APPROVAL_ID: string = '[data-testid="limited-license-approval-form-personal-sign"]';
    private readonly SPECIAL_LICENCE_APPROVAL_NAME: string = '[data-testid="limited-license-approval-form-name"]';
    private readonly SPECIAL_LICENCE_APPROVAL_EMAIL: string = '[data-testid="limited-license-approval-form-email"]';
    private readonly SPECIAL_LICENCE_APPROVAL_PHONE: string = '[data-testid="limited-license-approval-form-phone"]';
    private readonly SPECIAL_LICENCE_APPROVAL_CREATION: string = '[data-testid="limited-license-approval-form-created-at"]';
    private readonly SPECIAL_LICENCE_APPROVAL_FILE: string = '[data-testid="limited-license-approval-form-file-number"]';
    private readonly SPECIAL_LICENCE_APPROVAL_REGISTER_ID: string = '[data-testid="limited-license-approval-form-cash-register-sign"]';
    private readonly SPECIAL_LICENCE_PREVIEW_BOX: string = '[data-testid="limited-license-approval-document-preview-box"]';
    private readonly SPECIAL_LICENCE_PDF_LINK: string = '[data-testid="limited-license-approval-document-preview-box-show-document-button"]';
    private readonly SPECIAL_LICENCE_PDF_FILE: string = '[data-testid="payment-item-main-area"]';
    private readonly SPECIAL_LICENCE_INPUT: string = '[data-testid="input"]';


    public getApprovalPage():Cypress.Chainable{
        return cy.get(this.SPECIAL_LICENCE_APPROVAL_PAGE)
    }

    public getApprovalForm():Cypress.Chainable{
        return this.getApprovalPage().find(this.SPECIAL_LICENCE_APPROVAL_FORM)
    }

    public getEmployeeFormID():Cypress.Chainable{
        return this.getApprovalForm().find(this.SPECIAL_LICENCE_APPROVAL_ID)
    }

    public getEmployeeFormName():Cypress.Chainable{
        return this.getApprovalForm().find(this.SPECIAL_LICENCE_APPROVAL_NAME)
    }

    public getEmployeeFormEmail():Cypress.Chainable{
        return this.getApprovalForm().find(this.SPECIAL_LICENCE_APPROVAL_EMAIL)
    }

    public getEmployeeFormPhone():Cypress.Chainable{
        return this.getApprovalForm().find(this.SPECIAL_LICENCE_APPROVAL_PHONE)
    }

    public getDataFormCreationDate():Cypress.Chainable{
        return this.getApprovalForm().find(this.SPECIAL_LICENCE_APPROVAL_CREATION)
    }

    public getDataFormCreationDateInput():Cypress.Chainable{
        return this.getDataFormCreationDate().find(this.SPECIAL_LICENCE_INPUT)
    }

    public getDataFormFileNumber():Cypress.Chainable{
        return this.getApprovalForm().find(this.SPECIAL_LICENCE_APPROVAL_FILE)
    }

    public getDataFormCashRegister():Cypress.Chainable{
        return this.getApprovalForm().find(this.SPECIAL_LICENCE_APPROVAL_REGISTER_ID)
    }

    public getPreviewBox():Cypress.Chainable{
        return this.getApprovalPage().find(this.SPECIAL_LICENCE_PREVIEW_BOX)
    }

    public getPreviewDocuments():Cypress.Chainable{
        return this.getPreviewBox().find(this.SPECIAL_LICENCE_PDF_LINK)
    }

    public getPDFFile():Cypress.Chainable{
        return this.getPreviewBox().find(this.SPECIAL_LICENCE_PDF_FILE)
    }
}