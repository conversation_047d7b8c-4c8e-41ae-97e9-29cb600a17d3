export class TaxesCo{
    private readonly TAXES_PAGE: string = '[data-testid="digitize-tab-payments"]';
    private readonly TAXES_BOX_HEADER: string = '[data-testid="payment-box-cost"]';
    private readonly TAXES_NEW_LICENCE: string = '[data-testid="payment-box-fee"]';
    private readonly TAXES_EXISTING_LICENCE: string = '[data-testid="payment-box-payed-taxes"]';
    private readonly TAXES_EXTRA_LICENCE: string = '[data-testid="payment-box-tax-payment-item"]';
    private readonly TAXES_CHECKBOX: string = '[data-testid="checkbox"]'
    private readonly TAXES_PAYMENT_METHOD_CARD: string = '[data-testid="payment-method-card-option"]';
    private readonly TAXES_PAYMENT_METHOD_CASH: string = '[data-testid="payment-method-cash-option"]';
    private readonly TAXES_PAYMENT_METHOD_TRANSFER: string = '[data-testid="payment-method-bank-transfer-option"]';
    private readonly TAXES_EXTRA_DIALOG: string = '[data-test-id="dialog"]';
    private readonly TAXES_DIALOG_OPTIONS: string = '[data-testid="tax-dialog-duration-option"]';
    private readonly TAXES_DIALOG_OPTIONS_RABIO_BUTTON: string = '[data-testid="tax-dialog-duration-option-radio"]';
    private readonly TAXES_DIALOG_OPTIONS_YEAR_FROM: string = '[data-testid="tax-dialog-duration-option-year-from"]';
    private readonly TAXES_DIALOG_OPTIONS_YEAR_TO: string = '[data-testid="tax-dialog-duration-option-duration"]';
    private readonly TAXES_DIALOG_OPTIONS_YEAR_FROM_UP: string = '[data-testid="skipper-up"]';
    private readonly TAXES_DIALOG_OPTIONS_YEAR_FROM_DOWN: string = '[data-testid="skipper-down"]';
    private readonly TAXES_DIALOG_SUBMIT: string = '[data-testid="tax-dialog-confirm-button"]';
    private readonly TAXES_EXTRA_CONFIGURE: string = '[data-testid="tax-item-edit-button"]';
    private readonly TAXES_STATIC_VALUE: string = '[data-testid="static-tax-item"]';
    private readonly TAXES_PAYMENT_PAGE: string = '[data-testid="pay-tax-page"]';
    private readonly TAXES_PAYMENT_NEW_USER_PAGE: string = '[data-testid="create-person-tab-payments"]';

    public getTaxesPage():Cypress.Chainable{
        return cy.get(this.TAXES_PAGE)
    }

    public getTaxesBoxHeader():Cypress.Chainable{
        return cy.get(this.TAXES_BOX_HEADER)
    }

    public getTaxesNewLicence():Cypress.Chainable{
        return cy.get(this.TAXES_NEW_LICENCE)
    }

    public getStaticTaxValue():Cypress.Chainable{
        return cy.get(this.TAXES_STATIC_VALUE)
    }

    public getTaxesNewLicenceCheckbox():Cypress.Chainable{
        return this.getTaxesNewLicence().find(this.TAXES_CHECKBOX)
    }

    public getStaticTaxValueCheckbox():Cypress.Chainable{
        return this.getStaticTaxValue().find(this.TAXES_CHECKBOX)
    }

    public getTaxesExistingLicence():Cypress.Chainable{
        return this.getTaxesPage().find(this.TAXES_EXISTING_LICENCE)
    }

    public getTaxesExistingLicenceCheckbox():Cypress.Chainable{
        return this.getTaxesExistingLicence().find(this.TAXES_CHECKBOX)
    }

    public getTaxesExtraLicence():Cypress.Chainable{
        return cy.get(this.TAXES_EXTRA_LICENCE)
    }

    public getTaxesExtraLicenceConfigure():Cypress.Chainable{
        return this.getTaxesExtraLicence().find(this.TAXES_EXTRA_CONFIGURE)
    }

    public getTaxesExtraLicenceCheckbox():Cypress.Chainable{
        return this.getTaxesExtraLicence().find(this.TAXES_CHECKBOX)
    }

    public getPaymentMethodCard():Cypress.Chainable{
        return cy.get(this.TAXES_PAYMENT_METHOD_CARD)
    }

    public getPaymentMethodCash():Cypress.Chainable{
        return cy.get(this.TAXES_PAYMENT_METHOD_CASH)
    }

    public getPaymentExtraDialog():Cypress.Chainable{
        return cy.get(this.TAXES_EXTRA_DIALOG)
    }

    public getPaymentDialogOptions():Cypress.Chainable{
        return this.getPaymentExtraDialog().find(this.TAXES_DIALOG_OPTIONS)
    }

    public getPaymentDialogOptionsButtons():Cypress.Chainable{
        return this.getPaymentDialogOptions().find(this.TAXES_DIALOG_OPTIONS_RABIO_BUTTON)
    }

    public getPaymentDialogOptionsButtonsInput():Cypress.Chainable{
        return this.getPaymentDialogOptionsButtons().find('input[type="radio"]')
    }

    public getPaymentDialogYearFrom():Cypress.Chainable{
        return this.getPaymentExtraDialog().find(this.TAXES_DIALOG_OPTIONS_YEAR_FROM)
    }

    public getPaymentDialogYearTo():Cypress.Chainable{
        return this.getPaymentExtraDialog().find(this.TAXES_DIALOG_OPTIONS_YEAR_TO)
    }

    public getPaymentDialogYearUp():Cypress.Chainable{
        return this.getPaymentExtraDialog().find(this.TAXES_DIALOG_OPTIONS_YEAR_FROM_UP)
    }

    public getPaymentDialogYearDown():Cypress.Chainable{
        return this.getPaymentExtraDialog().find(this.TAXES_DIALOG_OPTIONS_YEAR_FROM_DOWN)
    }

    public getPaymentDialogConfirm():Cypress.Chainable{
        return this.getPaymentExtraDialog().find(this.TAXES_DIALOG_SUBMIT)
    }

    public getTaxesPaymentPage():Cypress.Chainable{
        return cy.get(this.TAXES_PAYMENT_PAGE)
    }

    public getTaxesPaymentsNewUserPage():Cypress.Chainable{
        return cy.get(this.TAXES_PAYMENT_NEW_USER_PAGE)
    }

    public getPaymentMethodTransfer():Cypress.Chainable{
        return cy.get(this.TAXES_PAYMENT_METHOD_TRANSFER)
    }
}