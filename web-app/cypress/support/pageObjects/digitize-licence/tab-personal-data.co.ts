export class TabPersonalDataCo {
  private readonly TAB_PERSONAL_DATA: string = '[data-testid="digitize-tab-personal-data"]';
  private readonly TAB_PERSONAL_DATA_LIMITED_LICENCE:string = '[data-testid="create-limited-license-page"]';
  private readonly PERSONAL_DATA_FORM: string = '[data-testid="personal-data-form"]';
  private readonly USER_DATA_FORM: string = '[data-testid="personal-base-data-form"]';
  private readonly PERSONAL_DATA_FORM_TITLE: string = '[data-testid="person-form-title"]';
  private readonly PERSONAL_DATA_FORM_FIRSTNAME: string = '[data-testid="person-form-firstname"]';
  private readonly PERSONAL_DATA_FORM_LASTNAME: string = '[data-testid="person-form-lastname"]';
  private readonly PERSONAL_DATA_FORM_BIRTH_NAME: string = '[data-testid="person-form-birth-name"]';
  private readonly PERSONAL_DATA_FORM_BIRTH_DATE: string = '[data-testid="person-form-birth-date"]';
  private readonly PERSONAL_DATA_FORM_BIRTH_PLACE: string = '[data-testid="person-form-birth-place"]';
  private readonly PERSONAL_DATA_FORM_NATIONALITY: string = '[data-testid="person-form-nationality"]';
  private readonly PERSONAL_DATA_FORM_ADDRESS_STREET: string = '[data-testid="address-form-street"]';
  private readonly TOGGLE_BOX: string = '[data-testid="personal-data-address-toggle-box"]';
  private readonly TOGGLE_BOX_ADDRESS_ICON: string = '[data-testid="address-toggle-box-address-icon"]';
  private readonly TOGGLE_BOX_NO_PERMANENT_RESIDENCE_ICON: string = '[data-testid="address-toggle-box-no-permanent-residence-icon"]';
  private readonly TOGGLE_BOX_ADDRESS_FORM: string = '[data-testid="address-form"]';
  private readonly TOGGLE_BOX_ADDRESS_FORM_STREET_NUMBER: string = '[data-testid="address-form-street-number"]';
  private readonly TOGGLE_BOX_ADDRESS_FORM_POST_CODE: string = '[data-testid="address-form-post-code"]';
  private readonly TOGGLE_BOX_ADDRESS_FORM_CITY: string = '[data-testid="address-form-city"]';
  private readonly TOGGLE_BOX_ADDRESS_FORM_ADDRESS_DETAILS: string = '[data-testid="address-form-address-details"]';
  private readonly TOGGLE_BOX_NO_PERMANENT_RESIDENCE_FORM: string = '[data-testid="no-permanent-residence-form"]';
  private readonly NO_PERMANENT_FORM_OFFICE: string = '[data-testid="no-permantent-residence-form-office"]';
  private readonly NO_PERMANENT_FORM_DELIVER_TO: string = '[data-testid="no-permantent-residence-form-deliver-to"]';
  private readonly NO_PERMANENT_FORM_STREET: string = '[data-testid="no-permantent-residence-form-street"]';
  private readonly NO_PERMANENT_FORM_STREET_NUMBER: string = '[data-testid="no-permantent-residence-form-street-number"]';
  private readonly NO_PERMANENT_FORM_POSTCODE: string = '[data-testid="no-permantent-residence-form-postcode"]';
  private readonly NO_PERMANENT_FORM_CITY: string = '[data-testid="no-permantent-residence-form-city"]';
  private readonly NO_PERMANENT_FORM_DETAIL: string = '[data-testid="no-permantent-residence-form-detail"]';
  private readonly INPUT_FIELD: string = '[data-testid="input"]';
  private readonly COMBOBOX_LIST_BOX: string = '[data-testid="combobox-listbox"]';
  private readonly COMBOBOX_LIST_OPTION: string = '[data-testid="combobox-option"]';

  public getTabPersonalData(): Cypress.Chainable {
    return cy.get(this.TAB_PERSONAL_DATA);
  }

  public getTabPersonalDataLimitedLicence(): Cypress.Chainable {
    return cy.get(this.TAB_PERSONAL_DATA_LIMITED_LICENCE);
  }

  public getPersonalDataForm(): Cypress.Chainable {
    return this.getTabPersonalData().find(this.PERSONAL_DATA_FORM);
  }

  public getUserDataForm(): Cypress.Chainable {
    return cy.get(this.USER_DATA_FORM);
  }

  public getPersonalDataFormTitle(): Cypress.Chainable {
    return this.getTabPersonalData().find(this.PERSONAL_DATA_FORM_TITLE);
  }

  public getPersonalDataFormTitleAllOptions(): Cypress.Chainable {
    return this.getPersonalDataFormTitle()
      .find(this.INPUT_FIELD)
      .click()
      .get(this.COMBOBOX_LIST_BOX)
      .should('be.visible')
      .find(this.COMBOBOX_LIST_OPTION);
  }

  public getPersonalDataFormFirstname(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.PERSONAL_DATA_FORM_FIRSTNAME))
      } else {
        return cy.get(this.PERSONAL_DATA_FORM_FIRSTNAME)
      }
    });
  }

  public getPersonalDataFormFirstnameInput(): Cypress.Chainable {
    return this.getPersonalDataFormFirstname().find(this.INPUT_FIELD);
  }

  public getPersonalDataFormLastname(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.PERSONAL_DATA_FORM_LASTNAME))
      } else {
        return cy.get(this.PERSONAL_DATA_FORM_LASTNAME)
      }
    });
  }

  public getPersonalDataFormLastnameInput(): Cypress.Chainable {
    return this.getPersonalDataFormLastname().find(this.INPUT_FIELD);
  }

  public getPersonalDataFormBirthName(): Cypress.Chainable {
    return cy.get(this.PERSONAL_DATA_FORM_BIRTH_NAME);
  }

  public getPersonalDataFormBirthInput(): Cypress.Chainable {
    return this.getPersonalDataFormBirthName().find(this.INPUT_FIELD);
  }

  public getPersonalDataFormBirthDate(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.PERSONAL_DATA_FORM_BIRTH_DATE))
      } else {
        return cy.get(this.PERSONAL_DATA_FORM_BIRTH_DATE)
      }
    })
  }

  public getPersonalDataFormBirthDateInput(): Cypress.Chainable {
    return this.getPersonalDataFormBirthDate().find(this.INPUT_FIELD);
  }

  public getPersonalDataFormBirthPlace(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.PERSONAL_DATA_FORM_BIRTH_PLACE))
      } else {
        return cy.get(this.PERSONAL_DATA_FORM_BIRTH_PLACE)
      }
    })
  }

  public getPersonalDataFormBirthPlaceInput(): Cypress.Chainable {
    return this.getPersonalDataFormBirthPlace().find(this.INPUT_FIELD);
  }

  public getPersonalDataFormNationality(): Cypress.Chainable {
    return cy.get(this.PERSONAL_DATA_FORM_NATIONALITY);
  }

  public getPersonalDataFormStreet(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.PERSONAL_DATA_FORM_ADDRESS_STREET))
      } else {
        return cy.get(this.PERSONAL_DATA_FORM_ADDRESS_STREET)
      }
    })
  }

  public getPersonalDataFormStreetInput(): Cypress.Chainable {
    return this.getPersonalDataFormStreet().find(this.INPUT_FIELD);
  }

  public getPersonalDataFormNationalityInput(): Cypress.Chainable {
    return this.getPersonalDataFormNationality().find(this.INPUT_FIELD);
  }

  public getPersonalDataFormNationalityAllOptions(): Cypress.Chainable {
    return this.getPersonalDataFormNationality()
      .find(this.INPUT_FIELD)
      .click()
      .get(this.COMBOBOX_LIST_BOX)
      //.should('be.visible')
      .find(this.COMBOBOX_LIST_OPTION);
  }

  public getToggleBox(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.TOGGLE_BOX))
      } else {
        return cy.get(this.TOGGLE_BOX)
      }
    })
  }

  public getToggleBoxAddressForm(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.TOGGLE_BOX_ADDRESS_FORM))
      } else {
        return cy.get(this.TOGGLE_BOX_ADDRESS_FORM)
      }
    })
  }

  public getAddressToggleBoxAddressButtonIcon(): Cypress.Chainable {
    return this.getToggleBox().find(this.TOGGLE_BOX_ADDRESS_ICON);
  }

  public getAddressToggleBoxNoPermanentResidenceButtonIcon(): Cypress.Chainable {
    return this.getToggleBox().find(this.TOGGLE_BOX_NO_PERMANENT_RESIDENCE_ICON);
  }

  public getToggleBoxAddressFormStreet(): Cypress.Chainable {
    return this.getToggleBoxAddressForm().find(this.TOGGLE_BOX_ADDRESS_FORM_CITY);
  }

  public getToggleBoxAddressFormStreetInput(): Cypress.Chainable {
    return this.getToggleBoxAddressFormStreet().find(this.INPUT_FIELD);
  }

  public getToggleBoxAddressFormStreetNumber(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.TOGGLE_BOX_ADDRESS_FORM_STREET_NUMBER))
      } else {
        return cy.get(this.TOGGLE_BOX_ADDRESS_FORM_STREET_NUMBER)
      }
    })
  }

  public getToggleBoxAddressFormStreetNumberInput(): Cypress.Chainable {
    return this.getToggleBoxAddressFormStreetNumber().find(this.INPUT_FIELD);
  }

  public getToggleBoxAddressFormPostCode(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.TOGGLE_BOX_ADDRESS_FORM_POST_CODE))
      } else {
        return cy.get(this.TOGGLE_BOX_ADDRESS_FORM_POST_CODE)
      }
    })
  }

  public getToggleBoxAddressFormPostCodeInput(): Cypress.Chainable {
    return this.getToggleBoxAddressFormPostCode().find(this.INPUT_FIELD);
  }

  public getToggleBoxAddressFormCity(): Cypress.Chainable {
    return cy.document().then((doc) => {
      const exists = doc.querySelectorAll(this.TAB_PERSONAL_DATA_LIMITED_LICENCE).length > 0
      if (exists) {
        return cy.wrap(null).then(()=>this.getTabPersonalDataLimitedLicence().find(this.TOGGLE_BOX_ADDRESS_FORM_CITY))
      } else {
        return cy.get(this.TOGGLE_BOX_ADDRESS_FORM_CITY)
      }
    })
  }
  
  public getToggleBoxAddressFormCityInput(): Cypress.Chainable {
    return this.getToggleBoxAddressFormCity().find(this.INPUT_FIELD);
  }

  public getToggleBoxAddressFormAddressDetails(): Cypress.Chainable {
    return this.getToggleBoxAddressForm().find(this.TOGGLE_BOX_ADDRESS_FORM_ADDRESS_DETAILS);
  }

  public getToggleBoxAddressFormAddressDetailsInput(): Cypress.Chainable {
    return this.getToggleBoxAddressFormAddressDetails().find(this.INPUT_FIELD);
  }

  public getNoPermanentResidenceForm(): Cypress.Chainable {
    return this.getToggleBox().find(this.TOGGLE_BOX_NO_PERMANENT_RESIDENCE_FORM);
  }

  public getNoPermanentFormOffice(): Cypress.Chainable {
    return this.getNoPermanentResidenceForm().find(this.NO_PERMANENT_FORM_OFFICE);
  }
  
  public getNoPermanentFormOfficeInput(): Cypress.Chainable {
    return this.getNoPermanentFormOffice().find(this.INPUT_FIELD);
  }

  public getNoPermanentFormDeliverTo(): Cypress.Chainable {
    return this.getNoPermanentResidenceForm().find(this.NO_PERMANENT_FORM_DELIVER_TO);
  }

  public getNoPermanentFormDeliverToInput(): Cypress.Chainable {
    return this.getNoPermanentFormDeliverTo().find(this.INPUT_FIELD);
  }

  public getNoPermanentFormStreet(): Cypress.Chainable {
    return this.getNoPermanentResidenceForm().find(this.NO_PERMANENT_FORM_STREET);
  }

  public getNoPermanentFormStreetInput(): Cypress.Chainable {
    return this.getNoPermanentFormStreet().find(this.INPUT_FIELD);
  }

  public getNoPermanentFormStreetNumber(): Cypress.Chainable {
    return this.getNoPermanentResidenceForm().find(this.NO_PERMANENT_FORM_STREET_NUMBER);
  }

  public getNoPermanentFormStreetNumberInput(): Cypress.Chainable {
    return this.getNoPermanentFormStreetNumber().find(this.INPUT_FIELD);
  }

  public getNoPermanentFormPostcode(): Cypress.Chainable {
    return this.getNoPermanentResidenceForm().find(this.NO_PERMANENT_FORM_POSTCODE);
  }

  public getNoPermanentFormPostcodeInput(): Cypress.Chainable {
    return this.getNoPermanentFormPostcode().find(this.INPUT_FIELD);
  }

  public getNoPermanentFormCity(): Cypress.Chainable {
    return this.getNoPermanentResidenceForm().find(this.NO_PERMANENT_FORM_CITY);
  }

  public getNoPermanentFormCityInput(): Cypress.Chainable {
    return this.getNoPermanentFormCity().find(this.INPUT_FIELD);
  }

  public getNoPermanentFormDetail(): Cypress.Chainable {
    return this.getNoPermanentResidenceForm().find(this.NO_PERMANENT_FORM_DETAIL);
  }

  public getNoPermanentFormDetailInput(): Cypress.Chainable {
    return this.getNoPermanentFormDetail().find(this.INPUT_FIELD);
  }
}
