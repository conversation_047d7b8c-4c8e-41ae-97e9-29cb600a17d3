export class StatisticsPo{
    private readonly STATISTICS_OPTIONS:string = '[data-testid="reportings-dashboard-template"]';
    private readonly STATISTICS_OPTIONS_SLIDER:string = '[data-testid="reportings-slider-button"]';
    private readonly STATISTICS_DASHBOARD:string = '[data-testid="statistics-dashboard"]';
    private readonly STATISTICS_TAXES_FRAME:string = '[data-testid="taxes-statistics-report-tile"]';
    private readonly STATISTICS_FRAME_TITLE:string = '[data-testid="statistics-report-tile-title"]';
    private readonly STATISTICS_FRAME_SUBTITLE:string = '[data-testid="statistics-report-tile-subtitle"]';
    private readonly STATISTICS_PRIMARY_LABEL:string = '[data-testid="statistics-report-tile-primary-statistic-label"]';
    private readonly STATISTICS_PRIMARY_DESCRIPTION:string = '[data-testid="statistics-report-tile-primary-statistic-note"]';
    private readonly STATISTICS_PRIMARY_VALUE:string = '[data-testid="statistics-report-tile-primary-statistic-value"]';
    private readonly STATISTICS_SECONDARY_LABEL:string = '[data-testid="statistics-report-tile-secondary-statistic-label"]';
    private readonly STATISTICS_SECONDARY_VALUE:string = '[data-testid="statistics-report-tile-secondary-statistic-value"]';
    private readonly STATISTICS_TERTIARY_LABEL:string = '[data-testid="statistics-report-tile-tertiary-statistic-label"]';
    private readonly STATISTICS_TERTIARY_VALUE:string = '[data-testid="statistics-report-tile-tertiary-statistic-value"]';
    private readonly STATISTICS_OVERVIEW_YEAR:string = '[data-testid="statistics-by-submission-type-table-year"]';
    private readonly STATISTICS_OVERVIEW_PREVIOUS_YEAR:string = '[data-testid="statistics-by-submission-type-table-previous-year"]';
    private readonly STATISTICS_LICENCES_FRAME:string = '[data-testid="regular-licenses-statistics-report-tile"]';
    private readonly STATISTICS_VACATION_LICENCES_FRAME:string = '[data-testid="vacation-licenses-statistics-report-tile"]';
    private readonly STATISTICS_LIMITED_LICENCES_FRAME:string = '[data-testid="limited-licenses-statistics-report-tile"]';
    private readonly STATISTICS_CERTIFICATIONS_FRAME:string = '[data-testid="certifications-statistics-report-tile"]';
    private readonly STATISTICS_CERTIFICATIONS_YEAR:string = '[data-testid="statistics-by-issuers-table-year"]';
    private readonly STATISTICS_CERTIFICATIONS_PREVIOUS_YEAR: string = '[data-testid="statistics-by-issuers-table-previous-year"]';
    private readonly STATISTICS_BAN_YEAR:string = '[data-testid="bans-statistics-year"]';
    private readonly STATISTICS_PREVIOUS_BAN_YEAR: string = '[data-testid="bans-statistics-previous-year"]';
    private readonly STATISTICS_FILTER_TYPE:string = '[data-testid="statistics-dashboard-filter-type"]';
    private readonly STATISTICS_FILTER_YEAR:string = '[data-testid="statistics-dashboard-filter-year"]';
    private readonly STATISTICS_FILTER_STATE:string = '[data-testid="statistics-dashboard-filter-federal-state"]';
    private readonly STATISTICS_FILTER_OFFICE:string = '[data-testid="statistics-dashboard-filter-office"]';
    private readonly STATISTICS_FILTER_EXAM_ISSUER:string = '[data-testid="statistics-dashboard-filter-certificate-issuer"]';
    private readonly STATISTICS_FILTER_LIST:string = '[data-testid="combobox-listbox"]';
    private readonly STATISTICS_FILTER_OPTIONS:string = '[data-testid="combobox-option"]';
    private readonly STATISTICS_INSPECTION_FRAME:string = '[data-testid="inspections-statistics-report-tile"]';
    private readonly STATISTICS_BAN_FRAME:string = '[data-testid="bans-statistics-report-tile"]';

    public getStatisticsOptions():Cypress.Chainable{
        return cy.get(this.STATISTICS_OPTIONS)
    }

    public getStatisticsOptionsSlider():Cypress.Chainable{
        return this.getStatisticsOptions().find(this.STATISTICS_OPTIONS_SLIDER)
    }

    public getStatisticsDashbboard():Cypress.Chainable{
        return this.getStatisticsOptions().find(this.STATISTICS_DASHBOARD)
    }

    public getStatisticsFilterType():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_FILTER_TYPE)
    }

    public getStatisticsFilterYear():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_FILTER_YEAR)
    }

    public getStatisticsFilterState():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_FILTER_STATE)
    }

    public getStatisticsFilterOffice():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_FILTER_OFFICE)
    }

    public getStatisticsFilterExamIssuer():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_FILTER_EXAM_ISSUER)
    }

    public getStatisticsFilterLists():Cypress.Chainable{
        return cy.get(this.STATISTICS_FILTER_LIST)
    }

    public getStatisticsFilterListItems():Cypress.Chainable{
        return this.getStatisticsFilterLists().find(this.STATISTICS_FILTER_OPTIONS)
    }

    public getTaxesOverview():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_TAXES_FRAME)
    }

    public getTaxesOverviewTitle():Cypress.Chainable{
        return this.getTaxesOverview().find(this.STATISTICS_FRAME_TITLE)
    }

    public getTaxesOverviewSubtitle():Cypress.Chainable{
        return this.getTaxesOverview().find(this.STATISTICS_FRAME_SUBTITLE)
    }

    public getOverviewPrimaryLabels():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_PRIMARY_LABEL)
    }

    public getOverviewPrimaryDescriptions():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_PRIMARY_DESCRIPTION)
    }

    public getOverviewPrimaryValues():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_PRIMARY_VALUE)
    }

    public getOverviewSecondaryLabels():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_SECONDARY_LABEL)
    }

    public getOverviewSecondaryValues():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_SECONDARY_VALUE)
    }

    public getOverviewTertiaryLabels():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_TERTIARY_LABEL)
    }

    public getOverviewTertiaryValues():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_TERTIARY_VALUE)
    }

    public getSelectedYearOverview():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_OVERVIEW_YEAR)
    }

    public getPreviousYearOverview():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_OVERVIEW_PREVIOUS_YEAR)
    }

    public getLicencesOverview():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_LICENCES_FRAME)
    }

    public getLicencesOverviewTitle():Cypress.Chainable{
        return this.getLicencesOverview().find(this.STATISTICS_FRAME_TITLE)
    }

    public getLicencesOverviewSubtitle():Cypress.Chainable{
        return this.getLicencesOverview().find(this.STATISTICS_FRAME_SUBTITLE)
    }

    public getVacationLicencesOverview():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_VACATION_LICENCES_FRAME)
    }

    public getVacationLicencesOverviewTitle():Cypress.Chainable{
        return this.getVacationLicencesOverview().find(this.STATISTICS_FRAME_TITLE)
    }

    public getVacationLicencesOverviewSubtitle():Cypress.Chainable{
        return this.getVacationLicencesOverview().find(this.STATISTICS_FRAME_SUBTITLE)
    }

    public getLimitedLicencesOverview():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_LIMITED_LICENCES_FRAME)
    }

    public getLimitedLicencesOverviewTitle():Cypress.Chainable{
        return this.getLimitedLicencesOverview().find(this.STATISTICS_FRAME_TITLE)
    }

    public getLimitedLicencesOverviewSubtitle():Cypress.Chainable{
        return this.getLimitedLicencesOverview().find(this.STATISTICS_FRAME_SUBTITLE)
    }

    public getCertificationsOverview():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_CERTIFICATIONS_FRAME)
    }

    public getCertificationLicencesOverviewTitle():Cypress.Chainable{
        return this.getCertificationsOverview().find(this.STATISTICS_FRAME_TITLE)
    }

    public getCertificationLicencesOverviewSubtitle():Cypress.Chainable{
        return this.getCertificationsOverview().find(this.STATISTICS_FRAME_SUBTITLE)
    }

    public getCertificationsSelectedYear():Cypress.Chainable{
        return this.getCertificationsOverview().find(this.STATISTICS_CERTIFICATIONS_YEAR)
    }

    public getCertificationsPreviousYearOverview():Cypress.Chainable{
        return this.getCertificationsOverview().find(this.STATISTICS_CERTIFICATIONS_PREVIOUS_YEAR)
    }

    public getBansSelectedYear():Cypress.Chainable{
        return this.getBanOverview().find(this.STATISTICS_BAN_YEAR)
    }

    public getBansPreviousYearOverview():Cypress.Chainable{
        return this.getBanOverview().find(this.STATISTICS_PREVIOUS_BAN_YEAR)
    }

    public getInspectionOverview():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_INSPECTION_FRAME)
    }

    public getInspectionOverviewTitle():Cypress.Chainable{
        return this.getInspectionOverview().find(this.STATISTICS_FRAME_TITLE)
    }

    public getInspectionOverviewSubtitle():Cypress.Chainable{
        return this.getInspectionOverview().find(this.STATISTICS_FRAME_SUBTITLE)
    }

    public getBanOverview():Cypress.Chainable{
        return this.getStatisticsDashbboard().find(this.STATISTICS_BAN_FRAME)
    }

    public getBanOverviewTitle():Cypress.Chainable{
        return this.getBanOverview().find(this.STATISTICS_FRAME_TITLE)
    }

    public getBanOverviewSubtitle():Cypress.Chainable{
        return this.getBanOverview().find(this.STATISTICS_FRAME_SUBTITLE)
    }
}