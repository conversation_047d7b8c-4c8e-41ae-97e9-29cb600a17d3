import { MoveJurisdictionPo } from "./move-jurisdiction-page.po";
export class ServiceOverviewPo {
    private readonly SERVICE_OVERVIEW_PAGE: string = '[data-testid="service-overview"]';
    private readonly SO_FISHING_LICENCE: string = '[data-testid="service-overview-license-card"]';
    private readonly SO_FISHING_LICENCE_REORDER: string = '[data-testid="service-overview-license-card-reorder"]';
    private readonly SO_FISHING_LICENCE_CREATE: string = '[data-testid="service-overview-license-card-create-license"]';
    private readonly SO_FISHING_LICENCE_VACATION: string = '[data-testid="service-overview-license-card-vacation-license"]';
    private readonly SO_FISHING_LICENCE_OTHER: string = '[data-testid="service-overview-license-card-limited-license"]';
    private readonly SO_TAX_PAYMENT: string = '[data-testid="service-overview-tax-card"]';
    private readonly SO_TAX_PAYMENT_DIGITIZE_LICENCE: string = '[data-testid="service-overview-license-card-digitize-license"]';
    private readonly SO_TAX_PAYMENT_VACATION_LICENCE: string = '[data-testid="service-overview-license-card-vacation-license"]';
    private readonly SO_DOCUMENTS: string = '[data-testid="service-overview-document-card"]';
    private readonly SO_DOCUMENTS_BUTTON: string = '[data-testid="service-overview-document-card-inspection-link"]';
    private readonly USER_PROFILE_HEADER:string = '[data-testid="profile-header-component"]';
    private readonly SO_MOVE_JURISDICTION: string = '[data-testid="service-overview-jurisdiction-notice-move-button"]';
    private readonly SO_ADD_BUTTON: string = '[data-testid="service-card-create-button"]';
    private readonly SO_BAN_HEADER: string = '[data-testid="service-overview-ban-notice"]';
    private readonly SO_BAN_DESC: string = '.w-fit.shrink.px-6.py-2.leading-tight'
    private readonly SO_BAN_EDIT_BUTTON: string = '[data-testid="service-overview-ban-notice-edit"]';
    private readonly SO_UNBAN_BUTTON: string = '[data-testid="service-overview-ban-notice-unban"]';
    private readonly SO_UNBAN_DIALOG: string = '[data-test-id="dialog"]';
    private readonly SO_UNBAN_CONFIRM_BUTTON: string = '[data-testid="unban-dialog-confirm-button"]';

    private moveJurisdictionComponent: MoveJurisdictionPo = new MoveJurisdictionPo()

    public getMoveJurisdictionComponent(): MoveJurisdictionPo {
        return this.moveJurisdictionComponent;
    }

    public getServiceOverviewPage(): Cypress.Chainable {
        return cy.get(this.SERVICE_OVERVIEW_PAGE);
    }

    public getUserProfileHeader(): Cypress.Chainable<string> {
        return cy.get(this.USER_PROFILE_HEADER);
    }

    public getFishingLicenceCard(): Cypress.Chainable {
        return this.getServiceOverviewPage().find(this.SO_FISHING_LICENCE);
    }

    public getFishingLicenceReorderCard(): Cypress.Chainable {
        return this.getServiceOverviewPage().find(this.SO_FISHING_LICENCE_REORDER);
    }

    public getFishingLicenceButton(): Cypress.Chainable {
        return this.getFishingLicenceCard().find(this.SO_ADD_BUTTON);
    }

    public getFishingLicenceCreate(): Cypress.Chainable {
        return cy.get(this.SO_FISHING_LICENCE_CREATE);
    }

    public getFishingLicenceVacation(): Cypress.Chainable {
        return cy.get(this.SO_FISHING_LICENCE_VACATION);
    }

    public getFishingLicenceOther(): Cypress.Chainable {
        return cy.get(this.SO_FISHING_LICENCE_OTHER);
    }

    public getTaxPaymentCard(): Cypress.Chainable {
        return this.getServiceOverviewPage().find(this.SO_TAX_PAYMENT);
    }

    public getTaxPaymentButton(): Cypress.Chainable {
        return this.getTaxPaymentCard().find(this.SO_ADD_BUTTON);
    }

    public getDigitizeLicenceButton(): Cypress.Chainable {
        return cy.get(this.SO_TAX_PAYMENT_DIGITIZE_LICENCE);
    }

    public getVacationLicenceButton(): Cypress.Chainable {
        return cy.get(this.SO_TAX_PAYMENT_VACATION_LICENCE);
    }

    public getDocumentsCard(): Cypress.Chainable {
        return this.getServiceOverviewPage().find(this.SO_DOCUMENTS);
    }

    public getDocumentsButton(): Cypress.Chainable {
        return this.getDocumentsCard().find(this.SO_DOCUMENTS_BUTTON);
    }

    public getMoveJurisdiction(): Cypress.Chainable {
        return this.getServiceOverviewPage().find(this.SO_MOVE_JURISDICTION);
    }

    public getBanHeader(): Cypress.Chainable{
        return this.getServiceOverviewPage().find(this.SO_BAN_HEADER)
    }

    public getBanEditButton():Cypress.Chainable{
        return this.getBanHeader().find(this.SO_BAN_EDIT_BUTTON)
    }

    public getUnbanButton():Cypress.Chainable{
        return this.getBanHeader().find(this.SO_UNBAN_BUTTON)
    }

    public getBanDescription():Cypress.Chainable{
        return this.getBanHeader().find(this.SO_BAN_DESC)
    }

    public getUnbanDialog():Cypress.Chainable{
        return cy.get(this.SO_UNBAN_DIALOG)
    }

    public getUnbanConfirmationbutton():Cypress.Chainable{
        return this.getUnbanDialog().find(this.SO_UNBAN_CONFIRM_BUTTON)
    }
}