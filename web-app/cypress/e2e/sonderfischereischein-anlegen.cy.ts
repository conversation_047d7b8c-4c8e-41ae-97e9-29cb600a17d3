import * as allure from "allure-js-commons";
import {ExternalLoginPagePo} from "../support/pageObjects/external-login-page.po";
import {LoginPagePo} from "../support/pageObjects/login-page.po";
import {HomePagePo} from "../support/pageObjects/home-page.po";
import {SearchResultsPo} from "../support/pageObjects/search-results.po";
import {ConsentDigitizePo} from "../support/pageObjects/consent-digitize-page.po";
import {DigitizeLicensePo} from "../support/pageObjects/digitize-licence/digitize-license-page.po";
import { HeaderComponentCo } from "../support/sharedComponents/header-component.co";
/*import {ServiceOverviewPo} from "../support/pageObjects/service-overview.po";
import { ExaminationPagePo } from "../support/pageObjects/examination-page.po";
import { LicencePaymentsPo } from "../support/pageObjects/licence-payment-page.po";*/

describe('',()=>{
  const externalLoginPage: ExternalLoginPagePo = new ExternalLoginPagePo();
  const loginPage: LoginPagePo = new LoginPagePo();
  const homePage: HomePagePo = new HomePagePo();
  const searchResultsPage: SearchResultsPo = new SearchResultsPo();
  const consentDigitizePage: ConsentDigitizePo = new ConsentDigitizePo();
  const digitizeLicencePage: DigitizeLicensePo = new DigitizeLicensePo();
  const headerComponent: HeaderComponentCo = new HeaderComponentCo();
  //TODO: adapt for future scenarios
  /*const licencePayment: LicencePaymentsPo = new LicencePaymentsPo();
  const serviceOverviewPage: ServiceOverviewPo = new ServiceOverviewPo();
  const examinationPage: ExaminationPagePo = new ExaminationPagePo();*/
  let testUsers: any;
  let testData: any;
  let testCitizen: any;
  const currentDate = new Date().toISOString().split('T')[0];
  const system = Cypress.env('system');
  
  before(() => {
    cy.clearAllCookies();
    allure.feature('Prüfung-und-Fischereischein-anlegen');      
    allure.owner('Hegedus Kinga');
    cy.fixture(`${system}/suche/testuser.json`).then((users) => {
        cy.log('reading the test users file');
        testUsers = users;
    });
  
    cy.fixture(`${system}/suche/testdata.json`).then((data) => {    
        cy.log('reading the test data file');
        testData = data;
        cy.log(JSON.stringify(testData));
    });
  
    cy.fixture(`${system}/suche/testcitizen.json`).then((citizen) => {      
        cy.log('reading the test users file');      
        testCitizen = citizen;        
    });
    })

    it('Einen neuen digitalen Sonderfischereischein-Eintrag erstellen: vom Dritten erstellt, mit Verwaltungsgebühr, mit Abgabe für dieses Jahr mit 1 Jahr Gültigkeit, Überweisung,',()=>{
        allure.story('Einen neuen digitalen Sonderfischereischein-Eintrag erstellen: vom Dritten erstellt, mit Verwaltungsgebühr und Abgabe mit 1 Jahr Gültigkeit, Überweisung,');
        allure.severity('normal');
    
        allure.step('Als NRW Mitarbeiter anmelden', () => {
            allure.parameter("username", testUsers.user7.username);
            allure.parameter("userPassword", '');
            cy.visit('/');
            loginPage.getLoginButton().click();
            externalLoginPage.getLoginPageUsernameInput().type(testUsers.user7.username);
            externalLoginPage.getLoginPagePasswordInput().type(testUsers.user7.password, {log: false});
            externalLoginPage.getLoginPageSubmitButton().click();
        })
    
        allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
            homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
            searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
            searchResultsPage.getSearchResultsPageCreationMenu().click();
            searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
        })
    
        allure.step('Akzeptiere GDPR', () => {
            searchResultsPage.getSearchResultsPageCreationMenuSpecial().should('be.visible').click();
            consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
            consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
            consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
        });
        
        allure.step('Fülle das Formular "Personen Daten" aus',()=>{
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBox().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressForm().should('be.visible')
            digitizeLicencePage.getEditFooterComponent().getFooterConfirmBox().should('be.visible').click()
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
        })

        allure.step('Überprüfe das Formular "Nachweis"',()=>{
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCard().should('be.visible')
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCard().contains("Nachweis über Schwerbehinderung")
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCard().contains("wurde vorgelegt, geprüft und entsprach den rechtlichen Anforderungen")
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCard().contains("Daraus resultierende Befristung des Fischereischeins mit Begleitung:")
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablement().contains("Gültig ab sofort und lebenslang")
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckboxInput().should('have.class','ng-invalid')
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().should('be.disabled')
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckbox().click()
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().click({force: true})
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckboxInput().should('have.class','ng-valid')
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().should('have.class','ng-valid')
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckbox().click()
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckboxInput().should('have.class','ng-invalid')
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().should('be.disabled')
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckbox().click()
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckboxInput().should('have.class','ng-valid')
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().should('have.class','ng-invalid')
            //TODO: add asserts for error messages once available
        })

        allure.step('Fülle das Formular "Nachweis" aus',()=>{
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().click({force:true})
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckboxInput().should('have.class','ng-valid')
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().should('have.class','ng-valid')
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
        })
        
        allure.step('Überprüfe das Formular "Gebühren und Abgaben"',()=>{
            digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
            digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
            digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible').click()
            digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains('12 €')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtonsInput().eq(0).should('have.class','ng-valid')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearFrom().eq(0).should('be.visible')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearTo().eq(0).should('be.visible')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearDown().eq(0).should('be.disabled')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(0).should('not.be.disabled')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearDown().eq(1).should('be.disabled')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(1).should('be.disabled')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).should('be.visible').contains('36 €')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtonsInput().eq(1).should('have.class','ng-valid')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearFrom().eq(1).should('be.visible')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearTo().eq(1).should('be.visible')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearDown().eq(2).should('be.disabled')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(2).should('be.disabled')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearDown().eq(3).should('be.disabled')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(3).should('be.disabled')
            digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
        })

        allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
            digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('31,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('12,00 €')
            digitizeLicencePage.getTaxesComponent().getPaymentMethodTransfer().should('not.be.checked').click()
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
        })

        allure.step('Überprüfe das Formular "Bewilligungsbescheid"', ()=>{
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalPage().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalForm().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormID().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormName().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormEmail().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormPhone().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCreationDate().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormFileNumber().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCashRegister().should('be.visible').click()
            digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible')
        })

        allure.step('Fülle das Formular "Bewilligungsbescheid" aus', ()=>{
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalPage().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalForm().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormID().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormName().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormEmail().should('be.visible').type("<EMAIL>")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormPhone().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCreationDateInput().should('be.visible').type(currentDate)
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormFileNumber().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCashRegister().should('be.visible').type("test")
            digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().click()
        })
        
        allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
            cy.wait(2000)
            digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
            digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Besonderer Fischereischein')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Bewilligungsbescheid')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('Fischereiabgabe')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Drucken')
            //TODO: replace with confirmation steps once available
            /*digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(3).should('be.visible').contains('Senden').click()
            digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirm().should('be.visible').click()
            digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')*/
        })
    
        allure.step('Als NRW Mitarbeiter abmelden', () => {
            headerComponent.getHeaderDropdownMenu().should('be.visible').click()
            headerComponent.getHeaderLogoutButton().should('be.visible').click() 
        })
    })

    it('Einen neuen digitalen Sonderfischereischein-Eintrag erstellen: vom Nutzer erstellt, mit Verwaltungsgebühr, mit Abgabe für nächstes Jahr mit 5 Jahr Gültigkeit, Überweisung,',()=>{
        allure.story('Einen neuen digitalen Sonderfischereischein-Eintrag erstellen: vom Dritten erstellt, mit Verwaltungsgebühr und Abgabe mit 1 Jahr Gültigkeit, Überweisung,');
        allure.severity('normal');
    
        allure.step('Als NRW Mitarbeiter anmelden', () => {
            allure.parameter("username", testUsers.user7.username);
            allure.parameter("userPassword", '');
            cy.visit('/');
            loginPage.getLoginButton().click();
            externalLoginPage.getLoginPageUsernameInput().type(testUsers.user7.username);
            externalLoginPage.getLoginPagePasswordInput().type(testUsers.user7.password, {log: false});
            externalLoginPage.getLoginPageSubmitButton().click();
        })
    
        allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
            homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
            searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
            searchResultsPage.getSearchResultsPageCreationMenu().click();
            searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
        })
    
        allure.step('Akzeptiere GDPR', () => {
            searchResultsPage.getSearchResultsPageCreationMenuSpecial().should('be.visible').click();
            consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
            consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
            consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
        });
        
        allure.step('Fülle das Formular "Personen Daten" aus',()=>{
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBox().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressForm().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
        })

        allure.step('Fülle das Formular "Nachweis" aus',()=>{
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().click({force:true})
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckbox().click()
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
        })

        allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
            digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
            digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
            digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible').click()
            digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(1).click()
            digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(2).click()
            digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
            digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('55,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('36,00 €')
            digitizeLicencePage.getTaxesComponent().getPaymentMethodTransfer().should('not.be.checked').click()
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
        })

        allure.step('Überprüfe das Formular "Bewilligungsbescheid"', ()=>{
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalPage().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalForm().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormID().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormName().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormEmail().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormPhone().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCreationDate().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormFileNumber().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCashRegister().should('be.visible').click()
            digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible')
        })

        allure.step('Fülle das Formular "Bewilligungsbescheid" aus', ()=>{
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalPage().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalForm().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormID().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormName().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormEmail().should('be.visible').type("<EMAIL>")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormPhone().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCreationDateInput().should('be.visible').type(currentDate)
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormFileNumber().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCashRegister().should('be.visible').type("test")
            digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().click()
        })
        
        allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
            cy.wait(2000)
            digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
            digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Besonderer Fischereischein')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Bewilligungsbescheid')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('Fischereiabgabe')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Drucken')
            //TODO: replace with confirmation steps once available
            /*digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(3).should('be.visible').contains('Senden').click()
            digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirm().should('be.visible').click()
            digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')*/
        })
    
        allure.step('Als NRW Mitarbeiter abmelden', () => {
            headerComponent.getHeaderDropdownMenu().should('be.visible').click()
            headerComponent.getHeaderLogoutButton().should('be.visible').click() 
        })
    })

    it('Einen neuen digitalen Sonderfischereischein-Eintrag erstellen: vom Nutzer erstellt, mit Verwaltungsgebühr ohne Abgabe, Überweisung,',()=>{
        allure.story('Einen neuen digitalen Sonderfischereischein-Eintrag erstellen: vom Dritten erstellt, mit Verwaltungsgebühr und Abgabe mit 1 Jahr Gültigkeit, Überweisung,');
        allure.severity('normal');
    
        allure.step('Als NRW Mitarbeiter anmelden', () => {
            allure.parameter("username", testUsers.user7.username);
            allure.parameter("userPassword", '');
            cy.visit('/');
            loginPage.getLoginButton().click();
            externalLoginPage.getLoginPageUsernameInput().type(testUsers.user7.username);
            externalLoginPage.getLoginPagePasswordInput().type(testUsers.user7.password, {log: false});
            externalLoginPage.getLoginPageSubmitButton().click();
        })
    
        allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
            homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
            searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
            searchResultsPage.getSearchResultsPageCreationMenu().click();
            searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
        })
    
        allure.step('Akzeptiere GDPR', () => {
            searchResultsPage.getSearchResultsPageCreationMenuSpecial().should('be.visible').click();
            consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
            consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
            consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
        });
        
        allure.step('Fülle das Formular "Personen Daten" aus',()=>{
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBox().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressForm().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
        })

        allure.step('Fülle das Formular "Nachweis" aus',()=>{
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().click({force:true})
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckbox().click()
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
        })

        allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
            digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
            digitizeLicencePage.getTaxesComponent().getPaymentMethodTransfer().should('not.be.checked').click()
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
        })

        allure.step('Überprüfe das Formular "Bewilligungsbescheid"', ()=>{
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalPage().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalForm().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormID().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormName().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormEmail().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormPhone().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCreationDate().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormFileNumber().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCashRegister().should('be.visible').click()
            digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible')
        })

        allure.step('Fülle das Formular "Bewilligungsbescheid" aus', ()=>{
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalPage().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalForm().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormID().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormName().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormEmail().should('be.visible').type("<EMAIL>")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormPhone().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCreationDateInput().should('be.visible').type(currentDate)
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormFileNumber().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCashRegister().should('be.visible').type("test")
            digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().click()
        })
        
        allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
            cy.wait(2000)
            digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
            digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Besonderer Fischereischein')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Bewilligungsbescheid')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
            //TODO: replace with confirmation steps once available
            /*digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
            digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirm().should('be.visible').click()
            digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')*/
        })
    
        allure.step('Als NRW Mitarbeiter abmelden', () => {
            headerComponent.getHeaderDropdownMenu().should('be.visible').click()
            headerComponent.getHeaderLogoutButton().should('be.visible').click() 
        })
    })

   /*it('Eine neue Fischereischein Prüfung und Sonderfischereischein anlegen: vom Nutzer erstellt, mit Verwaltungsgebühr ohne Abgabe, Überweisung,',()=>{
        allure.story('Einen neuen digitalen Sonderfischereischein-Eintrag erstellen: vom Dritten erstellt, mit Verwaltungsgebühr und Abgabe mit 1 Jahr Gültigkeit, Überweisung,');
        allure.severity('normal');

        allure.step('Als Prüfer anmelden', () => {
            allure.parameter("username", testUsers.user2.username);
            allure.parameter("userPassword", '');
            cy.visit('/');
            loginPage.getLoginButton().click();
            externalLoginPage.getLoginPageUsernameInput().type(testUsers.user2.username);
            externalLoginPage.getLoginPagePasswordInput().type(testUsers.user2.password, {log: false});
            externalLoginPage.getLoginPageSubmitButton().click();
        });
        
        allure.step('Befüllen des Prüfungsformulars',()=>{
            examinationPage.getExaminationPage().should('be.visible')      
            examinationPage.getExamPageFormFirstname().should('be.visible').type(testCitizen.buerger1.person.firstname)
            examinationPage.getExamPageFormLastname().should('be.visible').type(testCitizen.buerger1.person.lastname)
            examinationPage.getExamPageFormBirthdate().should('be.visible').type(testCitizen.buerger1.person.birthdate)        
            examinationPage.getExamPageFormBirthplace().should('be.visible').type(testCitizen.buerger1.person.birthplace)
            examinationPage.getExamPageFormPassDate().should('be.visible').type("2009-01-01")
            examinationPage.getExamPageContinueButton().should('be.visible').click()
            examinationPage.getExamSubmitButton().should('be.visible').click()
            examinationPage.getSuccessPage().should('be.visible').contains("Erfolgreich angelegt!")
            examinationPage.getSuccessPage().should('be.visible').contains("Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.")
            examinationPage.getSuccessEntries().invoke('text')
                .then((text) => {
                    const match = text.match(/ZF\d{2}-\d{4}-\d{4}-\d{4}/);
                    cy.wrap(match[0]).as('accessCode')
                });
                cy.get('@accessCode').then((accessCode) => {
                    cy.log(`Extracted Code: ${accessCode}`);
                    examinationPage.getSuccessEntries().should('be.visible').contains(accessCode)
                });
        }) 
          
        allure.step('Als Prüfer abmelden', () => {
            headerComponent.getHeaderDropdownMenu().should('be.visible').click()
            headerComponent.getHeaderLogoutButton().should('be.visible').click() 
        });
        
        allure.step('Als Mitarbeiter der oberen Fischereibehörde NRW anmelden', () => {
          allure.parameter("username", testUsers.user7.username);
          allure.parameter("userPassword", '');
          cy.visit('/');
          loginPage.getLoginButton().click();
          externalLoginPage.getLoginPageUsernameInput().type(testUsers.user7.username);
          externalLoginPage.getLoginPagePasswordInput().type(testUsers.user7.password, {log: false});
          externalLoginPage.getLoginPageSubmitButton().click();
        });
              
        allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
            cy.get('@accessCode').then((accessCode) => {
            searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
                .type(accessCode);
            });
            homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
            searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
            serviceOverviewPage.getUserProfileHeader().should('be.visible')
                .contains(`${testCitizen.buerger1.person.firstname} ${testCitizen.buerger1.person.lastname}`)
        })
          
        allure.step('Urlauberfischereischein anlegen',()=>{
            serviceOverviewPage.getFishingLicenceButton().should('be.visible').click()
            serviceOverviewPage.getFishingLicenceCard().should('be.visible')
            serviceOverviewPage.getFishingLicenceVacation().should('be.visible').click()
            
            //TODO:update after implementation is ready
            //serviceOverviewPage.getFishingLicenceOther().should('be.visible')
            //serviceOverviewPage.getFishingLicenceCreate().should('be.visible').click()
        })
    
        allure.step('Akzeptiere GDPR', () => {
          consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
          consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
          consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
        });
        
        allure.step('Fülle das Formular "Personen Daten" aus',()=>{
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().contains(testCitizen.buerger4.person.firstname)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().contains(testCitizen.buerger4.person.lastname)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().contains(testCitizen.buerger4.person.birthdate)
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().contains(testCitizen.buerger4.person.birthplace)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBox().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressForm().should('be.visible')
            digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().contains(testCitizen.buerger4.person.address.street)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().contains(testCitizen.buerger4.person.address.streetNumber)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().contains(testCitizen.buerger4.person.address.postcode)
            digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().contains(testCitizen.buerger4.person.address.city)
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
        })

        allure.step('Fülle das Formular "Nachweis" aus',()=>{
            digitizeLicencePage.getQualificationDataComponent().getPermanentDisablementRadio().click({force:true})
            digitizeLicencePage.getQualificationDataComponent().getDisablementCertificationCheckbox().click()
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
        })

        allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
            digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('19,00 €')
            digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
            digitizeLicencePage.getTaxesComponent().getPaymentMethodTransfer().should('not.be.checked').click()
            digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
        })

        allure.step('Überprüfe das Formular "Bewilligungsbescheid"', ()=>{
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalPage().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalForm().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormID().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormName().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormEmail().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormPhone().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCreationDate().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormFileNumber().should('be.visible').click()
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCashRegister().should('be.visible').click()
            digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible')
        })

        allure.step('Fülle das Formular "Bewilligungsbescheid" aus', ()=>{
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalPage().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getApprovalForm().should('be.visible')
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormID().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormName().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormEmail().should('be.visible').type("<EMAIL>")
            digitizeLicencePage.getSpecialLicenceComponent().getEmployeeFormPhone().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCreationDateInput().should('be.visible').type(currentDate)
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormFileNumber().should('be.visible').type("test")
            digitizeLicencePage.getSpecialLicenceComponent().getDataFormCashRegister().should('be.visible').type("test")
            digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().click()
        })
        
        allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
            cy.wait(2000)
            digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
            digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Besonderer Fischereischein')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Bewilligungsbescheid')
            digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
            
            //TODO: replace with confirmation steps once available
            digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
            digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
            digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirm().should('be.visible').click()
            digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
        })
    
        allure.step('Als Mitarbeiter der oberen NRW abmelden', () => {
            headerComponent.getHeaderDropdownMenu().should('be.visible').click()
            headerComponent.getHeaderLogoutButton().should('be.visible').click() 
        })
    })*/
})