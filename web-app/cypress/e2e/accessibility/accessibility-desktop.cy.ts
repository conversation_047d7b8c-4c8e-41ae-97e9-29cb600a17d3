import * as allure from 'allure-js-commons';
import { ExternalLoginPagePo } from '@/support/pageObjects/external-login-page.po';
import { LoginPagePo } from '@/support/pageObjects/login-page.po';
import { HomePagePo } from '@/support/pageObjects/home-page.po';
import { SearchResultsPo } from '@/support/pageObjects/search-results.po';
import { ErrorPagePo } from '@/support/pageObjects/error-page.po';
import { ConsentDigitizePo } from '@/support/pageObjects/consent-digitize-page.po';
import { DigitizeLicensePo } from '@/support/pageObjects/digitize-licence/digitize-license-page.po';
import { injectAxe } from 'cypress-axe';
import { HeaderComponentCo } from '@/support/sharedComponents/header-component.co';

describe('Accessibility on Desktop', {retries: 0}, () => {
  const externalLoginPage: ExternalLoginPagePo = new ExternalLoginPagePo();
  const loginPage: LoginPagePo = new LoginPagePo();
  const homePage: HomePagePo = new HomePagePo();
  const searchResultsPage: SearchResultsPo = new SearchResultsPo();
  const errorPage: ErrorPagePo = new ErrorPagePo();
  const consentDigitizePage: ConsentDigitizePo = new ConsentDigitizePo();
  const digitizeLicencePage: DigitizeLicensePo = new DigitizeLicensePo();
  const headerComponent: HeaderComponentCo = new HeaderComponentCo();
  let testUsers: any;
  let testData: any;
  let testCitizen: any;
  const system = Cypress.env('system');

  before(() => {
    cy.clearAllCookies();
    allure.feature('Accessibility Tests');
    allure.owner('Hegedus Kinga');

    cy.fixture(`${system}/suche/testuser.json`).then((users) => {
      cy.log('reading the test users file');
      testUsers = users;
      cy.log(`run Tests using test users on: ${system}`);
    });

    cy.fixture(`${system}/suche/testdata.json`).then((data) => {
      cy.log('reading the test data file');
      testData = data;
      cy.log(`run Tests using test data on: ${system}`);
      cy.log(JSON.stringify(testData));
    });

    cy.fixture(`${system}/suche/testcitizen.json`).then((citizen) => {
      cy.log('reading the test data file');
      testCitizen = citizen;
      cy.log(`run Tests using test citizen on: ${system}`);
    })
  });

  it('Überprüfung der Anmeldungsseite', () => {
    allure.story('Überprüfung der Anmeldungsseite');
    allure.severity('normal');
    allure.step('Öffne die Startseite und prüfe die Anmeldungsseite', () => {
      allure.parameter("url", testData.url);
      cy.visit('/');
      loginPage.getLoginPage().should('be.visible');
      cy.injectAxe();
      cy.checkA11y(undefined, undefined, (violations) => {
        cy.log(`${violations.length} accessibility violation(s) detected`);
        violations.forEach((violation) => {
          cy.log(`Violation: ${violation.id}`);
          cy.log(`Impact: ${violation.impact}`);
          cy.log(`Description: ${violation.description}`);
          cy.log(`Help: ${violation.helpUrl}`);
        });
      });
    });
  });

  it('Überprüfung der Startseite', () => {
    allure.story('Überprüfung der Startseite');
    allure.severity('normal');
    allure.step('Überprüfung der Home Page', () => {
      allure.parameter("username", testUsers.user6.username);
      loginPage.getLoginButton().should('be.visible').click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
      homePage.getHomePage().should('be.visible');
      cy.injectAxe();
      cy.checkA11y(undefined, undefined, (violations) => {
        cy.log(`${violations.length} accessibility violation(s) detected`);
        violations.forEach((violation) => {
          cy.log(`Violation: ${violation.id}`);
          cy.log(`Impact: ${violation.impact}`);
          cy.log(`Description: ${violation.description}`);
          cy.log(`Help: ${violation.helpUrl}`);
        });
      });
    });
  });

  it('Überprüfung der Suchergebnisseite', () => {
    allure.story('Überprüfung der Suchergebnisseite');
    allure.severity('normal');
    allure.step('Führe eine Suche und überprüft die Suchergebnisseite', () => {
      allure.parameter("lastname", testCitizen.buerger2.person.lastname);
      allure.parameter("firstname", testCitizen.buerger2.person.firstname);
      homePage.getHomePage().then((result) => {
        result.find(homePage.getSearchBarInputComponent().getSearchbarComponentInput()
          .type(testCitizen.buerger2.person.lastname));
        result.find(homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click());
      });
      searchResultsPage.getSearchResultsPage().should('be.visible');
      cy.injectAxe();
      cy.checkA11y(undefined, undefined, (violations) => {
        cy.log(`${violations.length} accessibility violation(s) detected`);
        violations.forEach((violation) => {
          cy.log(`Violation: ${violation.id}`);
          cy.log(`Impact: ${violation.impact}`);
          cy.log(`Description: ${violation.description}`);
          cy.log(`Help: ${violation.helpUrl}`);
        });
      });
    });
  });

  it('Überprüfung der Suchergebnisseite ohne Ergebnisse', () => {
    allure.story('Überprüfung der Suchergebnisseite ohne Ergebnisse');
    allure.severity('normal');
    allure.step('Führe eine leere Suche und überprüft die Suchergebnisseite', () => {
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentClearButton()
        .should('be.visible').click();
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentSearchButton()
        .should('be.visible').click();
      searchResultsPage.getSearchResultsPage().then(resultsPage => {
        resultsPage.find(searchResultsPage.getSearchResultsComponent().getSearchResultsComponent()
          .should('not.exist'));
      });
      cy.injectAxe();
      cy.checkA11y(undefined, undefined, (violations) => {
        cy.log(`${violations.length} accessibility violation(s) detected`);
        violations.forEach((violation) => {
          cy.log(`Violation: ${violation.id}`);
          cy.log(`Impact: ${violation.impact}`);
          cy.log(`Description: ${violation.description}`);
          cy.log(`Help: ${violation.helpUrl}`);
        });
      });
    });
  });

  it('Überprüfung der Seite "Digitaliseren DSGVO"', () => {
    allure.story('Überprüfung der Seite "Digitaliseren DSGVO"');
    allure.severity('normal');
    allure.step('Öffne und prüfe die Seite "Digitaliseren DSGVO"', () => {
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getConsentDigitizePage().should('be.visible');
      cy.injectAxe();
      cy.checkA11y(undefined, undefined, (violations) => {
        cy.log(`${violations.length} accessibility violation(s) detected`);
        violations.forEach((violation) => {
          cy.log(`Violation: ${violation.id}`);
          cy.log(`Impact: ${violation.impact}`);
          cy.log(`Description: ${violation.description}`);
          cy.log(`Help: ${violation.helpUrl}`);
        });
      });
    });
  });

  it('Überprüfung der Seite "Lizenz Digitalisieren"', () => {
    allure.story('Überprüfung der Seite "Lizenz Digitalisieren"');
    allure.severity('normal');
    allure.step('Öffne und prüfe die Seite "Lizenz Digitaliseren"', () => {
      consentDigitizePage.getGdprCheckbox().click();
      consentDigitizePage.getSelfDisclosureCheckbox().click();
      consentDigitizePage.getConsentPageContinueButton().click();
      cy.injectAxe();
      cy.checkA11y(undefined, undefined, (violations) => {
        cy.log(`${violations.length} accessibility violation(s) detected`);
        violations.forEach((violation) => {
          cy.log(`Violation: ${violation.id}`);
          cy.log(`Impact: ${violation.impact}`);
          cy.log(`Description: ${violation.description}`);
          cy.log(`Help: ${violation.helpUrl}`);
        });
      });
    });
  });

  it('Überprüfe die Combobox im Tab "Personendaten" auf der Seite "Lizenz Digitalisieren"', () => {
    allure.step('Überprüfe die Combobox im Tab "Personendaten" auf der Seite "Lizenz Digitalisieren"', () => {
      injectAxe();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormTitle().should('be.visible')
        .click().checkA11y(undefined, undefined, (violations) => {
          cy.log(`${violations.length} accessibility violation(s) detected`);
          violations.forEach((violation) => {
            cy.log(`Violation: ${violation.id}`);
            cy.log(`Impact: ${violation.impact}`);
            cy.log(`Description: ${violation.description}`);
            cy.log(`Help: ${violation.helpUrl}`);
          });
        });
    });
  });

  it('Überprüfe den Tab "Bezahlte Abgaben" auf der Seite "Lizenz Digitalisieren"', () => {
    allure.step('Überprüfe den Tab "Bezahlte Abgaben" auf der Seite "Lizenz Digitalisieren"', () => {
      injectAxe();
      headerComponent.getAllTabTitle().eq(1).click().checkA11y(undefined, undefined, (violations) => {
        cy.log(`${violations.length} accessibility violation(s) detected`);
        violations.forEach((violation) => {
          cy.log(`Violation: ${violation.id}`);
          cy.log(`Impact: ${violation.impact}`);
          cy.log(`Description: ${violation.description}`);
          cy.log(`Help: ${violation.helpUrl}`);
        });
      });
    });
  });

  it('überprüft der Tab "Gebühren & Abgaben" auf der Lizenz Digitalisieren Seite', () => {
    allure.step('überprüft der Tab "Gebühren & Abgaben" auf der Lizenz Digitalisieren Seite', () => {
      injectAxe();
      headerComponent.getAllTabTitle().eq(2).click().checkA11y(undefined, undefined, (violations) => {
        cy.log(`${violations.length} accessibility violation(s) detected`);
        violations.forEach((violation) => {
          cy.log(`Violation: ${violation.id}`);
          cy.log(`Impact: ${violation.impact}`);
          cy.log(`Description: ${violation.description}`);
          cy.log(`Help: ${violation.helpUrl}`);
        });
      });
    });
  });

  it('Überprüft den Tab "Bezahlte Abgaben" auf der Seite "Lizenz Digitalisieren"', () => {
    allure.step('Überprüft den Tab "Bezahlte Abgaben" auf der Seite "Lizenz Digitalisieren"', () => {
      injectAxe();
      headerComponent.getAllTabTitle().eq(3).click().checkA11y(undefined, undefined, (violations) => {
        cy.log(`${violations.length} accessibility violation(s) detected`);
        violations.forEach((violation) => {
          cy.log(`Violation: ${violation.id}`);
          cy.log(`Impact: ${violation.impact}`);
          cy.log(`Description: ${violation.description}`);
          cy.log(`Help: ${violation.helpUrl}`);
        });
      });
    });
  });

  it('Fehlerseite überprüfen', () => {
    allure.story('Fehlerseite überprüfen');
    allure.severity('mminor');
    allure.step('Cookies löschen und prüfen', () => {
      cy.clearAllCookies().then(() => {
        errorPage.getErrorTimeoutPage().should('be.visible')
          .checkA11y(undefined, undefined, (violations) => {
            cy.log(`${violations.length} accessibility violation(s) detected`);
            violations.forEach((violation) => {
              cy.log(`Violation: ${violation.id}`);
              cy.log(`Impact: ${violation.impact}`);
              cy.log(`Description: ${violation.description}`);
              cy.log(`Help: ${violation.helpUrl}`);
            });
          });
      });
    });
  });

  after('logout', () => {
    allure.step('logout', () => {
      consentDigitizePage.getHeaderComponent().getHeaderUserInfoLogoutButton().eq(0).should('be.visible').click();
    });
  });

});
