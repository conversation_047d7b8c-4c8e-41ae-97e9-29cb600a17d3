import {ExternalLoginPagePo} from "../support/pageObjects/external-login-page.po";
import {LoginPagePo} from "../support/pageObjects/login-page.po";
import {HomePagePo} from "../support/pageObjects/home-page.po";
import {SearchResultsPo} from "../support/pageObjects/search-results.po";
import {ConsentDigitizePo} from "../support/pageObjects/consent-digitize-page.po";
import {DigitizeLicensePo} from "../support/pageObjects/digitize-licence/digitize-license-page.po";
import { ExaminationPagePo } from "@/support/pageObjects/examination-page.po";
import { ServiceOverviewPo } from "@/support/pageObjects/service-overview.po";
import * as allure from "allure-js-commons";
import { HeaderComponentCo } from "@/support/sharedComponents/header-component.co";

describe('Prüfung-und-Fischereischein-anlegen', () => {
  const headerComponent: HeaderComponentCo = new HeaderComponentCo();
  const externalLoginPage: ExternalLoginPagePo = new ExternalLoginPagePo();
  const loginPage: LoginPagePo = new LoginPagePo();
  const homePage: HomePagePo = new HomePagePo();
  const searchResultsPage: SearchResultsPo = new SearchResultsPo();
  const consentDigitizePage: ConsentDigitizePo = new ConsentDigitizePo();
  const digitizeLicencePage: DigitizeLicensePo = new DigitizeLicensePo();
  const examinationPage: ExaminationPagePo = new ExaminationPagePo();
  const serviceOverviewPage: ServiceOverviewPo = new ServiceOverviewPo();
  const consentPage: ConsentDigitizePo = new ConsentDigitizePo();
  let testUsers: any;
  let testData: any;
  let testCitizen: any;
  const system = Cypress.env('system');

  before(() => {
    cy.clearAllCookies();
    allure.feature('Prüfung-und-Fischereischein-anlegen');
    allure.owner('Hegedus Kinga');

    cy.fixture(`${system}/suche/testuser.json`).then((users) => {
      cy.log('reading the test users file');
      testUsers = users;
    });

    cy.fixture(`${system}/suche/testdata.json`).then((data) => {
      cy.log('reading the test data file');
      testData = data;
      cy.log(JSON.stringify(testData));
    });

    cy.fixture(`${system}/suche/testcitizen.json`).then((citizen) => {      
      cy.log('reading the test users file');      
      testCitizen = citizen;        
    });
  })

  it('Eine neue Fischereischein Prüfung anlegen, eine neue Fischereiabgabe für das folgende Jahr mit 3 Jahre Gültigkeit anlegen und Zuständigkeit vergeben',()=>{
    allure.story('Eine neue Fischereischein Prüfung anlegen, eine neue Fischereiabgabe für das folgende Jahr mit 3 Jahre Gültigkeit anlegen und Zuständigkeit vergeben');
    allure.severity('normal');

    allure.step('Als Prüfer anmelden', () => {
      allure.parameter("username", testUsers.user2.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user2.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user2.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });

    allure.step('Befüllen des Prüfungsformulars',()=>{
      examinationPage.getExaminationPage().should('be.visible')      
      examinationPage.getExamPageFormFirstname().should('be.visible').type(testCitizen.buerger1.person.firstname)
      examinationPage.getExamPageFormLastname().should('be.visible').type(testCitizen.buerger1.person.lastname)
      examinationPage.getExamPageFormBirthdate().should('be.visible').type(testCitizen.buerger1.person.birthdate)        
      examinationPage.getExamPageFormBirthplace().should('be.visible').type(testCitizen.buerger1.person.birthplace)
      examinationPage.getExamPageFormPassDate().should('be.visible').type("2009-01-01")
      examinationPage.getExamPageContinueButton().should('be.visible').click()
      examinationPage.getExamSubmitButton().should('be.visible').click()
      examinationPage.getSuccessPage().should('be.visible').contains("Erfolgreich angelegt!")
      examinationPage.getSuccessPage().should('be.visible').contains("Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.")
      examinationPage.getSuccessEntries().invoke('text')
      .then((text) => {
        const match = text.match(/ZF\d{2}-\d{4}-\d{4}-\d{4}/);
        cy.wrap(match[0]).as('accessCode')
      });
      cy.get('@accessCode').then((accessCode) => {
        cy.log(`Extracted Code: ${accessCode}`);
        examinationPage.getSuccessEntries().should('be.visible').contains(accessCode)
      });
    }) 

    allure.step('Als Prüfer abmelden', () => {
     headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click()      
    });

    allure.step('Als SH Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
    
    allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
      cy.get('@accessCode').then((accessCode) => {
        searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
          .type(accessCode);
      });
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
      serviceOverviewPage.getUserProfileHeader().should('be.visible')
        .contains(`${testCitizen.buerger1.person.firstname} ${testCitizen.buerger1.person.lastname}`)
    })

    allure.step('Überprüfe den Bereich Fischereiabgabe',()=>{
      serviceOverviewPage.getTaxPaymentCard().should('be.visible')
      serviceOverviewPage.getTaxPaymentButton().should('be.visible').click()
    })

    allure.step('Akzeptiere DSVGO',()=>{
      consentDigitizePage.getConsentTaxPaymentPage().should('be.visible');
      consentDigitizePage.getTaxPaymentPdfLink().should('be.visible');
      consentDigitizePage.getTaxPaymentInfoLink().should('be.visible');
      consentDigitizePage.getTaxSelfDisclosureCheckbox().should('be.visible').click();
      consentDigitizePage.getConsentTaxPageContinueButton().should('be.enabled').click()
    })

    allure.step('Fischereiabgabe anlegen',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPaymentPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains("22,00")
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('22,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains("22 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).should('be.visible').contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(1).should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(0).should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(2).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(3).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('46,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('46,00 €')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(3000)
      digitizeLicencePage.getDocumentsComponent().getDocumentsTaxPaymentPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })

    allure.step('Als SH miterbeiter abmelden', () => {
     headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click()     
    })

    allure.step('Als NRW Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });

    allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
      cy.get('@accessCode').then((accessCode) => {
        searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
          .type(accessCode);
      });
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
      serviceOverviewPage.getUserProfileHeader().should('be.visible')
        .contains(`${testCitizen.buerger1.person.firstname} ${testCitizen.buerger1.person.lastname}`)
    })
    
    allure.step('Öffne das Formular "Zuständigkeitsvergabe".',()=>{
      serviceOverviewPage.getMoveJurisdiction().should('be.visible').click()
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Sie sind im Begriff, die Zuständigkeit für Theo Fischer in Ihr Bundesland zu überführen.")
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Diese Aktion kann nicht rückgängig gemacht werden.")
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Wenden Sie sich im Falle eines Fehlers bitte an eine:n Administrator:in.")
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Zukünftige Zuständigkeit für Theo Fischer liegt in:")
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Nordrhein-Westfalen")
    })
    
    allure.step('Fülle das Formular "Zuständigkeitsvergabe" aus.',()=>{
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionForm().should('be.visible')
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveCertificate().should('be.visible')
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveCertificateThirdParty().should('be.visible')
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveCertificateCheckbox().should('be.visible').click()
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveCertificateThirdPartyCheckbox().should('be.visible')
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionContinueButton().should('be.visible').click()
    })
    
    allure.step('Akzeptiere DSVGO',()=>{
      consentPage.getConsentDigitizePage().should('be.visible')
      consentPage.getConsentForm().should('be.visible')
      consentPage.getInfoLink().should('be.visible')
      consentPage.getPdfLink().should('be.visible')
      consentPage.getConsentFormGDPR().should('be.visible')
      consentPage.getConsentFormSelfDisclosure().should('be.visible')
      consentPage.getSelfDisclosureCheckbox().should('be.visible').click()
      consentPage.getGdprCheckbox().should('be.visible').click()
      consentPage.getConsentPageContinueButton().should('be.visible').click()
    })
    
    allure.step('Fülle das Formular "Fischereiabgabe" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains("0,00 €")
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains("— €")
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains("22 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).should('be.visible').contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains("22,00 €")
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains("22,00 €")
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible').click()
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })
    
    allure.step('Überprüfe die Seite zur Bestätigung des Zuständigkeitsumzugs',()=>{
      cy.wait(2000)
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJurisdictionConfirmationPage().should('be.visible').contains("Zuständigkeits-Umzug erfolgreich!")
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJurisdictionConfirmationHeader().should('be.visible').contains("Anpassung wurde wurde erfolgreich im Fischereiregister aufgenommen.")
      serviceOverviewPage.getMoveJurisdictionComponent().getMoveJurisdictionConfirmationTableEntries().should('be.visible').contains("NW")
    })

    allure.step('Als NRW Mitarbeiter abmelden', () => {
     headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click()     
    })
  })

  it('Eine neue Fischereischein Prüfung anlegen und eine neue Fischereiabgabe für das folgende Jahr mit 3 Jahre Gültigkeit anlegen',()=>{
    allure.story('Eine neue Fischereischein Prüfung anlegen und eine neue Fischereiabgabe für das folgende Jahr mit 3 Jahre Gültigkeit anlegen');
    allure.severity('normal');

    allure.step('Als Prüfer anmelden', () => {
      allure.parameter("username", testUsers.user2.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user2.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user2.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });

    allure.step('Befüllen des Prüfungsformulars',()=>{
      examinationPage.getExaminationPage().should('be.visible')      
      examinationPage.getExamPageFormFirstname().should('be.visible').type(testCitizen.buerger1.person.firstname)
      examinationPage.getExamPageFormLastname().should('be.visible').type(testCitizen.buerger1.person.lastname)
      examinationPage.getExamPageFormBirthdate().should('be.visible').type(testCitizen.buerger1.person.birthdate)        
      examinationPage.getExamPageFormBirthplace().should('be.visible').type(testCitizen.buerger1.person.birthplace)
      examinationPage.getExamPageFormPassDate().should('be.visible').type("2009-01-01")
      examinationPage.getExamPageContinueButton().should('be.visible').click()
      examinationPage.getExamSubmitButton().should('be.visible').click()
      examinationPage.getSuccessPage().should('be.visible').contains("Erfolgreich angelegt!")
      examinationPage.getSuccessPage().should('be.visible').contains("Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.")
      examinationPage.getSuccessEntries().invoke('text')
      .then((text) => {
        const match = text.match(/ZF\d{2}-\d{4}-\d{4}-\d{4}/);
        cy.wrap(match[0]).as('accessCode')
      });
      cy.get('@accessCode').then((accessCode) => {
        cy.log(`Extracted Code: ${accessCode}`);
        examinationPage.getSuccessEntries().should('be.visible').contains(accessCode)
      });
    }) 

    allure.step('Als Prüfer abmelden', () => {
     headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click()     
    });

    allure.step('Als SH Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
    
    allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
      cy.get('@accessCode').then((accessCode) => {
        searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
          .type(accessCode);
      });
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
      serviceOverviewPage.getUserProfileHeader().should('be.visible')
        .contains(`${testCitizen.buerger1.person.firstname} ${testCitizen.buerger1.person.lastname}`)
    })

    allure.step('Eine neue Fischereiabgabe anlegen',()=>{
      serviceOverviewPage.getTaxPaymentCard().should('be.visible')
      serviceOverviewPage.getTaxPaymentButton().should('be.visible').click()
    })

    allure.step('Akzeptiere DSVGO',()=>{
      consentDigitizePage.getConsentTaxPaymentPage().should('be.visible');
      consentDigitizePage.getTaxPaymentPdfLink().should('be.visible');
      consentDigitizePage.getTaxPaymentInfoLink().should('be.visible');
      consentDigitizePage.getTaxSelfDisclosureCheckbox().should('be.visible').click();
      consentDigitizePage.getConsentTaxPageContinueButton().should('be.enabled').click()
    })

    allure.step('Fischereiabgabe anlegen',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPaymentPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains("22,00")
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('22,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains("22 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).should('be.visible').contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(1).should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(0).should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(2).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(3).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('46,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('46,00 €')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(3000)
      digitizeLicencePage.getDocumentsComponent().getDocumentsTaxPaymentPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })

    allure.step('Als SH miterbeiter abmelden', () => {
     headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click()     
    })
  })

  it('Eine neue Fischereischein Prüfung anlegen und eine neue Fischereiabgabe für das folgende Jahr mit 2 Jahre Gültigkeit anlegen',()=>{
    allure.story('Eine neue Fischereischein Prüfung anlegen und eine neue Fischereiabgabe für das folgende Jahr mit 2 Jahre Gültigkeit anlegen');
    allure.severity('normal');

    allure.step('Als Prüfer anmelden', () => {
      allure.parameter("username", testUsers.user2.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user2.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user2.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });

    allure.step('Befüllen des Prüfungsformulars',()=>{
      examinationPage.getExaminationPage().should('be.visible')      
      examinationPage.getExamPageFormFirstname().should('be.visible').type(testCitizen.buerger1.person.firstname)
      examinationPage.getExamPageFormLastname().should('be.visible').type(testCitizen.buerger1.person.lastname)
      examinationPage.getExamPageFormBirthdate().should('be.visible').type(testCitizen.buerger1.person.birthdate)        
      examinationPage.getExamPageFormBirthplace().should('be.visible').type(testCitizen.buerger1.person.birthplace)
      examinationPage.getExamPageFormPassDate().should('be.visible').type("2009-01-01")
      examinationPage.getExamPageContinueButton().should('be.visible').click()
      examinationPage.getExamSubmitButton().should('be.visible').click()
      examinationPage.getSuccessPage().should('be.visible').contains("Erfolgreich angelegt!")
      examinationPage.getSuccessPage().should('be.visible').contains("Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.")
      examinationPage.getSuccessEntries().invoke('text')
      .then((text) => {
        const match = text.match(/ZF\d{2}-\d{4}-\d{4}-\d{4}/);
        cy.wrap(match[0]).as('accessCode')
      });
      cy.get('@accessCode').then((accessCode) => {
        cy.log(`Extracted Code: ${accessCode}`);
        examinationPage.getSuccessEntries().should('be.visible').contains(accessCode)
      });
    }) 

    allure.step('Als Prüfer abmelden', () => {
     headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click()     
    });

    allure.step('Als SH Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
    
    allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
      cy.get('@accessCode').then((accessCode) => {
        searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
          .type(accessCode);
      });
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
      serviceOverviewPage.getUserProfileHeader().should('be.visible')
        .contains(`${testCitizen.buerger1.person.firstname} ${testCitizen.buerger1.person.lastname}`)
    })

    allure.step('Eine neue Fischereiabgabe anlegen',()=>{
      serviceOverviewPage.getTaxPaymentCard().should('be.visible')
      serviceOverviewPage.getTaxPaymentButton().should('be.visible').click()
    })

    allure.step('Akzeptiere DSVGO',()=>{
      consentDigitizePage.getConsentTaxPaymentPage().should('be.visible');
      consentDigitizePage.getTaxPaymentPdfLink().should('be.visible');
      consentDigitizePage.getTaxPaymentInfoLink().should('be.visible');
      consentDigitizePage.getTaxSelfDisclosureCheckbox().should('be.visible').click();
      consentDigitizePage.getConsentTaxPageContinueButton().should('be.enabled').click()
    })

    allure.step('Fischereiabgabe anlegen',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPaymentPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains("22,00")
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('22,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains("22 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(0).should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(0).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(0).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).contains("22 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(1).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('22,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('22,00 €')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(3000)
      digitizeLicencePage.getDocumentsComponent().getDocumentsTaxPaymentPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })

    allure.step('Als SH Mitarbeiter abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click()    
    })
  })

})
