import {ExternalLoginPagePo} from "../support/pageObjects/external-login-page.po";
import {LoginPagePo} from "../support/pageObjects/login-page.po";
import {HomePagePo} from "../support/pageObjects/home-page.po";
import {SearchResultsPo} from "../support/pageObjects/search-results.po";
import {ConsentDigitizePo} from "../support/pageObjects/consent-digitize-page.po";
import * as allure from "allure-js-commons";
import { LicencePaymentsPo } from "../support/pageObjects/licence-payment-page.po";
import { DigitizeLicensePo } from "../support/pageObjects/digitize-licence/digitize-license-page.po";
import { EditFooterComponentCo } from "@/support/sharedComponents/edit-footer-component.co";
import { HeaderComponentCo } from "@/support/sharedComponents/header-component.co";

// Testsuite für Fischereischein Anlage
describe('Fischereiabgabe-anlegen', () => {
  const headerComponent:HeaderComponentCo = new HeaderComponentCo()
  const externalLoginPage: ExternalLoginPagePo = new ExternalLoginPagePo();
  const loginPage: LoginPagePo = new LoginPagePo();
  const homePage: HomePagePo = new HomePagePo();
  const searchResultsPage: SearchResultsPo = new SearchResultsPo();
  const consentDigitizePage: ConsentDigitizePo = new ConsentDigitizePo();
  const licencePaymentPage: LicencePaymentsPo = new LicencePaymentsPo();
  const digitizeLicencePage: DigitizeLicensePo = new DigitizeLicensePo();
  const editFooterComponentPage: EditFooterComponentCo = new EditFooterComponentCo();
  const editHeaderComponent: HeaderComponentCo = new HeaderComponentCo();
  let testUsers: any;
  let testData: any;
  let testCitizen: any;
  const system = Cypress.env('system');

  before(() => {
    cy.clearAllCookies();
    allure.feature('Fischereischeinabgabe Anlage');
    allure.owner('Hegedus Kinga');

    cy.fixture(`${system}/suche/testuser.json`).then((users) => {
      cy.log('reading the test users file');
      testUsers = users;
      cy.log(`run Tests using test users on: ${system}`);
    });

    cy.fixture(`${system}/suche/testdata.json`).then((data) => {
      cy.log('reading the test data file');
      testData = data;
      cy.log(`run Tests using test users on: ${system}`);
      cy.log(JSON.stringify(testData));
    });

    cy.fixture(`${system}/suche/testcitizen.json`).then((citizen) => {
      cy.log('reading the test data file');
      testCitizen = citizen;
      cy.log(`run Tests using test citizens on: ${system}`);
      cy.log(JSON.stringify(testCitizen));
    });

    allure.step('Einen Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
  })

  it('Starte den Prozess für das Anlegen einer Fischereiabgabe', () => {
    allure.story('Starte den Prozess für das Anlegen einer Fischereiabgabe');
    allure.severity('normal');

    allure.step('Führe eine Suche durch und überprüfe, ob das Menü zur Erstellung von Registereinträgen vorhanden ist', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
    });

    allure.step('Überprüfe der Menü zur Erstellung von Fischereiabgabe', () => {
      searchResultsPage.getSearchResultsPageCreationMenu();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenuVacation().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenuTax().should('be.visible').click();
    })

    allure.step('Öffne  und überprüfe die Seite DSGVO', () => {        
      consentDigitizePage.getConsentTaxPaymentPage().should('be.visible');
      consentDigitizePage.getTaxPaymentInfoLink().should('be.visible');
      consentDigitizePage.getTaxPaymentPdfLink().should('be.visible');
      consentDigitizePage.getTaxPaymentGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentTaxPageContinueButton().should('be.enabled').click()
    });
  });
  
  it('Überprüfe die Validierung der Pflichtfelder in dem Formular "Personen Daten"', () => {
    allure.step('Überprüfe die Validierung der Pflichtfelder in dem Formular "Personen Daten"', () => {
      licencePaymentPage.getTabPersonalData().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstname().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastname().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDate().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlace().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormNationalityInput().should('be.visible').clear();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthName().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormNationalityInput().should('have.class', 'ng-invalid');
    });
  });

  it('Überprüfe die Validierung der pflichtfelder in Personen Daten Formular', () => {
    allure.step('Überprüfe die Validierung der pflichtfelder in Personen Daten Formular', () => {
      licencePaymentPage.getTabPersonalData().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().type(testCitizen.buerger1.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().type(testCitizen.buerger1.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().type(testCitizen.buerger1.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().type(testCitizen.buerger1.person.birthplace)
      editHeaderComponent.getAllTabTitleState().eq(0).should('have.class', 'active')
      editHeaderComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
      editHeaderComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
      editHeaderComponent.getAllTabTitleState().eq(1).should('be.disabled')    
      editHeaderComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
      editHeaderComponent.getAllTabTitleState().eq(2).should('be.disabled')  
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormNationalityAllOptions().eq(0).click()
      editFooterComponentPage.getFooterConfirmBox().should('be.visible')
      editFooterComponentPage.getFooterConfirmBoxCheckbox().should('be.visible').click()
      editFooterComponentPage.getFooterContinueButton().should('not.be.disabled')
    });
  });

  it('Überprüfe die Validierung aller Felder auf der Zahlungsseite zu Fischereischeinlosen Abgaben',() => {
    allure.step('Überprüfe die Validierung aller Felder auf der Zahlungsseite zu Fischereischeinlosen Abgaben',() => {
      editFooterComponentPage.getFooterContinueButton().click()
      digitizeLicencePage.getTaxesComponent().getTaxesPaymentsNewUserPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains("22,00")
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('22,00 €')
      editHeaderComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
      editHeaderComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
      editHeaderComponent.getAllTabTitleState().eq(1).should('have.class', 'active')
      editHeaderComponent.getAllTabTitleState().eq(1).should('not.be.disabled')    
      editHeaderComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
      editHeaderComponent.getAllTabTitleState().eq(2).should('be.disabled')     
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains("22 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).should('be.visible').contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(0).should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(1).should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(2).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(2).click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(3).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("46 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(0).should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(0).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(1).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('22,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('22,00 €')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible')
    })
  })

  it('Überprüfe die digitale Dokumente und stelle die Zahlung bereit',()=>{
    allure.step("Überprüfe die digitale Dokumente und stelle die Zahlung bereit",()=>{
      editFooterComponentPage.getFooterFinishButton().click()
      licencePaymentPage.getPaymentDocumentsPage("new").should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      editHeaderComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
      editHeaderComponent.getAllTabTitleState().eq(0).should('be.disabled')
      editHeaderComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
      editHeaderComponent.getAllTabTitleState().eq(1).should('be.disabled')    
      editHeaderComponent.getAllTabTitleState().eq(2).should('have.class', 'active')
      editHeaderComponent.getAllTabTitleState().eq(2).should('not.be.disabled')  
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })
  
  after(() => {
    allure.step('logout', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    });
  })
})
