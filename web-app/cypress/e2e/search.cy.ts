import {ExternalLoginPagePo} from "../support/pageObjects/external-login-page.po";
import {LoginPagePo} from "../support/pageObjects/login-page.po";
import {HomePagePo} from "../support/pageObjects/home-page.po";
import {SearchResultsPo} from "../support/pageObjects/search-results.po";
import {expect} from "chai";
import * as allure from "allure-js-commons";

describe('Personen Suche', () => {
  const externalLoginPage: ExternalLoginPagePo = new ExternalLoginPagePo();
  const loginPage: LoginPagePo = new LoginPagePo();
  const homePage: HomePagePo = new HomePagePo();
  const searchResultsPage: SearchResultsPo = new SearchResultsPo();
  let testUsers: any;
  let testData: any;
  let testCitizen: any;
  const system = Cypress.env('system');

  before(() => {
    cy.clearCookies();
    allure.feature('<PERSON>en Suche');
    allure.owner('Hegedus Kinga');

    cy.fixture(`${system}/suche/testuser.json`).then((users) => {
      cy.log('reading the test users file');
      testUsers = users;
      cy.log(`run Tests using test users on: ${system}`);
    });

    cy.fixture(`${system}/suche/testdata.json`).then((data) => {
      cy.log('reading the test data file');
      testData = data;
      cy.log(`run Tests using test users on: ${system}`);
      cy.log(JSON.stringify(testData));
    });

    cy.fixture(`${system}/suche/testcitizen.json`).then((citizen) => {
      cy.log('reading the test users file');
      testCitizen = citizen;
      cy.log(`run Tests using dedicated test citizen on: ${system}`);
    });
  });

  it('Benutzer login', () => {
    allure.story('Benutzer login');
    allure.severity('blocker');
    allure.description('dieses Test überprüft ob ein Mitarbeiter, Prüfer, oder Kontrolleur sich anmelden kann');

    allure.step('öffne die Startseite', () => {
      allure.parameter("url", testData.url);
      cy.visit('/');
      loginPage.getLoginPage().should('be.visible');
    });

    allure.step('Anmeldung mit login Daten', () => {
      allure.parameter("username", testUsers.user6.username);
      loginPage.getLoginButton().should('be.visible').click();
      externalLoginPage.getLoginPage().should('be.visible');
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().should('be.visible').click();
    });
  });

  it('Startseite Überprüfung', () => {
    allure.story('Startseite Überprüfung');
    allure.severity('critical');
    allure.step('Prüfen ob die Startseite angezeigt ist', () => {
      homePage.getHomePage().should('be.visible');
    })
  });

  it('Überprüfung der Informationen des angemeldeten Benutzers', () => {
    allure.story('Überprüfung der Informationen des angemeldeten Benutzers');
    allure.severity('normal');
    allure.step('Prüfen ob die Daten der angemeldeten Benutzer angezeigt sind', () => {
      allure.parameter("given_name", testUsers.user6.given_name);
      allure.parameter("family_name", testUsers.user6.family_name);
      allure.parameter("Office_adress", testUsers.user6.officeAddress.office);
      homePage.getHeaderComponent().getHeaderUserInfoUsername()
        .should('be.visible')
        .invoke('text').then((text) => {
        expect(text.trim()).equal(testUsers.user6.given_name + ' ' + testUsers.user6.family_name);
      });
      homePage.getHeaderComponent().getHeaderUserInfoOffice().should('be.visible').invoke('text')
        .then((text) => {
          expect(text.trim()).equal(testUsers.user6.officeAddress.office);
        });
    });
  });

  it('Suche mit Nachname', () => {
    allure.story('Suche mit Nachname');
    allure.severity('normal');
    allure.step('Nachname im Suchfeld eingeben', () => {
      allure.parameter("lastname", testCitizen.buerger4.person.lastname);
      allure.parameter("firstname", testCitizen.buerger4.person.firstname);
      homePage.getHomePage().then((result) => {
        result.find(homePage.getSearchBarInputComponent().getSearchbarComponentInput()
          .should('be.visible').type(`${testCitizen.buerger4.person.lastname}`));
        result.find(homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click());
      });
    });

    allure.step('Überprüfen, ob die Person gefunden wurde', () => {
      searchResultsPage.getSearchResultsPage().should('be.visible');
      searchResultsPage.getSearchResultsPageNoResults().should('be.visible').contains("Bitte präzisieren Sie ihre Suche.")
      searchResultsPage.getSearchResultsPageNoResults().contains("Suche ergab zu viele Ergebnisse. Bitte ergänzen Sie weitere personenbezogene Daten.")
      searchResultsPage.getSearchResultsPageNoResults().should('be.visible').contains("Max Mustermann, 24.03.2024, Hamburg")
    });
  });

  it('Suche mit Vorname und Nachname', () => {
    allure.story('Suche mit Vorname und Nachname');
    allure.severity('normal');
    allure.step('Suchfeld auf der Suchergebnis Seite leeren', () => {
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible');
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentClearButton().should('be.visible').click();
    });
    allure.step('Vorname und Nachname im Suchfeld eingeben', () => {
      allure.parameter("lastname", testCitizen.buerger4.person.lastname);
      allure.parameter("firstname", testCitizen.buerger4.person.firstname);
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
        .type(testCitizen.buerger4.person.firstname + ' ' + testCitizen.buerger4.person.lastname);
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentSearchButton().should('be.visible').click();
    });

    allure.step('Überprüfen, ob die Person gefunden wurde', () => {
      searchResultsPage.getSearchResultsPage().should('be.visible');
      searchResultsPage.getSearchResultsPageNoResults().should('be.visible').contains("Bitte präzisieren Sie ihre Suche.")
      searchResultsPage.getSearchResultsPageNoResults().contains("Suche ergab zu viele Ergebnisse. Bitte ergänzen Sie weitere personenbezogene Daten.")
      searchResultsPage.getSearchResultsPageNoResults().should('be.visible').contains("Max Mustermann, 24.03.2024, Hamburg")
    });

  });

  it('Suche mit Vorname, Nachname und Geburtsdatum', () => {
    allure.story('Suche mit Vorname, Nachname und Geburtsdatum');
    allure.severity('normal');
    allure.step('Suchfeld auf der Suchergebnis Seite leeren', () => {
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible');
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentClearButton().should('be.visible').click();
    });
    allure.step('Vorname und Nachname im Suchfeld eingeben', () => {
      allure.parameter("lastname", testCitizen.buerger7.person.lastname);
      allure.parameter("firstname", testCitizen.buerger7.person.firstname);
      allure.parameter("birthdate", testCitizen.buerger7.person.birthdate);
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
        .type(testCitizen.buerger7.person.firstname + ' ' + 
              testCitizen.buerger7.person.lastname + ',' + 
              testCitizen.buerger7.person.birthdate);
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
    });
    allure.step('Überprüfen, ob die Person gefunden wurde', () => {
      searchResultsPage.getSearchResultsPage().should('be.visible');
      searchResultsPage.getSearchResultsPage().then(resultsPage => {
        resultsPage.find(searchResultsPage.getSearchResultsPageNoResults().should('not.exist'));
        resultsPage.find(searchResultsPage.getSearchResultsComponent().getSearchResultsComponent().should('be.visible'));
        resultsPage.find(searchResultsPage.getSearchResultsComponent()
          .getSearchResultsRow().eq(1)
          .within(() => {
            searchResultsPage.getSearchResultsComponent().getSearchResultsRowName().invoke('text')
              .then((text) => {
                expect(text.trim())
                  .equal(`${testCitizen.buerger7.person.firstname} ${testCitizen.buerger7.person.lastname}`);
              });
          }));
      });
    });
  });

  it('Suche mit Kennung', () => {
    allure.story('Suche mit Kennung');
    allure.severity('normal');
    allure.step('Suchfeld auf der Suchergebnis Seite leeren', () => {
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible');
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentClearButton().should('be.visible').click();
    });
    allure.step('Kennung im Suchfeld eingeben', () => {
      allure.parameter("Kennung", testCitizen.buerger3.fishingLicenses[0].number);
      cy.formatLicenseNumber(testCitizen.buerger3.fishingLicenses[0].number).then((formatedLiscence) => {
        searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
          .type(formatedLiscence);
      });
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
    });
    allure.step('Überprüfen, ob die Person gefunden wurde', () => {
      searchResultsPage.getSearchResultsPage().should('be.visible');
      searchResultsPage.getSearchResultsComponent().getSearchResultsRow()
        .should('have.length', 1)
        .within(() => {
          searchResultsPage.getSearchResultsComponent().getSearchResultsRowName().invoke('text')
            .then((text) => {
              expect(text.trim())
                .equal(`${testCitizen.buerger3.person.firstname} ${testCitizen.buerger3.person.lastname}`);
            });
          cy.formatLicenseNumber(testCitizen.buerger3.fishingLicenses[0].number).then((formatedLiscence) => {
            const kennung = formatedLiscence;
            searchResultsPage.getSearchResultsComponent().getSearchResultsRowIdentification().invoke('text')
              .then((text) => {
                expect(text.trim()).equal(kennung)
              });
          });
        });
    });
  });

  it('Suche mit mehr als 100 Ergebnissen', () => {
    allure.story('Suche mit mehr als 100 Ergebnissen');
    allure.severity('minor');
    allure.step('Suchfeld auf der Suchergebnis Seite leeren', () => {
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentClearButton().should('be.visible').click();
    });
    allure.step('Ein verbreites Nachname Eingeben', () => {
      allure.parameter('Nachname', testCitizen.buerger1.person.lastname);
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
        .type(testCitizen.buerger1.person.lastname);
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentSearchButton().should('be.visible').click();
    });
    allure.step('keine Ergebnisse werden angezeigt', () => {
      searchResultsPage.getSearchResultsPage().should('be.visible');
      searchResultsPage.getSearchResultsPage().then(resultsPage => {
        resultsPage.find(searchResultsPage.getSearchResultsPageNoResults().should('be.visible'));
        resultsPage.find(searchResultsPage.getSearchResultsComponent().getSearchResultsComponent().should('not.exist'));
      });
    });
  });

  it('Leere Suche', () => {
    allure.story('Leere Suche');
    allure.severity('minor');
    allure.step('leere durchführen', () => {
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentClearButton().should('be.visible').click();
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentSearchButton().should('be.visible').click();
    });
    allure.step('keine Ergebnisse werden angezeigt', () => {
      searchResultsPage.getSearchResultsPage().should('be.visible');
      searchResultsPage.getSearchResultsPage().then(resultsPage => {
        resultsPage.find(searchResultsPage.getSearchResultsPageNoResults().should('be.visible'));
        resultsPage.find(searchResultsPage.getSearchResultsComponent().getSearchResultsComponent().should('not.exist'));
      });
    });
  });

  it('Suche mit mehrere Ergebnissen', () => {
    allure.story('Suche mit mehrere Ergenissen');
    allure.severity('minor');
    allure.step('Suche durchführen', () => {

      allure.parameter('Nachname', testCitizen.buerger4.person.lastname);
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
        .type(`${testCitizen.buerger4.person.firstname} ${testCitizen.buerger4.person.lastname}`);
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentSearchButton().should('be.visible').click();
      searchResultsPage.getSearchResultsPage().should('be.visible');
    });

    allure.step('Überprüfen, ob die Person gefunden wurde', () => {
      searchResultsPage.getSearchResultsPage().should('be.visible');
      searchResultsPage.getSearchResultsPageNoResults().should('be.visible').contains("Bitte präzisieren Sie ihre Suche.")
      searchResultsPage.getSearchResultsPageNoResults().contains("Suche ergab zu viele Ergebnisse. Bitte ergänzen Sie weitere personenbezogene Daten.")
      searchResultsPage.getSearchResultsPageNoResults().should('be.visible').contains("Max Mustermann, 24.03.2024, Hamburg")
    });

  });

  it('Suche ohne Ergenisse', () => {
    allure.story('Suche ohne Ergenisse');
    allure.severity('minor');
    allure.step('Suche durchführen',()=>{
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentClearButton().should('be.visible').click();
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
        .type('aSearchTermWithoutResults');
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentSearchButton().should('be.visible').click();
    })
    allure.step('Ergebnisse überprüfen',()=>{
      searchResultsPage.getSearchResultsPage().should('be.visible');
      searchResultsPage.getSearchResultsPage().then(resultsPage => {
        resultsPage.find(searchResultsPage.getSearchResultsPageNoResults().should('be.visible'));
        resultsPage.find(searchResultsPage.getSearchResultsComponent().getSearchResultsComponent().should('not.exist'));
      });
    })
  });

  after(() => {
      allure.step('Abmelden', () => {
        searchResultsPage.getHeaderComponent().getHeaderUserInfoLogoutButton().eq(0).should('be.visible').click();
      });
    })
});
