import {ExternalLoginPagePo} from "../support/pageObjects/external-login-page.po";
import {LoginPagePo} from "../support/pageObjects/login-page.po";
import {HomePagePo} from "../support/pageObjects/home-page.po";
import {SearchResultsPo} from "../support/pageObjects/search-results.po";
import {ConsentDigitizePo} from "../support/pageObjects/consent-digitize-page.po";
import {DigitizeLicensePo} from "../support/pageObjects/digitize-licence/digitize-license-page.po";
import {ServiceOverviewPo} from "../support/pageObjects/service-overview.po";
import * as allure from "allure-js-commons";
import { HeaderComponentCo } from "../support/sharedComponents/header-component.co";
import {DialogComponent} from "../support/sharedComponents/dialog-component.co";

describe('Fischereischein-digitalisieren', () => {
  const externalLoginPage: ExternalLoginPagePo = new ExternalLoginPagePo();
  const loginPage: LoginPagePo = new LoginPagePo();
  const homePage: HomePagePo = new HomePagePo();
  const searchResultsPage: SearchResultsPo = new SearchResultsPo();
  const consentDigitizePage: ConsentDigitizePo = new ConsentDigitizePo();
  const digitizeLicencePage: DigitizeLicensePo = new DigitizeLicensePo();
  const headerComponent: HeaderComponentCo = new HeaderComponentCo();
  const serviceOverviewPage: ServiceOverviewPo = new ServiceOverviewPo();
  const dialogComponent: DialogComponent = new DialogComponent();
  let testUsers: any;
  let testData: any;
  let testCitizen: any;
  const system = Cypress.env('system');

  beforeEach(() => {
    cy.clearAllCookies();
    allure.feature('Fischereischein Anlage');
    allure.owner('Hegedus Kinga');

    cy.fixture(`${system}/suche/testuser.json`).then((users) => {
      cy.log('reading the test users file');
      testUsers = users;
    });

    cy.fixture(`${system}/suche/testdata.json`).then((data) => {
      cy.log('reading the test data file');
      testData = data;
      cy.log(JSON.stringify(testData));
    });

    cy.fixture(`${system}/suche/testcitizen.json`).then((citizen) => {      
      cy.log('reading the test users file');      
      testCitizen = citizen;        
    });

    allure.step('Einen Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
  })

  it('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, keine bezahlten Abgaben, keine ergänzenden Abgaben, Kartenzahlung.',()=>{
    allure.story('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, keine bezahlten Abgaben, keine ergänzenden Abgaben, Kartenzahlung.');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      
      
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearDown().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })

  it('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, mit eine bezahlte Abgabe für nächstes Jahr, keine ergänzenden Abgaben, Kartenzahlung.',()=>{
    allure.story('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, mit eine bezahlte Abgabe für nächstes Jahr, keine ergänzenden Abgaben, Kartenzahlung.');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(1).should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })

  it('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, mit eine bezahlte Abgabe für nächstes Jahr, mit ergänzede Abgabe für die übernächstes Jahr und 2 Jahre Gültigkeit, Kartenzahlung.',()=>{
    allure.story('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, mit eine bezahlte Abgabe für nächstes Jahr, mit ergänzede Abgabe für die übernächstes Jahr und 2 Jahre Gültigkeit, Kartenzahlung.');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      
      
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(1).should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains("12 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).should('be.visible').contains("36 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(0).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(0).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).contains("12 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(1).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("36 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('12,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('41,00 €')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(3).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(3).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })

  it('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, keine bezahlten Abgaben, mit ergänzede Abgabe für die nächstes Jahr und 3 Jahre Gültigkeit, Kartenzahlung.',()=>{
    allure.story('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, keine bezahlten Abgaben, mit ergänzede Abgabe für die nächstes Jahr und 3 Jahre Gültigkeit, Kartenzahlung.');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      
      
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearDown().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains("12 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).should('be.visible').contains("36 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(1).should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(0).should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(2).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("36 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(3).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("36 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('36,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('65,00 €')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })

  it('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, keine angegebenen Adresse, keine bezahlten Abgaben, keine ergänzenden Abgaben, Kartenzahlung.',()=>{
    allure.story('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, keine angegebenen Adresse, keine bezahlten Abgaben, keine ergänzenden Abgaben, Kartenzahlung.');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      
      
      digitizeLicencePage.getTabPersonalDataComponent().getAddressToggleBoxNoPermanentResidenceButtonIcon().click()
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormOfficeInput().should('be.visible').should('have.value',testUsers.user6.officeAddress.office)
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormDeliverToInput().should('be.visible').should('have.value',`${testUsers.user6.given_name} ${testUsers.user6.family_name}`)
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormStreetInput().should('be.visible').should('have.value',testUsers.user6.officeAddress.street)
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormStreetNumberInput().should('be.visible').should('have.value',testUsers.user6.officeAddress.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormPostcodeInput().should('be.visible').should('have.value',testUsers.user6.officeAddress.postcode)
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormCityInput().should('be.visible').should('have.value',testUsers.user6.officeAddress.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearDown().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })

  it('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Dritten erstellt, mit einer angegebenen Adresse, keine bezahlten Abgaben, keine ergänzenden Abgaben, Kartenzahlung.',()=>{
    allure.story('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Dritten erstellt, mit einer angegebenen Adresse, keine bezahlten Abgaben, keine ergänzenden Abgaben, Kartenzahlung.');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getEditFooterComponent().getFooterConfirmBox().should('be.visible').click()
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearDown().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })

  it('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Dritten erstellt, mit einer angegebenen Adresse, keine bezahlten Abgaben, keine ergänzenden Abgaben, Barzahlung.',()=>{
    allure.story('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Dritten erstellt, mit einer angegebenen Adresse, keine bezahlten Abgaben, keine ergänzenden Abgaben, Barzahlung.');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getEditFooterComponent().getFooterConfirmBox().should('be.visible').click()
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearDown().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible').click()
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })

  it('Einen digitalen Fischereischein erneut ausstellen: vom Dritten erstellt, keine angegebenen Adresse, keine ergänzenden Abgaben, Barzahlung.',()=>{
    allure.story('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Dritten erstellt, mit einer angegebenen Adresse, keine bezahlten Abgaben, keine ergänzenden Abgaben, Barzahlung.');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getEditFooterComponent().getFooterConfirmBox().should('be.visible').click()
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearDown().eq(0).should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible').click()
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })

    allure.step('Fischereischein neu asstellen',()=>{
      headerComponent.getServiceOverviewButton().should('be.visible').click()
      serviceOverviewPage.getFishingLicenceReorderCard().should('be.visible').click()
      consentDigitizePage.getConsentDigitizePage().should('be.visible')
    })
  })
  
  it('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, mit eine bezahlte Abgabe für nächstes Jahr, mit eine falsche ergänzede Abgabe für die übernächstes Jahr und 3 Jahre Gültigkeit, Kartenzahlung.',()=>{
    allure.story('Einen neuen digitalen Fischereischein-Eintrag erstellen: vom Nutzer erstellt, mit einer angegebenen Adresse, mit eine bezahlte Abgabe für nächstes Jahr, mit eine falsche ergänzede Abgabe für die übernächstes Jahr und 3 Jahre Gültigkeit, Kartenzahlung.');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(1).should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains("12 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).should('be.visible').contains("36 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(1).should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptionsButtons().eq(0).should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(2).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(2).click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("36 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(3).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).contains("36 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('36,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('65,00 €')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe die Fehlermeldung im angezeigten Dialog und behebe das Problem',()=>{
      dialogComponent.getDialog().should('be.visible').contains("Prüfen Sie Ihre Eingaben")
      dialogComponent.getDialog().contains("Bei der Eingabe sind Fehler aufgetreten. Bitte prüfen Sie Ihre Eingaben:")
      dialogComponent.getDialog().contains("Die Abgabe darf nicht mehr wie 5 Jahre im Voraus bezahlt werden")
      dialogComponent.getErrorDialogHeader().should('be.visible').contains("Prüfen Sie Ihre Eingaben")
      dialogComponent.getErrorDialogContent().should('be.visible').contains("Bei der Eingabe sind Fehler aufgetreten. Bitte prüfen Sie Ihre Eingaben:")
      dialogComponent.getErrorDialogContent().contains("Die Abgabe darf nicht mehr wie 5 Jahre im Voraus bezahlt werden")
      dialogComponent.getErrorDialogFooter().should('be.visible')
      dialogComponent.getDialogCloseButton().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })

  it('Validierung des Navigationsmenüs',()=>{
    allure.story('Validierung des Navigationsmenüs');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormStreetInput().should('be.visible').type(testCitizen.buerger4.person.address.street)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').type(testCitizen.buerger4.person.address.streetNumber)
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').type(testCitizen.buerger4.person.address.postcode)
      headerComponent.getAllTabTitleState().eq(0).should('have.class', 'active')
      headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(1).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(2).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(4).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(4).should('be.disabled')
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').type(testCitizen.buerger4.person.address.city)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationPage().should('be.visible')
    })

    allure.step('Fülle das Formular "Nachweis" aus',()=>{
      digitizeLicencePage.getQualificationDataComponent().getQualificationValidFrom().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].passedOn)
      digitizeLicencePage.getQualificationDataComponent().getQualificationLegacyNumber().should('be.visible').click()
      digitizeLicencePage.getQualificationDataComponent().getQualificationLicenceIssuer().should('be.visible').type(testCitizen.buerger4.qualificationsProofs[0].issuedBy)
      headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(1).should('have.class', 'active')
      headerComponent.getAllTabTitleState().eq(1).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(2).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(4).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(4).should('be.disabled')
      digitizeLicencePage.getQualificationDataComponent().getQualificationFederalState().should('be.visible').click().type('Sc')
      if(Cypress.env('envName') === 'dev'){
        cy.wait(1000)
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().eq(14).contains(" Schleswig-Holstein ").click()
      }
      else if(Cypress.env('envName') === 'test'){
        digitizeLicencePage.getQualificationDataComponent().getQualificationFederalStateAllOptions().contains(" Schleswig-Holstein ").click()
      }
      digitizeLicencePage.getQualificationDataComponent().getQualificationLinkBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationTemplate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationAntiForgery().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleBox().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleLicence().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleCertificate().should('be.visible')
      digitizeLicencePage.getQualificationDataComponent().getQualificationToggleOther().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Bezahlte Abgaben" aus',()=>{
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTabGroup().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsFormCheckbox().should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(0).should('be.visible').click()
      headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(1).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(2).should('have.class', 'active')
      headerComponent.getAllTabTitleState().eq(2).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(4).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(4).should('be.disabled')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsYearUp().eq(1).should('be.visible').click()
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsLinkBox().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsTemplate().should('be.visible')
      digitizeLicencePage.getPreviousPaymentsComponent().getPreviousPaymentsAntiForgery().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })

    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesPage().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('29,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicence().should('be.visible').contains('0,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesExistingLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('— €')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getPaymentExtraDialog().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).should('be.visible').contains("12 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(1).should('be.visible').contains("36 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(0).should('be.enabled').click()
      digitizeLicencePage.getTaxesComponent().getPaymentDialogYearUp().eq(1).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getPaymentDialogOptions().eq(0).contains("12 €")
      digitizeLicencePage.getTaxesComponent().getPaymentDialogConfirm().should('be.visible').click()
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceConfigure().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains('12,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('41,00 €')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(1).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(2).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(3).should('have.class', 'active')
      headerComponent.getAllTabTitleState().eq(3).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(4).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(4).should('be.disabled')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })

    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      digitizeLicencePage.getDocumentsComponent().getDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('PDF')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(3).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Drucken')
      headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(0).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(1).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(2).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(4).should('have.class', 'active')
      headerComponent.getAllTabTitleState().eq(4).should('not.be.disabled')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(3).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  })

  it('Valiedierung aller Felder auf "Neuen Fischereischein-Eintrag" Seite', () => {
    allure.story('Valiedierung aller Felder auf "Neuen Fischereischein-Eintrag" Seite');
    allure.severity('normal');

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })
    
    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuLicence().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Überprüfe die Validierung der pflichtfelder in Personen Daten Formular', () => {
      headerComponent.getAllTabTitle().first().parent().should('be.visible');
      headerComponent.getAllTabTitle().eq(1).find('button').should('not.be', 'enable');
      digitizeLicencePage.getTabPersonalDataComponent().getTabPersonalData()
        .should('be.visible');
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstname().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastname().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDate().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlace().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormNationalityInput().should('be.visible').clear();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthName().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormNationalityInput()
        .should('have.class', 'ng-invalid');
    });

    allure.step('Überprüfe die Validierung der pflichtfelder in Anschrift Daten Formular', () => {
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBox().should('be.visible');
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressForm().should('be.visible');
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetInput().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormAddressDetailsInput().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormStreetNumberInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormPostCodeInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getToggleBoxAddressFormCityInput()
        .should('have.class', 'ng-invalid');
    });

    allure.step('Überprüfe die Validierung der pflichtfelder im Formular "kein fester Wohnsitz"', () => {
      digitizeLicencePage.getTabPersonalDataComponent().getAddressToggleBoxNoPermanentResidenceButtonIcon()
        .should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentResidenceForm().should('be.visible');
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormOfficeInput().should('be.visible').clear();
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormStreetInput().should('be.visible').clear();
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormStreetNumberInput().should('be.visible').clear();
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormPostcodeInput().should('be.visible').clear();
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormCityInput().should('be.visible').clear();
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormDeliverToInput().should('be.visible').click();
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormOfficeInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormStreetInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormStreetNumberInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormPostcodeInput()
        .should('have.class', 'ng-invalid');
      digitizeLicencePage.getTabPersonalDataComponent().getNoPermanentFormCityInput()
        .should('have.class', 'ng-invalid');
    });
  });

  afterEach(() => {
    allure.step('Als Mitarbeiter abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    });
  })

})
