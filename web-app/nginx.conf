# Expires map
map $sent_http_content_type $expires {
  default off;
  text/html epoch;
  text/css max;
  application/json max;
  application/javascript max;
  ~image/ max;
}

server {
  listen 8080;
  location / {
    # Security headers with Google Fonts Support
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'nonce-$request_id'; style-src 'self' 'nonce-$request_id' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com;";

    sub_filter_once off;
    sub_filter randomNonceGoesHere 'nonce-$request_id';

    root /usr/share/nginx/html;
    index index.html;

    try_files $uri $uri/ /index.html =404;
  }
  expires $expires;
  gzip on;

  access_log /dev/null;
  error_log /dev/stderr;
}
