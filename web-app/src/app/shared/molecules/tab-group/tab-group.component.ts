import { CdkPortalOutlet } from '@angular/cdk/portal';
import { CommonModule } from '@angular/common';
import { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, QueryList, ViewChild, signal } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { TabTitleComponent } from '@/app/shared/atoms/tab-title/tab-title.component';
import { TabComponent } from '@/app/shared/atoms/tab/tab.component';

@Component({
  selector: 'fish-tab-group',
  templateUrl: './tab-group.component.html',
  imports: [CommonModule, TabTitleComponent, CdkPortalOutlet, TranslateModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TabGroupComponent implements AfterContentInit {
  @ContentChildren(TabComponent, { descendants: true })
  protected tabs!: QueryList<TabComponent>;

  protected readonly activeIndex = signal(0);

  @ViewChild(CdkPortalOutlet, { static: true })
  private readonly portalOutlet!: CdkPortalOutlet;

  public ngAfterContentInit(): void {
    this.selectFirstAvailableTab();
  }

  public goNext(): void {
    this.goTo(this.activeIndex() + 1);
  }

  public goBack(): void {
    this.goTo(this.activeIndex() - 1);
  }

  public goToLastTab(): void {
    this.goTo(this.tabs.length - 1);
  }

  public goTo(index: number): void {
    if (index < 0 || index >= this.tabs.length) {
      throw new Error(`Navigation error: Tab index ${index} is out of bounds. Valid indices are between 0 and ${this.tabs.length - 1}.`);
    }
    if (this.tabs.get(index)?.disabled()) {
      throw new Error(`Navigation error: Tab at index ${index} is disabled and cannot be activated.`);
    }
    this.setActiveTab(index);
  }

  protected setActiveTab(index: number): void {
    this.activeIndex.set(index);
    const selectedTab = this.tabs.get(index)!;

    this.portalOutlet.detach();
    this.portalOutlet.attach(selectedTab.templatePortal);
  }

  protected isFirstOfSection(tab: TabComponent): boolean {
    const tabsArray = this.tabs.toArray();
    const tabIndex = tabsArray.indexOf(tab);

    // If it's the first tab in the list the section is not considered
    if (tabIndex === 0) {
      return false;
    }

    // Compare the current tab's section with the previous tab's section
    const previousTab = tabsArray[tabIndex - 1];
    return tab.section !== previousTab.section;
  }

  private selectFirstAvailableTab(): void {
    const firstTabIndex = this.tabs.toArray().findIndex((tab) => !tab.disabled());
    if (firstTabIndex !== -1) {
      this.setActiveTab(firstTabIndex);
    }
  }
}
