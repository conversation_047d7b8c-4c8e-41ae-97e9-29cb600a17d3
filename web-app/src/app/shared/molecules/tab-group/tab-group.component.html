<div class="flex h-fit flex-col pb-6">
  <nav class="flex w-full items-end justify-stretch" data-testid="tab-group" [ariaLabel]="'edit_form.tab_titles.steps' | translate">
    @for (tab of tabs; let i = $index; track i) {
      <div class="flex-1 has-[:focus-visible]:z-10" [ngClass]="isFirstOfSection(tab) ? 'ml-6' : ''">
        <fish-tab-title
          [active]="i === activeIndex()"
          [disabled]="tab.disabled()"
          (clicked)="setActiveTab(i)"
          [dataTestId]="tab.labelButtonDataTestId()"
        >
          {{ tab.label() }}
        </fish-tab-title>
      </div>
    }
  </nav>

  <div class="relative mt-6 flex-1">
    <ng-template cdkPortalOutlet></ng-template>
  </div>
</div>
