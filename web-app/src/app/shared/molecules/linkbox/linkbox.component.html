<div class="flex w-full flex-col rounded bg-background shadow-glass-white-tint">
  <div class="inline-flex h-8 items-center justify-center border-b border-b-border-divider px-6 text-s font-bold">
    {{ header }}
  </div>
  <div
    class="grid divide-border-divider"
    [ngClass]="{
      'grid-flow-col divide-x': direction === 'horizontal',
      'grid-flow-row divide-y': direction === 'vertical',
    }"
  >
    <ng-content></ng-content>
  </div>
  <div *ngIf="isEmpty" class="inline-flex items-center justify-center px-6 py-4">
    <div class="-mx-20 w-full text-s font-normal leading-normal text-font-secondary">
      {{ alternativeText }}
    </div>
  </div>
</div>
