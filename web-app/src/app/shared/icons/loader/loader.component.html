<div role="status" aria-live="polite" class="flex items-center gap-2">
  <svg focusable="false" aria-hidden="true" width="48" height="48" viewBox="0 0 48 48" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
    <path
      opacity="0.92"
      d="M24.0539 8C27.0708 8.01016 30.0234 8.87309 32.571 10.4892C35.1186 12.1054 37.1573 14.4089 38.452 17.134C39.7466 19.859 40.2444 22.8946 39.8879 25.8904C39.5315 28.8863 38.3352 31.7203 36.4373 34.0654C34.5393 36.4106 32.017 38.1714 29.1613 39.1446C26.3056 40.1178 23.2329 40.2638 20.2978 39.5657C17.3627 38.8676 14.6848 37.3539 12.5731 35.1992C10.4613 33.0446 9.0018 30.3367 8.36291 27.3882L10.5521 26.9138C11.1016 29.4496 12.3567 31.7783 14.1729 33.6313C15.989 35.4843 18.292 36.7861 20.8161 37.3865C23.3403 37.9868 25.9828 37.8613 28.4387 37.0243C30.8946 36.1874 33.0638 34.6731 34.6961 32.6563C36.3283 30.6394 37.3571 28.2022 37.6636 25.6258C37.9702 23.0493 37.5421 20.4387 36.4287 18.0952C35.3153 15.7516 33.562 13.7706 31.3711 12.3807C29.1802 10.9908 26.6409 10.2487 24.0463 10.24L24.0539 8Z"
      fill="currentColor"
    />
  </svg>
  @if (loading()) {
    <span class="sr-only" [innerText]="'common.loading.start' | translate"></span>
  } @else {
    <span class="sr-only" [innerText]="'common.loading.finish' | translate"></span>
  }
</div>
