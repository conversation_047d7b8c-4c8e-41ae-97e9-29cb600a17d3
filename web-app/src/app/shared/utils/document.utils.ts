import { IdentificationDocument, IdentificationDocumentType, LicenseType } from '@digifischdok/ngx-register-sdk';

export function isTaxPDFDocument(document: IdentificationDocument): boolean {
  return !!document.tax && document.type === IdentificationDocumentType.Pdf;
}

export function isVisibleLicensePDFDocument(document: IdentificationDocument): boolean {
  return (
    !!document.fishingLicense &&
    document.type === IdentificationDocumentType.Pdf &&
    (document.fishingLicense.type === LicenseType.Regular ? document.validTo === null || new Date(document.validTo) >= new Date() : true)
  );
}

export function isLimitedLicenseApprovalDocument(document: IdentificationDocument): boolean {
  return !!document.limitedLicenseApproval && document.type === IdentificationDocumentType.Pdf;
}

export function isLimitedLicensePDFDocument(document: IdentificationDocument): boolean {
  return !!document.fishingLicense && document.type === IdentificationDocumentType.Pdf && document.fishingLicense.type === LicenseType.Limited;
}

export function isRelevantTaxDocument(document: IdentificationDocument): boolean {
  const oneYearAgo = new Date(new Date().getFullYear() - 1, 11, 31);
  return (
    // show only PDF documents
    document.type === IdentificationDocumentType.Pdf &&
    // that contain tax info
    !!document.tax &&
    // and have no expiration date or expire after one year ago
    (!document.validTo || new Date(document.validTo) > oneYearAgo)
  );
}
