<fish-focus-ring [class]="focusRingClasses">
  <button
    #buttonElement
    [class]="buttonClasses"
    [disabled]="disabled"
    (click)="onButtonClick()"
    [routerLink]="routeTo()"
    role="button"
    [attr.aria-disabled]="disabled"
    [attr.aria-label]="ariaLabel()"
    [title]="ariaLabel()"
  >
    @if (loading) {
      <fish-icon-loader [loading]="loading" class="animate-spin"></fish-icon-loader>
    } @else {
      <ng-content select="[icon]"></ng-content>
      <span class="whitespace-nowrap px-2 empty:px-0" [ngClass]="responsive() ? 'hidden lg:block' : ''">
        <ng-content />
      </span>
    }
  </button>
</fish-focus-ring>
