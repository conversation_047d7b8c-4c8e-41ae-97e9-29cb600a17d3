import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, HostBinding, Input, OnChanges, Output, input, viewChild } from '@angular/core';
import { RouterLink } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';
import { twMerge } from 'tailwind-merge';

import { OverlayStore } from '@/app/core/stores/overlay.store';
import { ButtonShape, ButtonSize, ButtonType, IconPosition } from '@/app/shared/atoms/button/button.models';
import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { IconLoaderComponent } from '@/app/shared/icons/loader/loader.component';

import { buttonVariants } from './button.component.styles';

@Component({
  selector: 'fish-button',
  templateUrl: './button.component.html',
  imports: [FocusRingComponent, IconLoaderComponent, TranslateModule, RouterLink, NgClass],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ButtonComponent implements OnChanges {
  @Input()
  public iconPosition?: IconPosition;

  @Input()
  public type: ButtonType = 'primary';

  @Input()
  public size: ButtonSize = 'm';

  @Input()
  public disabled: boolean = false;

  @Input()
  public loading: boolean = false;

  @Input()
  public shape: ButtonShape = 'rounded';

  public readonly loadingShape = input<ButtonShape>('pill');

  public readonly responsive = input<boolean>(false);

  public readonly ariaLabel = input<string>('');

  @Input()
  public class?: string;

  /**
   * Specifies the navigation path to route To when this button is clicked.
   *
   * For accessibility reasons the routerLink directive must not be used, since this would make the parent element selectable too.
   *
   * Usage together with `clicked` might lead to race conditions and does not ensure both events are fired correctly.
   */
  public readonly routeTo = input<unknown[] | string | undefined | null>(undefined);

  @Output()
  public readonly clicked = new EventEmitter<void>();

  /**
   * Reference to the underlying HTML button element.
   * This allows parent components to access the button element directly
   * without using querySelector.
   */
  public readonly nativeButtonElement = viewChild.required<ElementRef<HTMLButtonElement>>('buttonElement');

  constructor(private readonly overlayStore: OverlayStore) {}

  protected onButtonClick(): void {
    if (!this.loading && !this.disabled) {
      this.clicked.emit();
    }
  }

  protected get buttonClasses(): string {
    return twMerge(
      buttonVariants({
        iconPosition: this.loading ? 'left' : this.iconPosition,
        size: this.size,
        type: this.type,
        shape: this.loading ? this.loadingShape() : this.shape,
      })
    );
  }

  protected get focusRingClasses(): string {
    return twMerge('w-full', this.loading && 'z-[10000]');
  }

  @HostBinding('class')
  protected get hostClass(): string {
    return twMerge('block w-fit', this.class);
  }

  public ngOnChanges(): void {
    this.overlayStore.setShown(this.loading);
  }
}
