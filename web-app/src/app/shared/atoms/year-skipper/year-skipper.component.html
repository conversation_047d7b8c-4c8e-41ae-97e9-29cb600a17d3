<fish-focus-ring class="w-full">
  <div [class]="containerClasses" role="group">
    <button
      (click)="decrement()"
      [attr.aria-label]="'year_skipper.decrement' | translate"
      [class]="decrementButtonClasses"
      [disabled]="decrementButtonDisabled"
      data-testid="skipper-down"
    >
      <fish-icon-chevron-left icon size="48" />
    </button>
    <div tabindex="-1" [class]="displayClasses">
      {{ isDuration ? (durationDisplayValue$ | async) : control.value }}
    </div>
    <button
      (click)="increment()"
      [attr.aria-label]="'year_skipper.increment' | translate"
      [class]="incrementButtonClasses"
      [disabled]="incrementButtonDisabled"
      data-testid="skipper-up"
    >
      <fish-icon-chevron-right icon size="48" />
    </button>

    <input
      tabindex="0"
      (keydown)="onKeyDown($event)"
      [attr.aria-label]="label"
      [attr.aria-invalid]="control.invalid"
      [attr.aria-valuemax]="max"
      [attr.aria-valuemin]="min"
      [attr.aria-required]="required()"
      [formControl]="control"
      [max]="max"
      [min]="min"
      [step]="step"
      [id]="id()"
      class="sr-only"
      type="number"
      readonly
    />
  </div>
  <div [class]="borderClasses"></div>
</fish-focus-ring>
