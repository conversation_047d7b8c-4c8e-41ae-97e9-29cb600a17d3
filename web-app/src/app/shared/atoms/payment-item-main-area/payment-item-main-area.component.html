<ng-template #content>
  <ng-content />
</ng-template>

<div [class]="containerClasses()" [ngClass]="{ 'text-action-text-disabled': disabled() }">
  @if (hasCheckbox()) {
    <fish-checkbox [externalFocusRing]="true" [formControl]="control()!" class="gap-6">
      <div class="grow">
        <div class="flex w-full items-center justify-between">
          <ng-container *ngTemplateOutlet="content" />
        </div>
      </div>
    </fish-checkbox>
  }

  <div class="empty:hidden">
    <ng-content select="[icon]" />
  </div>

  @if (!hasCheckbox()) {
    <div class="grow">
      <div class="flex w-full items-center justify-between">
        <ng-container *ngTemplateOutlet="content" />
      </div>
    </div>
  }
</div>
