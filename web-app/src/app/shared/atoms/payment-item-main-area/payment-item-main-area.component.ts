import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, OnInit, computed, input, signal } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';

import { twMerge } from 'tailwind-merge';

import { CheckboxComponent } from '@/app/shared/atoms/checkbox/checkbox.component';
import { containerClasses } from '@/app/shared/atoms/payment-item-main-area/payment-item-main-area.component.styles';

@Component({
  selector: 'fish-payment-item-main-area',
  imports: [CheckboxComponent, NgTemplateOutlet, NgClass],
  templateUrl: './payment-item-main-area.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaymentItemMainAreaComponent implements OnInit, AfterViewInit {
  // Inputs
  public class = input<string>();

  public control = input<FormControl<boolean | null>>(this.formBuilder.control(false));

  public hasCheckbox = input<boolean>(true);

  // Fields
  protected disabled = signal<boolean>(false);

  constructor(private readonly formBuilder: FormBuilder) {}

  public ngOnInit(): void {
    this.control().statusChanges.subscribe((status) => {
      this.disabled.set(status === 'DISABLED');
    });
  }

  public ngAfterViewInit(): void {
    this.disabled.set(this.control().status === 'DISABLED');
  }

  protected readonly containerClasses = computed(() => {
    return twMerge(containerClasses(), this.class());
  });
}
