<fish-focus-ring class="w-full" data-testid="tab-title">
  <!-- the 'active' or 'inactive' classes are behavioural classes and should have nothing to do with the styling  -->
  <button
    [class]="titleClass"
    [ngClass]="{ active: active(), inactive: !active() }"
    (click)="clicked.emit()"
    [disabled]="disabled()"
    [attr.data-testid]="dataTestId()"
    role="link"
    [attr.aria-current]="active() ? 'page' : null"
  >
    <ng-content />
  </button>
</fish-focus-ring>
