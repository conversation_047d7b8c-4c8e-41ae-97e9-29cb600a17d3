import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'fish-license-cardbox',
  imports: [TranslateModule],
  templateUrl: './license-cardbox.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LicenseCardboxComponent {
  public readonly imageSrc = input<string>('');

  public readonly title = input<string>('');

  public readonly text = input<string>('');
}
