import { Meta, StoryObj, argsToTemplate } from '@storybook/angular';

import { LicenseCardboxComponent } from '@/app/shared/atoms/license-cardbox/license-cardbox.component';

// Meta configuration for the LicenseCardbox component
const meta: Meta<LicenseCardboxComponent> = {
  title: 'LicenseCardbox',
  component: LicenseCardboxComponent,
  render: (args) => ({
    props: { ...args },
    template: `
            <div class="flex justify-center">
              <div class="w-full lg:w-96 ">
                  <fish-license-cardbox ${argsToTemplate(args)}></fish-license-cardbox>
              </div>
            </div>
        `,
  }),
  args: {
    imageSrc: '/assets/license-card-preview/license-card-regular.png',
    title: 'Fischereischein Scheckkarte',
    text: '<p> Wird innerhalb der nächsten <b>7 bis 8 Wochen</b> automatisch zugestellt. </p> <p> Wurde <b>keine <PERSON></b> ange<PERSON>ben, wird sie der <b>ausstellenden Behörde</b> zugesendet. </p>',
  },
};

export default meta;

type Story = StoryObj<LicenseCardboxComponent>;

// Default story for LicenseCardbox
export const LicenseCardbox: Story = {};

// Story with Alternative Content
export const AlternativeContent: Story = {
  render: (args) => ({
    props: { ...args },
    template: `
            <div class="w-[25em]">
                <fish-license-cardbox
                    imageSrc="/assets/license-card-preview/license-card-limited.png"
                    title="Alternative Title"
                    text="<h1>Welcome to My Styled Cardbox</h1><p><b>This is a bold paragraph.</b> The quick brown fox jumps over the lazy dog.</p><p><i>This is an italicized paragraph.</i> The quick brown fox jumps over the lazy dog.</p>"
                ></fish-license-cardbox>
            </div>
        `,
  }),
};
