import { ChangeDetectionStrategy, Component, Signal, computed } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { CitizenStore, ICitizenProfile } from '@/app/core/stores/citizen.store';

@Component({
  selector: 'fish-profile-bar',
  imports: [TranslateModule],
  templateUrl: './profile-bar.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileBarComponent {
  constructor(private readonly citizenStore: CitizenStore) {}

  protected readonly citizenProfile: Signal<ICitizenProfile | undefined> = computed(() => {
    return this.citizenStore.profile() ?? undefined;
  });
}
