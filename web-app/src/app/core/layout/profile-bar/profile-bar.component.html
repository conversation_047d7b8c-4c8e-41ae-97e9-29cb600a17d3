<div class="w-full border-b-2 border-t-2 border-border-divider">
  <div class="grid grid-flow-col items-center justify-items-stretch divide-x-2 divide-border-divider truncate py-1 text-font-secondary">
    <div class="flex justify-center gap-1.5">
      <span>{{ 'profile.info.birthdate' | translate }}</span>
      <span
        class="max-w-10 overflow-hidden overflow-ellipsis font-bold md:max-w-24 lg:max-w-48 xl:max-w-sm"
        title="{{ citizenProfile()?.person?.birthdate || '—' }}"
      >
        {{ citizenProfile()?.person?.birthdate || '—' }}
      </span>
    </div>

    <div class="flex justify-center gap-1.5">
      <span>{{ 'profile.info.birthname' | translate }}</span>
      <span
        class="max-w-10 overflow-hidden overflow-ellipsis font-bold md:max-w-24 lg:max-w-48 xl:max-w-sm"
        title="{{ citizenProfile()?.person?.birthname || '—' }}"
      >
        {{ citizenProfile()?.person?.birthname || '—' }}
      </span>
    </div>

    <div class="flex justify-center gap-1.5">
      <span>{{ 'profile.info.birthplace' | translate }}</span>
      <span
        class="max-w-10 overflow-hidden overflow-ellipsis font-bold md:max-w-24 lg:max-w-48 xl:max-w-sm"
        title="{{ citizenProfile()?.person?.birthplace || '—' }}"
      >
        {{ citizenProfile()?.person?.birthplace || '—' }}
      </span>
    </div>

    <div class="flex justify-center gap-1.5">
      <span>{{ 'profile.info.federal_state' | translate }}</span>
      <span
        class="max-w-10 overflow-hidden overflow-ellipsis font-bold md:max-w-24 lg:max-w-48 xl:max-w-sm"
        title="{{ citizenProfile()?.jurisdiction?.federalState || '—' }}"
      >
        {{ citizenProfile()?.jurisdiction?.federalState || '—' }}
      </span>
    </div>
  </div>
</div>
