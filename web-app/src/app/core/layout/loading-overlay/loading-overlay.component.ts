import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { OverlayStore } from '@/app/core/stores/overlay.store';

@Component({
  selector: 'fish-loading-overlay',
  imports: [NgClass, TranslateModule],
  templateUrl: './loading-overlay.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoadingOverlayComponent {
  constructor(private readonly overlayStore: OverlayStore) {}

  protected get isShown(): boolean {
    return this.overlayStore.isShown();
  }
}
