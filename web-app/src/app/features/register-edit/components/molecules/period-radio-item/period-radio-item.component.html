<fish-focus-ring shadowClass="rounded-lg" class="w-full rounded">
  <div class="group w-full rounded-lg bg-background">
    <div [class]="containerClasses()">
      <div class="flex min-w-0 flex-1 basis-0 items-center gap-6">
        <fish-radio [name]="name()" [value]="value()" [formControl]="radioControl()" [externalFocusRing]="true"></fish-radio>
        <span
          class="text-base font-bold group-has-[:checked]:text-action-primary"
          [ngClass]="{ 'text-action-text-disabled': disabled() }"
          [innerText]="label()"
        ></span>
      </div>
      @if (startDateControl() || endDateControl()) {
        <div class="flex flex-1 basis-0 flex-wrap justify-end gap-4">
          @if (startDateControl()) {
            <fish-form-field
              data-testid="period-radio-item-start-date-input"
              type="date"
              [label]="startDateLabel()"
              [control]="startDateControl()!"
              [showOptionalLabel]="false"
              [errorMapping$]="startDateErrorMapping$()"
              class="w-[260px]"
            >
            </fish-form-field>
          }
          @if (endDateControl()) {
            <fish-form-field
              data-testid="period-radio-item-end-date-input"
              type="date"
              [label]="endDateLabel()"
              [control]="endDateControl()!"
              [showOptionalLabel]="false"
              [errorMapping$]="endDateErrorMapping$()"
              class="w-[260px]"
            >
            </fish-form-field>
          }
        </div>
      }
    </div>
  </div>
</fish-focus-ring>
