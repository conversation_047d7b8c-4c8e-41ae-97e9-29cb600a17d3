<fish-card data-testid="limited-license-approval-document-preview-box">
  <fish-card-header variant="secondary">
    <span class="text-l font-thin" [innerText]="'edit_form.limited_license_approval.document_preview_box.title' | translate"></span>
  </fish-card-header>
  <fish-card-content variant="pattern">
    <fish-payment-item [class]="'min-h-16'">
      <fish-payment-item-main-area [class]="'px-2'" [hasCheckbox]="false">
        <div class="flex w-full items-center">
          <div class="flex items-center gap-4">
            <fish-icon-document-pdf size="48"></fish-icon-document-pdf>
            <div class="flex-col text-s leading-5">
              <div [innerText]="'document_item.type.pdf' | translate"></div>
              <div class="font-bold" [innerText]="'edit_form.limited_license_approval.document_preview_box.document_name' | translate"></div>
            </div>
          </div>
          <div class="grow"></div>
        </div>
      </fish-payment-item-main-area>
    </fish-payment-item>
  </fish-card-content>
  <fish-card-footer>
    <div class="flex flex-col justify-center gap-4">
      <fish-button
        [loading]="isPreviewButtonLoading()"
        [type]="'secondary'"
        (clicked)="previewButtonClicked.emit()"
        data-testid="limited-license-approval-document-preview-box-show-document-button"
      >
        <fish-icon-link icon></fish-icon-link>
        <span [innerText]="'edit_form.limited_license_approval.document_preview_box.show_document_button' | translate"></span>
      </fish-button>
      @if (showValidationErrorMessage()) {
        <fish-form-error-message class="max-w-72" data-testid="limited-license-approval-document-preview-box-validation-error-message">
          <span [innerText]="'edit_form.limited_license_approval.document_preview_box.validation_error_message' | translate"></span>
        </fish-form-error-message>
      }
    </div>
  </fish-card-footer>
</fish-card>
