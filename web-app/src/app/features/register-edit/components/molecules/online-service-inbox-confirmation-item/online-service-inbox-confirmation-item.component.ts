import { ChangeDetectionStrategy, Component } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { PaymentItemMainAreaComponent } from '@/app/shared/atoms/payment-item-main-area/payment-item-main-area.component';
import { IconServicekontoComponent } from '@/app/shared/icons/servicekonto/servicekonto.component';
import { IconSuccessComponent } from '@/app/shared/icons/success/success.component';
import { PaymentItemComponent } from '@/app/shared/molecules/payment-item/payment-item.component';

@Component({
  selector: 'fish-online-service-inbox-confirmation-item',
  imports: [IconServicekontoComponent, PaymentItemComponent, PaymentItemMainAreaComponent, TranslateModule, IconSuccessComponent],
  templateUrl: './online-service-inbox-confirmation-item.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OnlineServiceInboxConfirmationItemComponent {}
