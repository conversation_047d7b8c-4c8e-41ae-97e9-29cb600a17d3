import { ChangeDetectionStrategy, Component, inject, output, signal } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, finalize, map } from 'rxjs';

import { DeletionReason, RegisterEntryService } from '@digifischdok/ngx-register-sdk';

import { RouteParamService } from '@/app/core/services/route-param.service';
import { DeletePersonFormGroup } from '@/app/features/register-edit/components/organisms/delete-person-form/delete-person-form.models';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { FormErrorMessageComponent } from '@/app/shared/atoms/form-error-message/form-error-message.component';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { IconDeleteComponent } from '@/app/shared/icons/delete/delete.component';
import { ConfirmBoxComponent } from '@/app/shared/molecules/confirm-box/confirm-box.component';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';

@Component({
  selector: 'fish-delete-person-form',
  imports: [TranslateModule, ConfirmBoxComponent, ButtonComponent, IconDeleteComponent, FormErrorMessageComponent],
  templateUrl: './delete-person-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeletePersonFormComponent extends FormComponent<DeletePersonFormGroup> {
  // Outputs
  public formSent = output<void>();

  // Dependencies
  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  private readonly registerEntryService: RegisterEntryService = inject(RegisterEntryService);

  private readonly routeParamService: RouteParamService = inject(RouteParamService);

  private readonly route: ActivatedRoute = inject(ActivatedRoute);

  private readonly serverDialogService: ServerDialogService = inject(ServerDialogService);

  // Fields
  public override formGroup!: DeletePersonFormGroup;

  protected readonly isLoading = signal(false);

  constructor() {
    super();
    this.initFormGroup();
  }

  private initFormGroup(): void {
    this.formGroup = this.formBuilder.group({
      deletionReason: this.formBuilder.control<DeletionReason | null>(null, [Validators.required]),
      gdprRequest: this.formBuilder.nonNullable.control(false),
      gdprRequestExceptCertificate: this.formBuilder.nonNullable.control(false),
      personDeceased: this.formBuilder.nonNullable.control(false),
    });

    this.formGroup.controls.gdprRequest.valueChanges.subscribe((selected: boolean) => {
      if (selected) {
        this.formGroup.controls.gdprRequestExceptCertificate.setValue(false, { emitEvent: false });
        this.formGroup.controls.personDeceased.setValue(false, { emitEvent: false });
      }
      this.formGroup.controls.deletionReason.setValue(selected ? DeletionReason.GdprRequest : null);

      this.setErrorsIfReasonNotSelected();
    });

    this.formGroup.controls.gdprRequestExceptCertificate.valueChanges.subscribe((selected: boolean) => {
      if (selected) {
        this.formGroup.controls.gdprRequest.setValue(false, { emitEvent: false });
        this.formGroup.controls.personDeceased.setValue(false, { emitEvent: false });
      }
      this.formGroup.controls.deletionReason.setValue(selected ? DeletionReason.GdprRequestExceptCertificate : null);

      this.setErrorsIfReasonNotSelected();
    });

    this.formGroup.controls.personDeceased.valueChanges.subscribe((selected: boolean) => {
      if (selected) {
        this.formGroup.controls.gdprRequest.setValue(false, { emitEvent: false });
        this.formGroup.controls.gdprRequestExceptCertificate.setValue(false, { emitEvent: false });
      }
      this.formGroup.controls.deletionReason.setValue(selected ? DeletionReason.PersonDeceased : null);

      this.setErrorsIfReasonNotSelected();
    });
  }

  private setErrorsIfReasonNotSelected(): void {
    Object.values(this.formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (!this.formGroup.controls.deletionReason.value) {
        control.setErrors({ reason: true });
      }
    });
  }

  protected confirmDelete(): void {
    this.setErrorsIfReasonNotSelected();
    if (this.formGroup.valid) {
      this.isLoading.set(true);

      const deletionReason = this.formGroup.controls.deletionReason.value;
      const registerEntryId: string = this.routeParamService.getParamOrFail('registerEntryId', this.route);

      this.registerEntryService
        .registerEntriesControllerDelete(registerEntryId, deletionReason!, 'response')
        .pipe(
          map((response) => response.body),
          catchError((error: unknown) => {
            this.serverDialogService.handleServerError(error);
          }),
          finalize(() => {
            this.isLoading.set(false);
          })
        )
        .subscribe(() => {
          setTimeout(() => this.formSent.emit(), 1);
        });
    }
  }
}
