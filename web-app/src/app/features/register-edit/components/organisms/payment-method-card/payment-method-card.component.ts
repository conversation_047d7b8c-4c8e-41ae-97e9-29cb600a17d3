import { ChangeDetectionStrategy, Component, OnInit, computed, effect, inject, input, signal } from '@angular/core';
import { Form<PERSON>uilder, FormControl, Validators } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { combineLatest, pairwise, startWith } from 'rxjs';

import { PaymentType } from '@digifischdok/ngx-register-sdk';

import { PaymentMethodCardFormGroup } from '@/app/features/register-edit/components/organisms/payment-method-card/payment-method-card.models';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { PaymentItemMainAreaComponent } from '@/app/shared/atoms/payment-item-main-area/payment-item-main-area.component';
import { FishBeforeUnloadAndCanDeactivate } from '@/app/shared/decorators/before-unload-and-can-deactivate/before-unload-and-can-deactivate.decorator';
import { IconWarningComponent } from '@/app/shared/icons/warning/warning.component';
import { CardComponent } from '@/app/shared/molecules/card/card.component';
import { PaymentItemComponent } from '@/app/shared/molecules/payment-item/payment-item.component';

@FishBeforeUnloadAndCanDeactivate()
@Component({
  selector: 'fish-payment-method-card',
  imports: [
    CardComponent,
    CardHeaderComponent,
    TranslateModule,
    CardContentComponent,
    PaymentItemComponent,
    PaymentItemMainAreaComponent,
    IconWarningComponent,
  ],
  templateUrl: './payment-method-card.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaymentMethodCardComponent extends FormComponent<PaymentMethodCardFormGroup> implements OnInit {
  // Inputs
  public readonly isRequired = input<boolean>(false);

  public readonly enabledPaymentTypes = input<Omit<PaymentType, 'ONLINE'>[]>([PaymentType.Card, PaymentType.Cash]);

  // Fields
  public override formGroup!: PaymentMethodCardFormGroup;

  private paymentMethodControl!: FormControl<PaymentType | null>;

  protected cardOptionControl!: FormControl<boolean>;

  protected cashOptionControl!: FormControl<boolean>;

  protected bankTransferOptionControl!: FormControl<boolean>;

  protected readonly isValid = signal<boolean>(true);

  protected isCardOptionEnabled = computed<boolean>(() => this.enabledPaymentTypes().includes(PaymentType.Card));

  protected isCashOptionEnabled = computed<boolean>(() => this.enabledPaymentTypes().includes(PaymentType.Cash));

  protected isBankTransferOptionEnabled = computed<boolean>(() => this.enabledPaymentTypes().includes(PaymentType.BankTransfer));

  // Dependencies
  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  constructor() {
    super();
    effect(() => {
      if (this.paymentMethodControl) {
        if (this.isRequired()) {
          this.paymentMethodControl.setValidators([Validators.required]);

          this.cardOptionControl.enable();
          this.cashOptionControl.enable();
          this.bankTransferOptionControl.enable();
        } else {
          this.paymentMethodControl.setValidators(null);

          this.cardOptionControl.disable();
          this.cashOptionControl.disable();
          this.bankTransferOptionControl.disable();
          this.cardOptionControl.setValue(false);
          this.cashOptionControl.setValue(false);
          this.bankTransferOptionControl.setValue(false);
        }
        this.paymentMethodControl.updateValueAndValidity();
      }
    });
  }

  public override ngOnInit(): void {
    this.initFormGroup();
    this.captureInitialState();
    this.initSubControls();
    super.ngOnInit();
  }

  private initFormGroup(): void {
    this.paymentMethodControl = this.formBuilder.control<PaymentType | null>(null);
    this.formGroup = this.formBuilder.group({
      paymentMethod: this.paymentMethodControl,
    });
    this.formGroup.statusChanges.subscribe(() => {
      this.isValid.set(this.paymentMethodControl.valid || !this.paymentMethodControl.touched);
    });
  }

  private initSubControls(): void {
    this.cardOptionControl = this.formBuilder.nonNullable.control(false);
    this.cashOptionControl = this.formBuilder.nonNullable.control(false);
    this.bankTransferOptionControl = this.formBuilder.nonNullable.control(false);

    combineLatest([
      this.cashOptionControl.valueChanges.pipe(startWith(false)),
      this.cardOptionControl.valueChanges.pipe(startWith(false)),
      this.bankTransferOptionControl.valueChanges.pipe(startWith(false)),
    ])
      .pipe(pairwise())
      .subscribe(([[prevCashActive, prevCardActive, prevBankTransferActive], [cashActive, cardActive, bankTransferActive]]) => {
        // Count how many options are currently active
        const activeCount = [cashActive, cardActive, bankTransferActive].filter(Boolean).length;

        // If more than one option is active, uncheck the others except the most recently changed one
        if (activeCount > 1) {
          if (cashActive && cashActive !== prevCashActive) {
            this.cardOptionControl.setValue(false);
            this.bankTransferOptionControl.setValue(false);
          } else if (cardActive && cardActive !== prevCardActive) {
            this.cashOptionControl.setValue(false);
            this.bankTransferOptionControl.setValue(false);
          } else if (bankTransferActive && bankTransferActive !== prevBankTransferActive) {
            this.cashOptionControl.setValue(false);
            this.cardOptionControl.setValue(false);
          }
          return;
        }

        // Set the payment method based on which option is active
        if (cashActive) {
          this.paymentMethodControl.setValue(PaymentType.Cash);
        } else if (cardActive) {
          this.paymentMethodControl.setValue(PaymentType.Card);
        } else if (bankTransferActive) {
          this.paymentMethodControl.setValue(PaymentType.BankTransfer);
        } else {
          this.paymentMethodControl.setValue(null);
        }
        this.formGroup.updateValueAndValidity();
      });
  }
}
