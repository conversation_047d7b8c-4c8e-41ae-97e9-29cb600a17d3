<div class="flex flex-col justify-between gap-12">
  <div class="flex flex-col gap-6">
    <div class="flex flex-col pl-4">
      <div class="text-base font-bold leading-10">
        {{ 'edit_form.qualification_proof.text' | translate }}
      </div>
      <span [innerHTML]="'edit_form.qualification_proof.subtext' | translate" class="text-s leading-normal"></span>
    </div>
    <div class="grid grid-cols-1 gap-4 lg:grid-cols-[4fr_1fr]">
      <fish-qualification-proof-toggle-box
        (tabChanged)="handleToggleBoxTabChanged($event)"
        (validated)="handleToggleBoxValidated($event)"
        (valueChanged)="handleToggleBoxValuesChanged($event)"
        class="w-full"
        data-testid="qualification-proof-toggle-box"
      ></fish-qualification-proof-toggle-box>

      @if (currentTabIndex() === QualificationProofToggleBoxComponent.TAB_INDEX_OTHER) {
        <fish-qualification-proof-other-infobox
          class="w-full"
          data-testid="qualification-proof-other-infobox"
        ></fish-qualification-proof-other-infobox>
      } @else {
        <fish-qualification-proof-linkbox
          [federalStateId]="selectedFederalStateId"
          [qualificationProofType]="selectedQualificationProofType"
          class="w-full"
          data-testid="qualification-proof-linkbox"
        ></fish-qualification-proof-linkbox>
      }
    </div>
  </div>

  <fish-edit-footer (backed)="backButtonClicked.emit()" (continued)="onContinue()" [showBackButton]="true" />
</div>
