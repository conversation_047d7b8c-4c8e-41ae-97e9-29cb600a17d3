import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { Form<PERSON><PERSON>er, Validators } from '@angular/forms';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Observable, forkJoin, map } from 'rxjs';

import { PreviousPaymentsFormGroup } from '@/app/features/register-edit/components/organisms/previous-payments-form/previous-payments-form.models';
import { validDateRange } from '@/app/features/register-edit/components/organisms/previous-payments-form/previous-payments-form.validators';
import { ErrorLabelComponent } from '@/app/shared/atoms/error-label/error-label.component';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { PaymentItemActionAreaComponent } from '@/app/shared/atoms/payment-item-action-area/payment-item-action-area.component';
import { PaymentItemMainAreaComponent } from '@/app/shared/atoms/payment-item-main-area/payment-item-main-area.component';
import { FishBeforeUnloadAndCanDeactivate } from '@/app/shared/decorators/before-unload-and-can-deactivate/before-unload-and-can-deactivate.decorator';
import { PaymentItemComponent } from '@/app/shared/molecules/payment-item/payment-item.component';
import { FormFieldComponent } from '@/app/shared/organisms/form-field/form-field.component';
import { ValidationErrorMapping } from '@/app/shared/organisms/form-field/form-field.models';

@FishBeforeUnloadAndCanDeactivate()
@Component({
  selector: 'fish-previous-payments-form',
  imports: [
    FormFieldComponent,
    TranslateModule,
    PaymentItemComponent,
    PaymentItemActionAreaComponent,
    PaymentItemMainAreaComponent,
    ErrorLabelComponent,
  ],
  templateUrl: './previous-payments-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PreviousPaymentsFormComponent extends FormComponent<PreviousPaymentsFormGroup> implements OnInit {
  // Fields
  public override formGroup!: PreviousPaymentsFormGroup;

  protected dateErrorMappings$!: Observable<ValidationErrorMapping>;

  protected readonly currentYear = new Date().getFullYear();

  protected readonly minYear = this.currentYear; // payed taxes can be only registered from the current year

  protected readonly maxYear = this.currentYear + 10; // and up to 10 years in the future

  // Dependencies
  private readonly translate: TranslateService = inject(TranslateService);

  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  constructor() {
    super();
    this.initFormGroup();
    this.captureInitialState();
  }

  private initFormGroup(): void {
    this.formGroup = this.formBuilder.group({
      validFrom: this.formBuilder.nonNullable.control({ disabled: true, value: this.currentYear }, [
        Validators.min(this.minYear),
        Validators.max(this.maxYear),
        Validators.required,
        validDateRange,
      ]),
      validTo: this.formBuilder.nonNullable.control({ disabled: true, value: this.currentYear }, [
        Validators.min(this.currentYear),
        Validators.max(this.maxYear),
        Validators.required,
      ]),
      previouslyPayedTaxChecked: this.formBuilder.nonNullable.control(false),
    });

    // Update validTo enabled state and validators based on previousPaymentsAvailable value
    this.formGroup.controls.previouslyPayedTaxChecked.valueChanges.subscribe((value) => {
      if (value) {
        this.formGroup.controls.validFrom.enable();
        this.formGroup.controls.validTo.enable();
      } else {
        this.formGroup.controls.validFrom.disable();
        this.formGroup.controls.validTo.disable();
      }
      this.formGroup.updateValueAndValidity();
    });

    this.formGroup.controls.validTo.valueChanges.subscribe(() => {
      this.formGroup.controls.validFrom.updateValueAndValidity(); // so the validFrom validator updates as well when the validTo changes
    });

    this.dateErrorMappings$ = forkJoin([this.translate.get('edit_form.previous_payments.error.valid_date_range')]).pipe(
      map(([validDateRangeError]) => ({
        validDateRange: validDateRangeError,
      }))
    );
  }
}
