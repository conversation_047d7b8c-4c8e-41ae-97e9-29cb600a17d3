<div class="relative h-16 w-full">
  <label [for]="'search-bar'" class="sr-only" [innerText]="'search.bar.label' | translate"></label>
  <fish-input
    [id]="'search-bar'"
    data-testid="search-bar-input"
    (submitted)="handleSearch()"
    [control]="inputControl"
    [placeholder]="'search.bar.placeholder' | translate"
    class="absolute w-full"
    size="large"
    variant="searchBar"
  >
  </fish-input>
  <div class="absolute right-2 top-2 flex gap-2">
    <fish-button
      data-testid="do-clear-button"
      (clicked)="handleClear()"
      *ngIf="showClearButton$ | async"
      class="text-s"
      size="s"
      type="secondary"
      aria-label="'search.bar.clear.aria' | translate"
    >
      <fish-icon-cancel icon size="48"></fish-icon-cancel>
      {{ 'search.bar.clear.text' | translate }}
    </fish-button>
    <fish-button
      data-testid="do-search-button"
      (clicked)="handleSearch()"
      [iconPosition]="'left'"
      [size]="'s'"
      [type]="searchButtonType"
      class="text-s"
    >
      <fish-icon-search icon size="48" />
      {{ 'search.bar.button' | translate }}
    </fish-button>
  </div>
</div>
