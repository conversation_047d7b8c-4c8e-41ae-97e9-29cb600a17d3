<fish-page-content>
  <div data-testid="search-results-page" class="flex h-page-content flex-col">
    <fish-search-bar
      (searched)="search($event)"
      [defaultValue]="searchQuery"
      class="pb-8"
      searchButtonType="secondary"
      [attr.aria-label]="'search.bar.label' | translate"
    />

    @if (resultsLoading()) {
      <fish-icon-loader
        [loading]="resultsLoading()"
        size="48"
        class="absolute inset-0 z-10 m-auto animate-spin text-action-primary"
      ></fish-icon-loader>
    }
    <div [ngClass]="{ 'opacity-25': resultsLoading() }" class="relative flex-1 transition-opacity duration-120">
      @if (!searchError()) {
        <fish-search-results [results]="searchResults()" [searchQuery]="searchQuery" />
      } @else {
        <fish-search-error [type]="searchError()!" class="absolute inset-0" data-testid="no-results" />
      }
    </div>

    <div class="flex w-full justify-center pb-8 pt-4">
      <fish-creation-menu />
    </div>
  </div>
</fish-page-content>
