@if (showMenu()) {
  <fish-button-menu [alwaysOpen]="true">
    <fish-button-menu-content>
      @if (showBanButton()) {
        <fish-button [disabled]="isPersonBanned()" [routeTo]="'ban-person'" data-testid="ban-person-button" size="m" type="secondary">
          <fish-icon-lock icon size="32" />
          <span [innerText]="'service_overview.button.ban_person' | translate"></span>
        </fish-button>
      }
      @if (showDeleteButton()) {
        <fish-button [routeTo]="'delete'" data-testid="delete-button" size="m" type="secondary">
          <fish-icon-delete icon size="32" />
          <span [innerText]="'service_overview.button.delete' | translate"></span>
        </fish-button>
      }
      @if (showRegisterFileButton()) {
        <fish-button (clicked)="registerFileButtonClicked()" data-testid="register-file-button" size="m" type="secondary">
          <fish-icon-info icon size="32" />
          <span [innerText]="'service_overview.button.register_file' | translate"></span>
        </fish-button>
      }
    </fish-button-menu-content>
  </fish-button-menu>
}
