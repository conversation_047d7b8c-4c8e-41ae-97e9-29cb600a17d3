<div class="flex w-full flex-wrap items-center justify-between gap-2">
  <div class="inline-flex flex-row items-center gap-1">
    @switch (type()) {
      @case (LicenseCardItemType.Regular) {
        <fish-icon-license-card size="32" class="text-font-primary" />
        <span [innerText]="licenseName()"></span>
      }
      @case (LicenseCardItemType.Vacation) {
        <fish-icon-vacation size="32" class="text-font-primary" />
        <span [innerText]="licenseName()"></span>
      }
      @case (LicenseCardItemType.Limited) {
        <fish-icon-handicapped size="32" class="text-font-primary" />
        <span [innerText]="licenseName()"></span>
      }
      @case (LicenseCardItemType.Certificate) {
        <fish-icon-any-proof size="32" class="text-font-primary" />
        <span [innerText]="'service_overview.license_card.title_labels.certificate' | translate"></span>
      }
    }
  </div>
  <div class="flex flex-row items-center gap-2">
    @if (identificationNumber()?.length) {
      <span class="font-bold" [innerText]="identificationNumber() | documentNumber: ' '"> </span>
    } @else if (errorLabelText()?.length) {
      <fish-badge type="warning">
        <span [innerText]="errorLabelText()"></span>
      </fish-badge>
    }
    @if (type() === LicenseCardItemType.Vacation || type() === LicenseCardItemType.Limited) {
      <div class="rounded border-b border-background bg-action-secondary-glass px-2 font-bold shadow-glass-white-tint">
        <span [innerText]="issuingFederalState()" class="font-bold"></span>
      </div>
    }
  </div>
</div>
