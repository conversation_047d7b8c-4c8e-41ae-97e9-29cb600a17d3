<div class="flex flex-col items-stretch gap-12" data-testid="move-jurisdiction-pre-consent-form">
  <div class="flex flex-col gap-3">
    <p [innerText]="'move_jurisdiction_consent.form.text' | translate" class="self-center text-base font-thin"></p>
    <div>
      <fish-confirm-box
        [formControl]="formGroup.controls.moveCertificateVerified"
        [label]="'move_jurisdiction_consent.form.move_certificate_verified.text' | translate"
        [subLabel]="'move_jurisdiction_consent.form.move_certificate_verified.subtext' | translate: { citizenFullname: citizen() | personFullname }"
        data-testid="move-jurisdiction-pre-consent-form-move-certificate"
      />
    </div>
    @if (!isFormValid()) {
      <fish-consent-form-error-message />
    }
  </div>

  <div class="mb-6 flex flex-1 flex-row flex-wrap content-end items-center justify-between">
    <div class="basis-1/2">
      <fish-confirm-box
        [formControl]="formGroup.controls.submittedByThirdParty"
        [label]="'third_party_consent.label' | translate"
        [subLabel]="'third_party_consent.sub_label' | translate"
        data-testid="move-jurisdiction-pre-consent-form-third-party"
      />
    </div>
    <div class="flex justify-items-end">
      <fish-button (clicked)="continued.emit()" iconPosition="right" size="l" data-testid="move-jurisdiction-pre-consent-form-continue-button">
        <fish-icon-arrow-right icon size="48"></fish-icon-arrow-right>
        <span [innerText]="'common.button.confirm_and_continue' | translate"></span>
      </fish-button>
    </div>
  </div>
</div>
