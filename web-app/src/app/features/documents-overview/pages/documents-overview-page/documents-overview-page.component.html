<fish-page-content>
  <div class="flex flex-col gap-8">
    @if (fishingLicenseDocuments().length > 0) {
      <div>
        <fish-document-overview-header [title]="'documents_overview.header.license' | translate" [amount]="fishingLicenseDocuments().length">
          <fish-icon-license-card size="48" icon></fish-icon-license-card>
        </fish-document-overview-header>
        <div class="flex flex-col gap-1">
          @for (fishingLicenseDocument of fishingLicenseDocuments(); track fishingLicenseDocument.documentId) {
            <fish-document-item
              [document]="fishingLicenseDocument"
              [federalState]="fishingLicenseDocument.fishingLicense!.issuingFederalState"
              [showSendButton]="fishingLicenseDocument.fishingLicense?.type !== LicenseType.Limited"
              [isFetchingDocument]="documentToFetch()?.documentId === fishingLicenseDocument.documentId"
              (printDocumentButtonClicked)="fetchDocument($event)"
              (sendDocumentButtonClicked)="startDocumentSend($event)"
            ></fish-document-item>
          }
        </div>
      </div>
    }
    @if (taxDocuments().length > 0) {
      <div>
        <fish-document-overview-header [title]="'documents_overview.header.taxes' | translate" [amount]="taxDocuments().length">
          <fish-icon-fishing-tax size="48" icon></fish-icon-fishing-tax>
        </fish-document-overview-header>
        <div class="flex flex-col gap-1">
          @for (taxDocument of taxDocuments(); track taxDocument.documentId) {
            <fish-document-item
              [document]="taxDocument"
              [federalState]="taxDocument.tax!.federalState"
              [showSendButton]="true"
              [isFetchingDocument]="documentToFetch()?.documentId === taxDocument.documentId"
              (printDocumentButtonClicked)="fetchDocument($event)"
              (sendDocumentButtonClicked)="startDocumentSend($event)"
            ></fish-document-item>
          }
        </div>
      </div>
    }
    @if (limitedLicenseApprovalDocuments().length > 0) {
      <div>
        <fish-document-overview-header [title]="'documents_overview.header.notices' | translate" [amount]="limitedLicenseApprovalDocuments().length">
          <fish-icon-license-card size="48" icon></fish-icon-license-card>
        </fish-document-overview-header>
        <div class="flex flex-col gap-1">
          @for (limitedLicenseApprovalDocument of limitedLicenseApprovalDocuments(); track limitedLicenseApprovalDocument.documentId) {
            <fish-document-item
              [document]="limitedLicenseApprovalDocument"
              [federalState]="limitedLicenseApprovalDocument.federalState"
              [showSendButton]="false"
              [isFetchingDocument]="documentToFetch()?.documentId === limitedLicenseApprovalDocument.documentId"
              (printDocumentButtonClicked)="fetchDocument($event)"
              (sendDocumentButtonClicked)="startDocumentSend($event)"
              [showValidTo]="true"
            ></fish-document-item>
          }
        </div>
      </div>
    }
    @if (taxDocuments().length === 0 && fishingLicenseDocuments().length === 0) {
      <div class="flex h-page-content-sm w-full items-center justify-center">
        <div class="text-l font-bold text-action-primary" [innerText]="'documents_overview.not_found' | translate"></div>
      </div>
    }
  </div>
</fish-page-content>

<fish-send-documents-dialog (confirmButtonClicked)="sendDocument($event)" [isLoading]="isSendingDocument()"></fish-send-documents-dialog>
<fish-send-documents-success-feedback></fish-send-documents-success-feedback>
