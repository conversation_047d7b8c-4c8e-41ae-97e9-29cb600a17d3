<div
  class="backdrop glass flex h-full rounded-lg border-b-4 border-border-divider-white bg-tint-white shadow-glass-white-tint backdrop-blur-md"
  data-testid="reportings-dashboard-template"
>
  <div
    class="flex grow flex-col items-stretch divide-x-0 divide-y divide-border-divider-alternative overflow-x-auto lg:flex-row lg:divide-x lg:divide-y-0"
  >
    <!-- Start Left part   -->
    <div class="flex flex-row divide-x divide-border-divider-alternative lg:flex-col lg:divide-x-0 lg:divide-y">
      <!-- Start Upper Left part -->
      <div class="flex min-h-fit min-w-48 max-w-48 flex-row items-center gap-2 px-1 py-2 text-action-primary lg:min-h-[5.5rem] lg:w-fit lg:px-4">
        <fish-icon-statistic size="48" />
        <span class="title-shadow font-bold" [innerText]="'reportings.title' | translate"></span>
      </div>
      <!-- End Upper Left part -->

      <!-- Start Lower Left part -->
      <div class="flex grow justify-center p-4">
        <fish-reportings-slider-button
          [direction]="breakpointService.breakpoints.lg() ? 'vertical' : 'horizontal'"
          (changed)="handleReportingsSliderButtonChanged($event)"
        ></fish-reportings-slider-button>
      </div>
      <!-- End Lower Left part -->
    </div>
    <!-- End Left part   -->

    <!-- Start Right part   -->
    @if (dashboardType() === 'statistics') {
      <fish-statistics-dashboard class="grow"></fish-statistics-dashboard>
    } @else if (dashboardType() === 'cashbook') {
      <div class="flex grow flex-row divide-x divide-y-0 divide-border-divider-alternative lg:flex-col lg:divide-x-0 lg:divide-y">
        <div class="flex min-h-fit min-w-48 max-w-48 flex-col items-center px-4 py-2 lg:min-h-[5.5rem] lg:w-full lg:flex-row">
          <h1>Not implemented yet</h1>
        </div>
        <div class="p-4"></div>
      </div>
    }
    <!-- End Right part   -->
  </div>
</div>
