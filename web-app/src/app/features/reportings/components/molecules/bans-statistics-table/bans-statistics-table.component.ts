import { DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { BansStatisticsTableData } from '@/app/features/reportings/components/molecules/bans-statistics-table/bans-statistics-table.models';
import { IconPlusComponent } from '@/app/shared/icons/plus/plus.component';

@Component({
  selector: 'fish-bans-statistics-table',
  imports: [DecimalPipe, TranslateModule, IconPlusComponent],
  templateUrl: './bans-statistics-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BansStatisticsTableComponent {
  // Inputs
  public yearData = input<BansStatisticsTableData | undefined>();

  public previousYearData = input<BansStatisticsTableData | undefined>();
}
