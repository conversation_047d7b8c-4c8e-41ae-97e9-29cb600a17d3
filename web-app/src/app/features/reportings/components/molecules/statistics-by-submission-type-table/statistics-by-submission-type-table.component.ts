import { DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { StatisticsBySubmissionTypeTableData } from '@/app/features/reportings/components/molecules/statistics-by-submission-type-table/statistics-by-submission-type-table.models';
import { IconAuthorityComponent } from '@/app/shared/icons/authority/authority.component';
import { IconEMailComponent } from '@/app/shared/icons/e-mail/e-mail.component';
import { IconOwnStateComponent } from '@/app/shared/icons/own-state/own-state.component';

@Component({
  selector: 'fish-statistics-by-submission-type-table',
  standalone: true,
  imports: [DecimalPipe, IconAuthorityComponent, IconEMailComponent, IconOwnStateComponent, TranslateModule],
  templateUrl: './statistics-by-submission-type-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StatisticsBySubmissionTypeTableComponent {
  // Inputs
  public yearData = input<StatisticsBySubmissionTypeTableData>();

  public previousYearData = input<StatisticsBySubmissionTypeTableData>();
}
