import { DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ErrorsStatisticsTableData } from '@/app/features/reportings/components/molecules/errors-statistics-table/errors-statistics-table.models';
import { IconNumberComponent } from '@/app/shared/icons/number/number.component';
import { IconOwnStateComponent } from '@/app/shared/icons/own-state/own-state.component';

@Component({
  selector: 'fish-errors-statistics-table',
  imports: [DecimalPipe, IconOwnStateComponent, TranslateModule, IconNumberComponent],
  templateUrl: './errors-statistics-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ErrorsStatisticsTableComponent {
  // Inputs
  public yearData = input<ErrorsStatisticsTableData>();

  public previousYearData = input<ErrorsStatisticsTableData>();
}
