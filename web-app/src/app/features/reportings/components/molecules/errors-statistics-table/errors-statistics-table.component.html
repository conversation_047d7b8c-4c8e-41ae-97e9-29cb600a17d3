<div class="flex flex-col bg-background-2 px-3 pb-4 pt-2">
  <table class="w-full text-left">
    <caption></caption>
    <thead>
      <tr>
        <th class="font-semibold px-3 py-1">
          <span [innerText]="'statistics.errors_statistics_table.attribute' | translate"></span>
        </th>
        <th class="font-semibold px-3 py-1 text-right">
          <span [innerText]="yearData()?.year" data-testid="errors-statistics-table-year"></span>
        </th>
        <th class="font-semibold px-3 py-1 text-right text-font-secondary">
          <span [innerText]="previousYearData()?.year" data-testid="errors-statistics-table-previous-year"></span>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr class="border-b border-border-divider">
        <td class="flex items-center gap-2 px-1 pb-3 pt-1">
          <fish-icon-own-state class="-my-2" size="32"></fish-icon-own-state>
          <span [innerText]="'statistics.errors_statistics_table.general' | translate"></span>
        </td>
        <td class="px-3 py-1 text-right">
          <span
            [innerText]="(yearData()?.onlineService ?? 0) + (yearData()?.cardPrinterService ?? 0) + (yearData()?.system ?? 0) | number"
            data-testid="errors-statistics-table-year-amount-general"
          ></span>
        </td>
        <td class="px-3 py-1 text-right text-font-secondary">
          <span
            [innerText]="
              (previousYearData()?.onlineService ?? 0) + (previousYearData()?.cardPrinterService ?? 0) + (previousYearData()?.system ?? 0) | number
            "
            data-testid="errors-statistics-table-previous-year-amount-general"
          ></span>
        </td>
      </tr>
      <tr>
        <td class="flex items-center gap-2 px-1 pb-1 pt-3">
          <fish-icon-number size="32" [number]="1"></fish-icon-number>
          <span [innerText]="'statistics.errors_statistics_table.online_service' | translate"></span>
        </td>
        <td class="px-3 pb-1 pt-3 text-right">
          <span [innerText]="yearData()?.onlineService ?? 0 | number" data-testid="errors-statistics-table-year-amount-online-service"></span>
        </td>
        <td class="px-3 pb-1 pt-3 text-right text-font-secondary">
          <span
            [innerText]="previousYearData()?.onlineService ?? 0 | number"
            data-testid="errors-statistics-table-previous-year-amount-online-service"
          ></span>
        </td>
      </tr>
      <tr>
        <td class="flex items-center gap-2 px-1 py-1">
          <fish-icon-number size="32" [number]="2"></fish-icon-number>
          <span
            [innerText]="'statistics.errors_statistics_table.card_printer_service' | translate"
            data-testid="errors-statistics-table-year-amount-card-printer-service"
          ></span>
        </td>
        <td class="px-3 py-1 text-right">
          <span [innerText]="yearData()?.cardPrinterService ?? 0 | number"></span>
        </td>
        <td class="px-3 py-1 text-right text-font-secondary">
          <span
            [innerText]="previousYearData()?.cardPrinterService ?? 0 | number"
            data-testid="errors-statistics-table-previous-year-amount-card-printer-service"
          ></span>
        </td>
      </tr>
      <tr>
        <td class="flex items-center gap-2 px-1 py-1">
          <fish-icon-number size="32" [number]="3"></fish-icon-number>
          <span [innerText]="'statistics.errors_statistics_table.system' | translate" data-testid="errors-statistics-table-year-amount-system"></span>
        </td>
        <td class="px-3 py-1 text-right">
          <span [innerText]="yearData()?.system ?? 0 | number"></span>
        </td>
        <td class="px-3 py-1 text-right text-font-secondary">
          <span [innerText]="previousYearData()?.system ?? 0 | number" data-testid="errors-statistics-table-previous-year-amount-system"></span>
        </td>
      </tr>
    </tbody>
  </table>
</div>
