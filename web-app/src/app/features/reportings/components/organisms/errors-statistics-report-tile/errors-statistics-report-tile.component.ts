import { ChangeDetectionStrategy, Component, Signal, computed, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ErrorsStatistics } from '@digifischdok/ngx-register-sdk';

import { HistoricalBarChartValue } from '@/app/features/reportings/components/atoms/historical-bar-chart/historical-bar-chart.model.models';
import { ErrorsStatisticsTableComponent } from '@/app/features/reportings/components/molecules/errors-statistics-table/errors-statistics-table.component';
import { ErrorsStatisticsTableData } from '@/app/features/reportings/components/molecules/errors-statistics-table/errors-statistics-table.models';
import { StatisticsReportTileComponent } from '@/app/features/reportings/components/molecules/statistics-report-tile/statistics-report-tile.component';
import { Statistic } from '@/app/features/reportings/models/statistic.model';
import { fadeInAnimation } from '@/app/shared/animations/fade.animations';
import { IconWarningComponent } from '@/app/shared/icons/warning/warning.component';

@Component({
  selector: 'fish-errors-statistics-report-tile',
  imports: [StatisticsReportTileComponent, TranslateModule, IconWarningComponent, ErrorsStatisticsTableComponent],
  templateUrl: './errors-statistics-report-tile.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [fadeInAnimation],
})
export class ErrorsStatisticsReportTileComponent {
  // Inputs
  public statistics = input<Array<ErrorsStatistics>>();

  // Computed signals
  protected readonly primaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      label: lastYearStatistics.year,
      value: this.calculateTotalCount(lastYearStatistics.data),
      amountType: 'unit',
    };
  });

  protected readonly secondaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      label: penultimateYearStatistics.year,
      value: this.calculateTotalCount(penultimateYearStatistics.data),
      amountType: 'unit',
    };
  });

  protected readonly chartData: Signal<HistoricalBarChartValue[]> = computed(() => {
    return [...this.sortedByYearStatistics()].reverse().map((stat) => ({
      year: stat.year,
      value: this.calculateTotalCount(stat.data),
    }));
  });

  protected readonly lastYearData: Signal<ErrorsStatisticsTableData | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      year: lastYearStatistics.year,
      onlineService: lastYearStatistics.data.onlineService,
      cardPrinterService: lastYearStatistics.data.cardPrinterService,
      system: lastYearStatistics.data.system,
    };
  });

  protected readonly penultimateYearData: Signal<ErrorsStatisticsTableData | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      year: penultimateYearStatistics.year,
      onlineService: penultimateYearStatistics.data.onlineService,
      cardPrinterService: penultimateYearStatistics.data.cardPrinterService,
      system: penultimateYearStatistics.data.system,
    };
  });

  private readonly sortedByYearStatistics: Signal<ErrorsStatistics[]> = computed(() => {
    return [...(this.statistics() ?? [])].sort((a, b) => b.year - a.year);
  });

  // the most recent year of the inputted statistics
  private readonly lastYearStatistics: Signal<ErrorsStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted[0] ?? null;
  });

  // the second most recent year of the inputted statistics
  private readonly penultimateYearStatistics: Signal<ErrorsStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted.length > 1 ? sorted[1] : null;
  });

  private calculateTotalCount(data: { onlineService: number; cardPrinterService: number; system: number }): number {
    return data.onlineService + data.cardPrinterService + data.system;
  }
}
