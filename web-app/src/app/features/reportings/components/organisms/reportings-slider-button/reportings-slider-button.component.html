<fish-slider-button-group [direction]="direction()" (changed)="handleSliderButtonChanged($event)" data-testid="reportings-slider-button">
  <fish-slider-button data-testid="reportings-slider-button-statistics">
    <div class="flex min-w-fit flex-row items-center gap-2 lg:min-w-52">
      <fish-icon-statistic size="32" />
      <span [innerText]="'reportings.slider_button.overview' | translate"></span>
    </div>
  </fish-slider-button>
  <!-- The cash statistics button is not visible and not usable, it will be visible later: (ticket. FISH-2284) -->
  <fish-slider-button *ngIf="false" data-testid="reportings-slider-button-cashbook">
    <div class="flex min-w-fit flex-row items-center gap-2 lg:min-w-52">
      <fish-icon-cashbook size="32" />
      <span [innerText]="'reportings.slider_button.cashflow' | translate"></span>
    </div>
  </fish-slider-button>
</fish-slider-button-group>
