<div class="flex h-full flex-row divide-x divide-border-divider-alternative lg:flex-col lg:divide-x-0 lg:divide-y" data-testid="statistics-dashboard">
  <!-- Start Upper part -->
  <div
    class="flex min-h-fit min-w-48 max-w-48 flex-col items-center gap-6 px-4 py-2 lg:min-h-[5.5rem] lg:w-full lg:max-w-full lg:flex-row lg:justify-between"
  >
    <fish-statistics-dashboard-filter
      class="max-w-48 lg:max-w-full lg:grow"
      (filterChange)="filterData.set($event)"
    ></fish-statistics-dashboard-filter>
    <fish-button data-testid="statistics-dashboard-export-button" class="w-full lg:w-fit">
      <fish-icon-download icon size="32"></fish-icon-download>
      <p class="text-left" [innerHTML]="'statistics.dashboard.export_button' | translate"></p>
    </fish-button>
  </div>
  <!-- End Upper part -->
  <!-- Start Lower part -->
  <div class="max-h-screen grow overflow-y-auto p-4 lg:max-h-full">
    <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 xl:grid-cols-3">
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.LICENSES_AND_TAXES) {
        <fish-taxes-statistics-report-tile
          data-testid="taxes-statistics-report-tile"
          [statistics]="taxesStatistics()"
        ></fish-taxes-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.LICENSES_AND_TAXES) {
        <fish-licenses-statistics-report-tile
          data-testid="regular-licenses-statistics-report-tile"
          [statistics]="regularLicensesStatistics()"
          [licenseType]="LicenseType.Regular"
        ></fish-licenses-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.LICENSES_AND_TAXES) {
        <fish-licenses-statistics-report-tile
          data-testid="vacation-licenses-statistics-report-tile"
          [statistics]="vacationLicensesStatistics()"
          [licenseType]="LicenseType.Vacation"
        ></fish-licenses-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.LICENSES_AND_TAXES) {
        <fish-licenses-statistics-report-tile
          data-testid="limited-licenses-statistics-report-tile"
          [statistics]="limitedLicensesStatistics()"
          [licenseType]="LicenseType.Limited"
        ></fish-licenses-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.CERTIFICATIONS) {
        <fish-certifications-statistics-report-tile
          data-testid="certifications-statistics-report-tile"
          [statistics]="certificationsStatistics()"
        ></fish-certifications-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.INSPECTIONS) {
        <fish-inspections-statistics-report-tile
          data-testid="inspections-statistics-report-tile"
          [statistics]="inspectionsStatistics()"
        ></fish-inspections-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.BANS) {
        <fish-bans-statistics-report-tile
          data-testid="bans-statistics-report-tile"
          [statistics]="bansStatistics()"
          [activeBansAmount]="activeBansAmount()"
        ></fish-bans-statistics-report-tile>
      }
      @if (typeFilter() === StatisticsDashboardFilterType.ALL || typeFilter() === StatisticsDashboardFilterType.ERRORS) {
        <fish-errors-statistics-report-tile
          data-testid="errors-statistics-report-tile"
          [statistics]="errorsStatistics()"
        ></fish-errors-statistics-report-tile>
      }
    </div>
  </div>
  <!-- End Lower part -->
</div>
