import { ChangeDetectionStrategy, Component, computed, effect, inject, output, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, Validators } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { filter, map } from 'rxjs';

import { FederalStateAbbreviation, StatisticsMetadataService } from '@digifischdok/ngx-register-sdk';

import { UserService } from '@/app/core/services/user/user.service';
import {
  ALL_CERTIFICATE_ISSUERS_FILTER_OPTION,
  ALL_OFFICES_FILTER_OPTION,
  StatisticsDashboardFilterData,
  StatisticsDashboardFilterType,
} from '@/app/features/reportings/components/organisms/statistics-dashboard-filter/statistics-dashboard-filter.models';
import { fadeInAnimation } from '@/app/shared/animations/fade.animations';
import { FormFieldComponent } from '@/app/shared/organisms/form-field/form-field.component';

@Component({
  selector: 'fish-statistics-dashboard-filter',
  standalone: true,
  imports: [FormFieldComponent, TranslateModule],
  templateUrl: './statistics-dashboard-filter.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [fadeInAnimation],
})
export class StatisticsDashboardFilterComponent {
  // Dependencies
  private readonly userService: UserService = inject(UserService);

  private readonly statisticsMetadataService: StatisticsMetadataService = inject(StatisticsMetadataService);

  // Options
  protected readonly typeOptions = Object.values(StatisticsDashboardFilterType);

  protected readonly federalStateOptions = Object.values(FederalStateAbbreviation);

  protected readonly yearOptions = toSignal(
    this.statisticsMetadataService.statisticsMetadataControllerGetYears().pipe(map((years) => (years ?? []).map(String))),
    { initialValue: [] }
  );

  private readonly defaultYearOption = computed(() => this.yearOptions()[0] ?? new Date().getFullYear().toString());

  // Form Controls
  protected readonly typeControl = new FormControl<StatisticsDashboardFilterType>(this.typeOptions[0], [Validators.required]);

  protected readonly yearControl = new FormControl<string>(this.defaultYearOption(), [Validators.required]);

  protected readonly federalStateControl = new FormControl<FederalStateAbbreviation>(this.userService.getFederalState(), [Validators.required]);

  protected readonly officeControl = new FormControl<string>('', [Validators.required]);

  protected readonly certificateIssuerControl = new FormControl<string>('', [Validators.required]);

  // Output
  public readonly filterChange = output<StatisticsDashboardFilterData>();

  // Signals
  private readonly typeFilter = signal<StatisticsDashboardFilterType>(this.typeOptions[0]);

  private readonly yearFilter = signal<string>(this.defaultYearOption());

  private readonly federalStateFilter = signal<FederalStateAbbreviation>(this.userService.getFederalState());

  private readonly officeFilter = signal<string>('');

  private readonly certificateIssuerFilter = signal<string>('');

  // Combined filter data signal
  protected readonly filterData = computed<StatisticsDashboardFilterData>(() => ({
    type: this.typeFilter(),
    year: this.yearFilter(),
    federalState: this.federalStateFilter(),
    office: this.officeFilter(),
    certificateIssuer: this.certificateIssuerFilter(),
  }));

  protected readonly isLicensesAndTaxesSelected = computed(() => {
    return this.typeFilter() === StatisticsDashboardFilterType.LICENSES_AND_TAXES;
  });

  protected readonly isCertificationsSelected = computed(() => {
    return this.typeFilter() === StatisticsDashboardFilterType.CERTIFICATIONS;
  });

  protected readonly officeFilterOptions = signal<string[]>([]);

  protected readonly certificateIssuerFilterOptions = signal<string[]>([]);

  constructor() {
    this.typeControl.valueChanges
      .pipe(
        filter(
          (value): value is StatisticsDashboardFilterType =>
            !!value && // filter out null values
            this.typeControl.valid // only emit if the control is valid
        )
      )
      .subscribe((value) => {
        this.typeFilter.set(value);
      });

    this.yearControl.valueChanges
      .pipe(
        filter(
          (value): value is string =>
            !!value && // filter out null values
            this.yearControl.valid // only emit if the control is valid
        )
      )
      .subscribe((value) => {
        this.yearFilter.set(value);
      });

    this.federalStateControl.valueChanges
      .pipe(
        filter(
          (value): value is FederalStateAbbreviation =>
            !!value && // filter out null values
            this.federalStateControl.valid // only emit if the control is valid
        )
      )
      .subscribe((value) => {
        this.federalStateFilter.set(value);
      });

    this.officeControl.valueChanges
      .pipe(
        filter(
          (value): value is string =>
            !!value && // filter out null values
            this.officeControl.valid // only emit if the control is valid
        )
      )
      .subscribe((value) => {
        this.officeFilter.set(value);
      });

    this.certificateIssuerControl.valueChanges
      .pipe(
        filter(
          (value): value is string =>
            !!value && // filter out null values
            this.certificateIssuerControl.valid // only emit if the control is valid
        )
      )
      .subscribe((value) => {
        this.certificateIssuerFilter.set(value);
      });

    // Effect for loading office options
    effect(() => {
      if (this.isLicensesAndTaxesSelected() && this.federalStateFilter() && this.yearFilter()) {
        this.statisticsMetadataService
          .statisticsMetadataControllerGetOffices([Number(this.yearFilter())], this.federalStateFilter())
          .subscribe((offices) => {
            this.officeFilterOptions.set([ALL_OFFICES_FILTER_OPTION, ...offices]);
            this.officeControl.setValue(ALL_OFFICES_FILTER_OPTION);
          });
      } else {
        this.officeControl.setValue(ALL_OFFICES_FILTER_OPTION);
      }
    });

    // Effect for loading certificate issuer options
    effect(() => {
      if (this.isCertificationsSelected() && this.federalStateFilter() && this.yearFilter()) {
        this.statisticsMetadataService
          .statisticsMetadataControllerGetCertificationIssuers([Number(this.yearFilter())], this.federalStateFilter())
          .subscribe((certificateIssuers) => {
            this.certificateIssuerFilterOptions.set([ALL_CERTIFICATE_ISSUERS_FILTER_OPTION, ...certificateIssuers]);
            this.certificateIssuerControl.setValue(ALL_CERTIFICATE_ISSUERS_FILTER_OPTION);
          });
      } else {
        this.certificateIssuerControl.setValue(ALL_CERTIFICATE_ISSUERS_FILTER_OPTION);
      }
    });

    // Effect for emitting filter change
    effect(() => {
      if (this.filterData()) {
        this.filterChange.emit(this.filterData());
      }
    });
  }
}
