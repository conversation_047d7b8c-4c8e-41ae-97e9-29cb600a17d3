import { ChangeDetectionStrategy, Component, effect, inject, signal } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, map } from 'rxjs';

import { RegisterFileResponse, RegisterFileService } from '@digifischdok/ngx-register-sdk';

import { RouteParamService } from '@/app/core/services/route-param.service';
import { CitizenStore } from '@/app/core/stores/citizen.store';
import { RegisterFileComponent } from '@/app/features/register-file/components/templates/register-file/register-file.component';
import { PersonFormatterService } from '@/app/shared/services/person-formatter.service';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';

@Component({
  selector: 'fish-register-file-page',
  imports: [TranslateModule, RegisterFileComponent],
  templateUrl: './register-file-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFilePageComponent {
  private readonly registerEntryId = inject(RouteParamService).getParamOrFail('registerEntryId', inject(ActivatedRoute));

  protected readonly registerFile = signal<RegisterFileResponse | null>(null);

  private readonly registerFileService = inject(RegisterFileService);

  private readonly serverErrorDialogService = inject(ServerDialogService);

  private readonly citizenStore = inject(CitizenStore);

  private readonly personFormatterService = inject(PersonFormatterService);

  private readonly titleService = inject(Title);

  constructor() {
    this.initRegisterFile();
    this.initTitle();
  }

  private initRegisterFile(): void {
    this.registerFileService
      .registerFileControllerGet(this.registerEntryId, 'response')
      .pipe(
        map((response) => response.body),
        catchError((error: unknown) => {
          this.serverErrorDialogService.handleServerError(error);
          return [];
        })
      )
      .subscribe((registerFile) => {
        this.registerFile.set(registerFile);
      });
  }

  private initTitle(): void {
    this.citizenStore.fetchAndSetProfile(this.registerEntryId);
    effect(() => {
      const person = this.citizenStore.profile()?.person;
      if (person) {
        this.titleService.setTitle(this.personFormatterService.formatName(person));
      }
    });
  }
}
