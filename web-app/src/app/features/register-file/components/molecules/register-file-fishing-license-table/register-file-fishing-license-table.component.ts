import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { FishingLicenseFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-fishing-license-table',
  imports: [TranslateModule, RegisterFileTableRowComponent],
  templateUrl: './register-file-fishing-license-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileFishingLicenseTableComponent {
  public readonly data = input.required<FishingLicenseFP>();
}
