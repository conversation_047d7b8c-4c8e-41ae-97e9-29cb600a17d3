import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { BanFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-ban-table',
  imports: [TranslateModule, RegisterFileTableRowComponent],
  templateUrl: './register-file-ban-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileBanTableComponent {
  public readonly data = input.required<BanFP>();
}
