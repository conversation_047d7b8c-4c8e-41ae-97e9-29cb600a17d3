import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { FiledProcess } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-process-head-table',
  imports: [TranslateModule, RegisterFileTableRowComponent],
  templateUrl: './register-file-process-head-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileProcessHeadTableComponent {
  public readonly data =
    input.required<Pick<FiledProcess, 'actingInstitution' | 'federalStateOfInstitution' | 'processTimestamp' | 'federalState' | 'issuedBy'>>();
}
