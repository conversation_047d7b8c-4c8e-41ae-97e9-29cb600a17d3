import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ConsentInfoFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-consent-info-table',
  imports: [TranslateModule, RegisterFileTableRowComponent],
  templateUrl: './register-file-consent-info-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileConsentInfoTableComponent {
  public readonly data = input.required<ConsentInfoFP>();
}
