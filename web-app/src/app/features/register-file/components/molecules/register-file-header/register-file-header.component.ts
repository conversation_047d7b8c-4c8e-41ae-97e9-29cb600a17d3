import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { LogoComponent } from '@/app/shared/atoms/logo/logo.component';

@Component({
  selector: 'fish-register-file-header',
  imports: [TranslateModule, DatePipe, LogoComponent],
  templateUrl: './register-file-header.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileHeaderComponent {
  public readonly registerEntryId = input.required<string>();

  public readonly createdAt = input.required<string>();

  public readonly createdByInstitution = input.required<string>();
}
