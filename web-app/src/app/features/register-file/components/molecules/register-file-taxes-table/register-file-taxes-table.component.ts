import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { TaxFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-taxes-table',
  imports: [RegisterFileTableRowComponent, TranslateModule],
  templateUrl: './register-file-taxes-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileTaxesTableComponent {
  public readonly data = input.required<TaxFP[]>();
}
