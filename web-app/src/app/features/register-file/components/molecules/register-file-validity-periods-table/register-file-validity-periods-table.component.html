<table class="w-full border-collapse">
  <caption></caption>
  <tr class="border-b border-border-divider-alternative odd:bg-background even:bg-background-2">
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.validity_periods.valid_from' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.validity_periods.valid_to' | translate"
    ></th>
  </tr>
  @for (dataEntry of data(); track dataEntry) {
    <tr fish-register-file-table-row [cells]="[dataEntry.validFrom, dataEntry.validTo]"></tr>
  }
</table>
