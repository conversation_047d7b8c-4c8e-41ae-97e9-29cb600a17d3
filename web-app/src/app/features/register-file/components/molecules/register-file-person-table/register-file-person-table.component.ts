import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { PersonFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-person-table',
  imports: [TranslateModule, RegisterFileTableRowComponent],
  templateUrl: './register-file-person-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFilePersonTableComponent {
  public readonly data = input.required<PersonFP & { serviceAccountId: string | undefined }>();
}
