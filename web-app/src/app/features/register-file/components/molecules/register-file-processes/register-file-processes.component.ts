import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';

import { FiledProcess, FiledProcessType, PersonFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileBanTableComponent } from '@/app/features/register-file/components/molecules/register-file-ban-table/register-file-ban-table.component';
import { RegisterFileConsentInfoTableComponent } from '@/app/features/register-file/components/molecules/register-file-consent-info-table/register-file-consent-info-table.component';
import { RegisterFileDeletionFlagTableComponent } from '@/app/features/register-file/components/molecules/register-file-deletion-flag-table/register-file-deletion-flag-table.component';
import { RegisterFileFeesTableComponent } from '@/app/features/register-file/components/molecules/register-file-fees-table/register-file-fees-table.component';
import { RegisterFileFishingLicenseTableComponent } from '@/app/features/register-file/components/molecules/register-file-fishing-license-table/register-file-fishing-license-table.component';
import { RegisterFileIdentificationDocumentsTableComponent } from '@/app/features/register-file/components/molecules/register-file-identification-documents-table/register-file-identification-documents-table.component';
import { RegisterFileOfficeAddressTableComponent } from '@/app/features/register-file/components/molecules/register-file-office-address-table/register-file-office-address-table.component';
import { RegisterFilePersonTableComponent } from '@/app/features/register-file/components/molecules/register-file-person-table/register-file-person-table.component';
import { RegisterFileProcessHeadTableComponent } from '@/app/features/register-file/components/molecules/register-file-process-head-table/register-file-process-head-table.component';
import { RegisterFileQualificationsProofTableComponent } from '@/app/features/register-file/components/molecules/register-file-qualifications-proof-table/register-file-qualifications-proof-table.component';
import { RegisterFileTaxesTableComponent } from '@/app/features/register-file/components/molecules/register-file-taxes-table/register-file-taxes-table.component';
import { RegisterFileValidityPeriodsTableComponent } from '@/app/features/register-file/components/molecules/register-file-validity-periods-table/register-file-validity-periods-table.component';

@Component({
  selector: 'fish-register-file-processes',
  imports: [
    TranslateModule,
    RegisterFileProcessHeadTableComponent,
    RegisterFilePersonTableComponent,
    RegisterFileOfficeAddressTableComponent,
    RegisterFileQualificationsProofTableComponent,
    RegisterFileFishingLicenseTableComponent,
    RegisterFileBanTableComponent,
    RegisterFileConsentInfoTableComponent,
    RegisterFileFeesTableComponent,
    RegisterFileTaxesTableComponent,
    RegisterFileValidityPeriodsTableComponent,
    RegisterFileIdentificationDocumentsTableComponent,
    RegisterFileDeletionFlagTableComponent,
    AsyncPipe,
  ],
  templateUrl: './register-file-processes.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileProcessesComponent {
  public readonly processes = input.required<FiledProcess[]>();

  private readonly translate: TranslateService = inject(TranslateService);

  protected personalDataWithServiceAccountId(
    person: PersonFP,
    serviceAccountId: string | undefined
  ): PersonFP & {
    serviceAccountId: string | undefined;
  } {
    return {
      ...person,
      serviceAccountId,
    };
  }

  protected getTranslatedProcessType$(processType: FiledProcessType): Observable<string> {
    switch (processType) {
      case FiledProcessType.QualificationsProofCreated:
        return this.translate.get('register_file.titles.process_type.qualifications_proof_created');
      case FiledProcessType.JurisdictionChanged:
        return this.translate.get('register_file.titles.process_type.jurisdiction_changed');
      case FiledProcessType.FishingLicenseCreated:
        return this.translate.get('register_file.titles.process_type.fishing_license_created');
      case FiledProcessType.FishingLicenseExtended:
        return this.translate.get('register_file.titles.process_type.fishing_license_extended');
      case FiledProcessType.ReplacementCardOrdered:
        return this.translate.get('register_file.titles.process_type.replacement_card_ordered');
      case FiledProcessType.FishingTaxCreated:
        return this.translate.get('register_file.titles.process_type.fishing_tax_created');
      case FiledProcessType.Banned:
        return this.translate.get('register_file.titles.process_type.banned');
      case FiledProcessType.Unbanned:
        return this.translate.get('register_file.titles.process_type.unbanned');
      case FiledProcessType.LimitedLicenseApplicationCreated:
        return this.translate.get('register_file.titles.process_type.limited_license_application_created');
      case FiledProcessType.LimitedLicenseApplicationRejected:
        return this.translate.get('register_file.titles.process_type.limited_license_application_rejected');
      case FiledProcessType.MarkedForDeletion:
        return this.translate.get('register_file.titles.process_type.marked_for_deletion');
      default:
        throw new Error('Unknown process type');
    }
  }
}
