import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { FeeFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-fees-table',
  imports: [TranslateModule, RegisterFileTableRowComponent],
  templateUrl: './register-file-fees-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileFeesTableComponent {
  public readonly data = input.required<FeeFP[]>();
}
