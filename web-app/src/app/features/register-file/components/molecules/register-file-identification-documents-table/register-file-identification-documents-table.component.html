<table class="w-full border-collapse">
  <caption></caption>
  <tr class="border-b border-border-divider-alternative odd:bg-background even:bg-background-2">
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.identification_documents.fishing_tax_id' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.identification_documents.fishing_license_id' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.identification_documents.type' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.identification_documents.valid_from' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.identification_documents.valid_to' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.identification_documents.issued_date' | translate"
    ></th>
  </tr>
  @for (dataEntry of data(); track dataEntry) {
    <tr
      fish-register-file-table-row
      [cells]="[dataEntry.fishingTaxId, dataEntry.fishingLicenseId, dataEntry.type, dataEntry.validFrom, dataEntry.validTo, dataEntry.issuedDate]"
    ></tr>
  }
</table>
