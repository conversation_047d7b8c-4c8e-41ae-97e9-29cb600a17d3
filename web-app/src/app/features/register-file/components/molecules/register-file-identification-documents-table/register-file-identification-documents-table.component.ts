import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { IdentificationDocumentFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-identification-documents-table',
  imports: [RegisterFileTableRowComponent, TranslateModule],
  templateUrl: './register-file-identification-documents-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileIdentificationDocumentsTableComponent {
  public readonly data = input.required<IdentificationDocumentFP[]>();
}
