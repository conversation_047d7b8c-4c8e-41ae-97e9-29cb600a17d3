import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { QualificationsProofFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-qualifications-proof-table',
  imports: [TranslateModule, RegisterFileTableRowComponent],
  templateUrl: './register-file-qualifications-proof-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileQualificationsProofTableComponent {
  public readonly data = input.required<QualificationsProofFP>();
}
