import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { DeletionFlagFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-deletion-flag-table',
  imports: [RegisterFileTableRowComponent, TranslateModule],
  templateUrl: './register-file-deletion-flag-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileDeletionFlagTableComponent {
  public readonly data = input.required<DeletionFlagFP>();
}
