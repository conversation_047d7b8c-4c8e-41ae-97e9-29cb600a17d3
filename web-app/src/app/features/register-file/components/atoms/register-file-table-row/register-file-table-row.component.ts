import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'tr[fish-register-file-table-row]',
  imports: [TranslateModule],
  templateUrl: './register-file-table-row.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'border-b border-border-divider-alternative odd:bg-background even:bg-background-2',
  },
})
export class RegisterFileTableRowComponent {
  public readonly cells = input.required<(string | number | boolean | undefined)[]>();
}
