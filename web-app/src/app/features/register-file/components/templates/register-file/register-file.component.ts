import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { RegisterFileResponse } from '@digifischdok/ngx-register-sdk';

import { RegisterFileHeaderComponent } from '@/app/features/register-file/components/molecules/register-file-header/register-file-header.component';
import { RegisterFileProcessesComponent } from '@/app/features/register-file/components/molecules/register-file-processes/register-file-processes.component';

@Component({
  selector: 'fish-register-file',
  imports: [RegisterFileHeaderComponent, RegisterFileProcessesComponent],
  templateUrl: './register-file.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileComponent {
  public readonly registerFile = input.required<RegisterFileResponse>();
}
