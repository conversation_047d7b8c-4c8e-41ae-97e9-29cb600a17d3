import { HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { APP_INITIALIZER, ApplicationConfig, LOCALE_ID, importProvidersFrom } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { PreloadAllModules, Router, provideRouter, withPreloading, withRouterConfig } from '@angular/router';

import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';

import { ApiModule, Configuration } from '@digifischdok/ngx-register-sdk';

import { routes } from '@/app/app.routes';
import { EnvironmentService } from '@/app/core/environment/environment.service';
import { initializeApp } from '@/app/core/factory/app-init.factory';
import { ApiInterceptor } from '@/app/core/interceptors/api.interceptor';
import { LicenseInformationStore } from '@/app/core/stores/license-information.store';
import { TaxInformationStore } from '@/app/core/stores/tax-information.store';
import { ClientSpecificLoaderFactory } from '@/app/core/translations/loader';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes, withRouterConfig({ paramsInheritanceStrategy: 'always' }), withPreloading(PreloadAllModules)),
    provideHttpClient(withInterceptorsFromDi()),
    importProvidersFrom(ApiModule),
    {
      provide: Configuration,
      useValue: new Configuration({
        basePath: 'api',
      }),
    },
    importProvidersFrom(BrowserModule),
    importProvidersFrom(BrowserAnimationsModule),
    KeycloakService,
    EnvironmentService,
    importProvidersFrom(
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: ClientSpecificLoaderFactory,
          deps: [HttpClient, KeycloakService],
        },
      })
    ),
    { provide: HTTP_INTERCEPTORS, useClass: ApiInterceptor, multi: true },
    {
      provide: APP_INITIALIZER,
      useFactory: initializeApp,
      multi: true,
      deps: [KeycloakService, TaxInformationStore, LicenseInformationStore, ServerDialogService, EnvironmentService, Router],
    },
    { provide: LOCALE_ID, useValue: 'de-DE' },
    // {
    //   provide: CSP_NONCE,
    //   useFactory: (): string | undefined => {
    //     const metaTag = document.querySelector('meta[name="csp-nonce"]');
    //     return metaTag?.getAttribute('content') || undefined;
    //   },
    // },
  ],
};
