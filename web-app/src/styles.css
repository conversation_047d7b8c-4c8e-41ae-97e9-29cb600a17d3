@import '@angular/cdk/overlay-prebuilt.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  @apply text-font-primary;
}


@layer utilities {
  .title-shadow {
    text-shadow: 0 38px 11px rgba(22, 59, 191, 0),
    0 24px 10px rgba(22, 59, 191, 0.01),
    0 14px 8px rgba(22, 59, 191, 0.04),
    0 6px 6px rgba(22, 59, 191, 0.07),
    0 2px 3px rgba(22, 59, 191, 0.08);
  }

  .border-gradient-white {
    background-image: linear-gradient(white, white), linear-gradient(to bottom, #E3E8F839, #FFFFFF39);
    background-origin: border-box;
    background-clip: padding-box, border-box;
    /*border-image: linear-gradient(to top, #E3E8F839, #FFFFFF39) 1*/
  }

  .border-gradient-primary {
    background-image: linear-gradient(white, white), linear-gradient(to bottom, #9FAFE6, #E3E8F8);
    background-origin: border-box;
    background-clip: padding-box, border-box;
    /*border-image: linear-gradient(to top, #9FAFE6, #E3E8F8) 1;*/
  }
}

@layer base {
  strong, b {
    font-weight: 700;
  }
}

/* Print styles for register file */
@media print {
  @page {
    size: A4;
    margin: 30mm;
  }

  * {
    /* Keep alternating table row background colors */
    print-color-adjust: exact;
  }
}
