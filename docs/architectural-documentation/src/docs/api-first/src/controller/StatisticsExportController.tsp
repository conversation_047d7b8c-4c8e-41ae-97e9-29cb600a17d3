import "@typespec/http";
import "@typespec/openapi3";

import "../common.tsp";
import "../Fischereiregister.tsp";
import "../model/enum/FederalStateAbbreviation.tsp";
import "../response/CSVExportResponse.tsp";

using TypeSpec.Http;

namespace Fischereiregister;

@tag("Statistics Export")
@route("/statistics")
@doc("allows the exporting of Statistics Data as CSV file")
interface StatisticsExportController {

    @get
    @route("/taxes/export")
    @doc("Retrieves a CSV file containing taxation statistics for the specified years, optionally filtered by office or federal state.")
    exportTaxesStatistics(
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): | CSVExportResponse
       | ServerErrorResponse
       | UnauthorizedResponse;

    @get
    @route("/licenses/regular/export")
    @doc("Retrieves a CSV file containing statistics for regular fishing licenses issued within the specified years, optionally filtered by office or federal state.")
    exportRegularLicensesStatistics(
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): | CSVExportResponse
       | ServerErrorResponse
       | UnauthorizedResponse;

    @get
    @route("/licenses/vacation/export")
    @doc("Retrieves a CSV file containing statistics for vacation fishing licenses issued within the specified years, optionally filtered by office or federal state.")
    exportVacationLicensesStatistics(
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): | CSVExportResponse
       | ServerErrorResponse
       | UnauthorizedResponse;
    
    @get
    @route("/licenses/limited/export")
    @doc("Retrieves a CSV file containing statistics for limited access fishing licenses issued within the specified years, optionally filtered by office or federal state.")
    exportLimitedLicensesStatistics(
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): | CSVExportResponse
       | ServerErrorResponse
       | UnauthorizedResponse;
    
    @get
    @route("/bans/export")
    @doc("Retrieves a CSV file containing statistics for issued and expired bans within the specified years, optionally filtered by federal state.")
    exportBansStatistics(
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The federal state for which the statistics should be retrieved. If no Federal State is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): | CSVExportResponse
       | ServerErrorResponse
       | UnauthorizedResponse;
    
    @get
    @route("/certifications/export")
    @doc("Retrieves a CSV file containing statistics for certifications issued within the specified years, optionally filtered by office or federal state.")
    exportCertificationsStatistics(
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): | CSVExportResponse
       | ServerErrorResponse
       | UnauthorizedResponse;
    
    
    @get
    @route("/inspections/export")
    @doc("Retrieves a CSV file containing statistics for fishing inspections conducted within the specified years, filtered by federal state.")
    exportInspectionsStatistics(
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The federal state for which the statistics should be retrieved. If no Federal State is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): | CSVExportResponse
       | ServerErrorResponse
       | UnauthorizedResponse;
    
     
    @get
    @route("/errors/export")
    @doc("Retrieves CSV file containing statistics for errors and failures within the specified years, filtered by federal state or office.")
    exportErrorsStatistics(
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,


        @doc("The federal state for which the statistics should be retrieved. If no Federal State is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): | CSVExportResponse
       | ServerErrorResponse
       | UnauthorizedResponse;
}
