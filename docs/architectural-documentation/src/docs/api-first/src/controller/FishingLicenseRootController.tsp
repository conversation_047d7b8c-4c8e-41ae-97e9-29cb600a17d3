import "@typespec/http";
import "@typespec/openapi3";
import "../response/CreateFishingLicenseResponse.tsp";
import "../response/CreateLimitedLicenseResponse.tsp";
import "../request/CreateVacationFishingLicenseRequest.tsp";
import "../request/CreateLimitedFishingLicenseRequest.tsp";
import "../request/CreateApprovalPreviewForLimitedLicenseRequest.tsp";
import "../response/ValidationResponse.tsp";
import "../Fischereiregister.tsp";
import "../common.tsp";

using TypeSpec.Http;

namespace Fischereiregister;

@tag("Fishing license")
@route("register-entries/fishing-licenses")
interface FishingLicenseRootController {
    @doc("""
    Create a new register entry with a LIMITED type license.

    This api endpoint will return 400, if limited licenses are not available in the tenant of the logged in user
    """)
    @post()
    @route("limited")
    createLimited(
      @body createLimitedRequest: CreateLimitedFishingLicenseRequest,
    ): WithCreatedErrors<CreateLimitedLicenseResponse, "/register-entries/:registerEntryId/fishing-licenses/:licenseNumber"> | NotFoundResponse;

    @doc("""
        Fetches a preview PDF for the approval of a limited license.
    """)
    @post()
    @route("limited/approval-preview")
    createApprovalPreviewForLimitedLicense(
    @body createApprovalPreviewForLimitedLicenseRequest: CreateApprovalPreviewForLimitedLicenseRequest,
    ):| PDFExportResponse
    | ServerErrorResponse;


    @doc("""
    Create a new register entry with a VACATION type license.
    """)
    @post()
    @route("vacation")
    createVacation(
      @body createVacationRequest: CreateVacationFishingLicenseRequest,
  
    ): WithCreatedErrors<CreateFishingLicenseResponse, "/register-entries/:registerEntryId/fishing-licenses/:licenseNumber"> | NotFoundResponse;

    @route(":validate")
    @doc("To validate a Citizen via Cardcode information or identification document id.")
    @get
    validate(
        @query
        licenseNumber?: string,

        @doc("The Identifier of the Document (UUID)")
        @query
        identificationDocumentId: string,

        @doc("Hash of the Register data used to verify lawful Validation and prevent tampering.")
        @query
        hash: string,
    ): SuccessResponse<ValidationResponse> | BadRequestResponse | NotFoundResponse;
}
