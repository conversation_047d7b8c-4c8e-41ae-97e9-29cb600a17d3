import "./WithOnlineServiceContextOS.tsp";
import "./WithPersonInformationOS.tsp";
import "../WithConsentInformation.tsp";
import "../WithValidityPeriod.tsp";
import "../WithFederalState.tsp";
import "../../model/os/FeeOS.tsp";

model CreateVacationLicenseRequestOS {

    @doc("Transaction id of the payment.")
    transactionId: string;

    @doc("Fee that is payed together with this request.")
    fee: FeeOS;

    ...WithFederalState;
    ...WithPersonInformationOS;
    ...WithValidityPeriod;
    ...WithOnlineServiceContextOS;
    ...WithConsentInformation;
}
