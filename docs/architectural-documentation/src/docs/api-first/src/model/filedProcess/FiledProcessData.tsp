import "./PersonFP.tsp";
import "./QualificationsProofFP.tsp";
import "./TaxFP.tsp";
import "./FeeFP.tsp";
import "./BanFP.tsp";
import "./ConsentInfoFP.tsp";
import "./IdentificationDocumentFP.tsp";
import "./FishingLicenseFP.tsp";
import "./DeletionFlagFP.tsp";

@doc("The additional data, which describes the process.")
model FiledProcessData {

    @doc("(Optional) The person regarding this process")
    person?: PersonFP;

    @doc("(Optional) Service account id of the person regarding this process")
    serviceAccountId?: string;

    @doc("(Optional) The qualification information about the person requesting the fishing license.")
    qualificationsProof?: QualificationsProofFP;

    @doc("All fishing taxes that the person registered has paid so far.")
    taxes?: TaxFP[];

    @doc("Fees that are payed together with this process.")
    fees?: FeeFP[];

    @doc("Ban that is issued in this process.")
    ban?: BanFP;

    @doc("The consent info, that was submitted with this process.")
    consentInfo?: ConsentInfoFP;

    @doc("Representations of the analog (currently valid) identification documents connected to the register (pdfs / check-cards)")
    identificationDocuments?: IdentificationDocumentFP[];

    @doc("License created or altered in this process.")
    fishingLicense?: FishingLicenseFP;

    @doc("The ID of the process, which is used to identify the process in the system.")
    deletionFlag?: DeletionFlagFP;

}
