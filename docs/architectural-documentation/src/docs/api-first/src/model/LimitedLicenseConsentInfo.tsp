@doc("Contains information about consent and disability certificate verification to be confirmed connected to a request to the API.")
model LimitedLicenseConsentInfo {
    @doc("Indicates if the submission was made by a third party.")
    submittedByThirdParty: boolean;

    @doc("Indicates if GDPR terms have been accepted.")
    gdprAccepted: boolean;

    @doc("Indicates if self-disclosure terms have been accepted.")
    selfDisclosureAccepted: boolean;

    @doc("Indicates if the disability certificate has been seen and verified.")
    disabilityCertificateVerified: boolean;
}
