import "@typespec/http";
import "@typespec/rest";
import "@typespec/versioning";
import "@typespec/openapi3";
import "@typespec/openapi";

using TypeSpec.Http;
using TypeSpec.Versioning;

// tag::comment[]
// https://github.com/microsoft/typespec/blob/main/packages/http/lib/auth.tsp#L47
// https://auth.dev.echolot.app/realms/digifischdok_dev/.well-known/openid-configuration
// end::comment[]
@service({
  title: "Fischereiregister API",
})
@TypeSpec.OpenAPI.info({
  `x-logo`: {
    url: "http://digifischdok-dev-cluster.dsecurecloud.de/assets/logo/logo-l.svg",
  },
})
@useAuth(
  KeycloakAuth<[
    // Default Scopes of Keycloak
    "openid",
    "email",
    "phone",
    "roles",
    "offline_access",
    "profile",
    "acr",
    "address",
    "microprofile-jwt",
    "web-origins",
    // New Scopes:
    "card-provider"
  ]> | KeycloakAuthWithAuthorizationCode<[
    // Default Scopes of Keycloak
    "openid",
    "email",
    "phone",
    "roles",
    "offline_access",
    "profile",
    "acr",
    "address",
    "microprofile-jwt",
    "web-origins",
    // New Scopes:
    "card-provider"
  ]>
)
@server(
  "http://digifischdok-dev-cluster.dsecurecloud.de/api",
  "DEV Development Endpoint"
)
@versioned(Fischereiregister.Versions)
namespace Fischereiregister;

enum Versions {
  `1.17.1`,
}
