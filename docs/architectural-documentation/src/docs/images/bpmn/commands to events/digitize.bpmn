<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0w63w7p" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1crftmq">
    <bpmn:participant id="Participant_0iz61gi" name="Digitize Fishing License" processRef="Process_061fgn0" />
  </bpmn:collaboration>
  <bpmn:process id="Process_061fgn0" isExecutable="true">
    <bpmn:laneSet id="LaneSet_0qswr6d">
      <bpmn:lane id="Lane_1hvo7kf">
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_105o16f</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0f0waik</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1721v09">
        <bpmn:flowNodeRef>Event_01ajovt</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_15f6b65</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0ni82hh</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_18jmrc4</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_05zgkr1</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0bi2paz</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_105o16f" name="FishingLicenseController">
      <bpmn:incoming>Flow_0bi2paz</bpmn:incoming>
      <bpmn:outgoing>Flow_1s777au</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_0f0waik" name="DigitizeRegularLiceseCommand">
      <bpmn:incoming>Flow_1s777au</bpmn:incoming>
      <bpmn:outgoing>Flow_05k4fw1</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_13fupkm" />
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateCatchEvent id="Event_01ajovt">
      <bpmn:incoming>Flow_05k4fw1</bpmn:incoming>
      <bpmn:outgoing>Flow_0zf19rh</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0hapdln" />
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="Activity_15f6b65" name="RegisterEntry">
      <bpmn:incoming>Flow_0zf19rh</bpmn:incoming>
      <bpmn:outgoing>Flow_1bm4kse</bpmn:outgoing>
      <bpmn:outgoing>Flow_0obo4x3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vcv9vj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0ni82hh" name="RegularLicenseDigitizedEvent">
      <bpmn:incoming>Flow_1bm4kse</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0cgp628" />
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_18jmrc4" name="QualificationsProofCreatedEvent">
      <bpmn:incoming>Flow_0obo4x3</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1p2vrkw" />
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_05zgkr1" name="FishingTaxPayedEvent">
      <bpmn:incoming>Flow_0vcv9vj</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1woaxbx" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0bi2paz" sourceRef="StartEvent_1" targetRef="Activity_105o16f" />
    <bpmn:sequenceFlow id="Flow_1s777au" sourceRef="Activity_105o16f" targetRef="Event_0f0waik" />
    <bpmn:sequenceFlow id="Flow_05k4fw1" sourceRef="Event_0f0waik" targetRef="Event_01ajovt" />
    <bpmn:sequenceFlow id="Flow_0zf19rh" sourceRef="Event_01ajovt" targetRef="Activity_15f6b65" />
    <bpmn:sequenceFlow id="Flow_1bm4kse" sourceRef="Activity_15f6b65" targetRef="Event_0ni82hh" />
    <bpmn:sequenceFlow id="Flow_0obo4x3" sourceRef="Activity_15f6b65" targetRef="Event_18jmrc4" />
    <bpmn:sequenceFlow id="Flow_0vcv9vj" sourceRef="Activity_15f6b65" targetRef="Event_05zgkr1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1crftmq">
      <bpmndi:BPMNShape id="Participant_0iz61gi_di" bpmnElement="Participant_0iz61gi" isHorizontal="true">
        <dc:Bounds x="151" y="80" width="679" height="470" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hvo7kf_di" bpmnElement="Lane_1hvo7kf" isHorizontal="true">
        <dc:Bounds x="181" y="80" width="649" height="150" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1721v09_di" bpmnElement="Lane_1721v09" isHorizontal="true">
        <dc:Bounds x="181" y="230" width="649" height="320" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="201" y="142" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_105o16f_di" bpmnElement="Activity_105o16f">
        <dc:Bounds x="289" y="120" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0f0waik_di" bpmnElement="Event_0f0waik">
        <dc:Bounds x="461" y="142" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="437" y="104.5" width="83" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01ajovt_di" bpmnElement="Event_01ajovt">
        <dc:Bounds x="461" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1k6mlf0" bpmnElement="Activity_15f6b65">
        <dc:Bounds x="569" y="270" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ni82hh_di" bpmnElement="Event_0ni82hh">
        <dc:Bounds x="741" y="292" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="715" y="256" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1p3kb51" bpmnElement="Event_18jmrc4">
        <dc:Bounds x="741" y="402" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="717" y="365.5" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1hqed9y" bpmnElement="Event_05zgkr1">
        <dc:Bounds x="741" y="492" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="717" y="456" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0bi2paz_di" bpmnElement="Flow_0bi2paz">
        <di:waypoint x="237" y="160" />
        <di:waypoint x="289" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s777au_di" bpmnElement="Flow_1s777au">
        <di:waypoint x="389" y="160" />
        <di:waypoint x="461" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05k4fw1_di" bpmnElement="Flow_05k4fw1">
        <di:waypoint x="479" y="178" />
        <di:waypoint x="479" y="292" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zf19rh_di" bpmnElement="Flow_0zf19rh">
        <di:waypoint x="497" y="310" />
        <di:waypoint x="569" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bm4kse_di" bpmnElement="Flow_1bm4kse">
        <di:waypoint x="669" y="310" />
        <di:waypoint x="741" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0obo4x3_di" bpmnElement="Flow_0obo4x3">
        <di:waypoint x="669" y="310" />
        <di:waypoint x="705" y="310" />
        <di:waypoint x="705" y="420" />
        <di:waypoint x="741" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vcv9vj_di" bpmnElement="Flow_0vcv9vj">
        <di:waypoint x="669" y="310" />
        <di:waypoint x="705" y="310" />
        <di:waypoint x="705" y="510" />
        <di:waypoint x="741" y="510" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
