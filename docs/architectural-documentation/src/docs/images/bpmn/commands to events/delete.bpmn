<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0w63w7p" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1crftmq">
    <bpmn:participant id="Participant_0iz61gi" name="Delete Entry" processRef="Process_061fgn0" />
    <bpmn:participant id="Participant_0fshrl1" name="Delete entry except certificate" processRef="Process_0yapw5k" />
    <bpmn:participant id="Participant_0o1b387" name="Person deceased" processRef="Process_14g3fq5" />
  </bpmn:collaboration>
  <bpmn:process id="Process_061fgn0" isExecutable="true">
    <bpmn:laneSet id="LaneSet_0qswr6d">
      <bpmn:lane id="Lane_1721v09">
        <bpmn:flowNodeRef>Activity_15f6b65</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0ni82hh</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_01ajovt</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1hvo7kf">
        <bpmn:flowNodeRef>Event_0f0waik</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_105o16f</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:serviceTask id="Activity_15f6b65" name="RegisterEntry">
      <bpmn:incoming>Flow_0zf19rh</bpmn:incoming>
      <bpmn:outgoing>Flow_1bm4kse</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0ni82hh" name="DeletedEvent">
      <bpmn:incoming>Flow_1bm4kse</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0cgp628" />
    </bpmn:endEvent>
    <bpmn:intermediateCatchEvent id="Event_01ajovt">
      <bpmn:incoming>Flow_04bvofx</bpmn:incoming>
      <bpmn:outgoing>Flow_0zf19rh</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0hapdln" />
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateThrowEvent id="Event_0f0waik" name="DeleteCommand">
      <bpmn:incoming>Flow_1s777au</bpmn:incoming>
      <bpmn:outgoing>Flow_04bvofx</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_13fupkm" />
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_105o16f" name="RegisterEntryController">
      <bpmn:incoming>Flow_0bi2paz</bpmn:incoming>
      <bpmn:outgoing>Flow_1s777au</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0bi2paz</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0zf19rh" sourceRef="Event_01ajovt" targetRef="Activity_15f6b65" />
    <bpmn:sequenceFlow id="Flow_1bm4kse" sourceRef="Activity_15f6b65" targetRef="Event_0ni82hh" />
    <bpmn:sequenceFlow id="Flow_04bvofx" sourceRef="Event_0f0waik" targetRef="Event_01ajovt" />
    <bpmn:sequenceFlow id="Flow_1s777au" sourceRef="Activity_105o16f" targetRef="Event_0f0waik" />
    <bpmn:sequenceFlow id="Flow_0bi2paz" sourceRef="StartEvent_1" targetRef="Activity_105o16f" />
  </bpmn:process>
  <bpmn:process id="Process_0yapw5k" isExecutable="true">
    <bpmn:laneSet id="LaneSet_07cbwqj">
      <bpmn:lane id="Lane_0fkup7j">
        <bpmn:flowNodeRef>Activity_1fzzxfu</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1asiqu0</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0ir1471</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_00yojh8">
        <bpmn:flowNodeRef>Event_1tlord1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1f5hlay</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1xab0yh</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:serviceTask id="Activity_1fzzxfu" name="RegisterEntry">
      <bpmn:incoming>Flow_17kha44</bpmn:incoming>
      <bpmn:outgoing>Flow_1panu7y</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1asiqu0" name="ExceptCertificateDeletedEvent">
      <bpmn:incoming>Flow_1panu7y</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1z0k4ku" />
    </bpmn:endEvent>
    <bpmn:intermediateCatchEvent id="Event_0ir1471">
      <bpmn:incoming>Flow_0efvvc9</bpmn:incoming>
      <bpmn:outgoing>Flow_17kha44</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1hddlvq" />
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateThrowEvent id="Event_1tlord1" name="DeleteExceptCertificateCommand">
      <bpmn:incoming>Flow_0g61x5q</bpmn:incoming>
      <bpmn:outgoing>Flow_0efvvc9</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1xmavp8" />
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_1f5hlay" name="RegisterEntryController">
      <bpmn:incoming>Flow_1kt46dv</bpmn:incoming>
      <bpmn:outgoing>Flow_0g61x5q</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="Event_1xab0yh">
      <bpmn:outgoing>Flow_1kt46dv</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_17kha44" sourceRef="Event_0ir1471" targetRef="Activity_1fzzxfu" />
    <bpmn:sequenceFlow id="Flow_1panu7y" sourceRef="Activity_1fzzxfu" targetRef="Event_1asiqu0" />
    <bpmn:sequenceFlow id="Flow_0efvvc9" sourceRef="Event_1tlord1" targetRef="Event_0ir1471" />
    <bpmn:sequenceFlow id="Flow_0g61x5q" sourceRef="Activity_1f5hlay" targetRef="Event_1tlord1" />
    <bpmn:sequenceFlow id="Flow_1kt46dv" sourceRef="Event_1xab0yh" targetRef="Activity_1f5hlay" />
  </bpmn:process>
  <bpmn:process id="Process_14g3fq5" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1vauqg7">
      <bpmn:lane id="Lane_1uj8rux">
        <bpmn:flowNodeRef>Activity_11icepm</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0ckh140</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_11vfobi</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1pggi2a">
        <bpmn:flowNodeRef>Event_1wo2qz0</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_00oxmhk</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_13lode0</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:serviceTask id="Activity_11icepm" name="RegisterEntry">
      <bpmn:incoming>Flow_1eif9dt</bpmn:incoming>
      <bpmn:outgoing>Flow_0r28ulj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0ckh140" name="PersonDeceasedEvent">
      <bpmn:incoming>Flow_0r28ulj</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_036l26j" />
    </bpmn:endEvent>
    <bpmn:intermediateCatchEvent id="Event_11vfobi">
      <bpmn:incoming>Flow_0q5ucnq</bpmn:incoming>
      <bpmn:outgoing>Flow_1eif9dt</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0xb05j0" />
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateThrowEvent id="Event_1wo2qz0" name="DeleteOnPerseonDeceasedCommand">
      <bpmn:incoming>Flow_093hs7h</bpmn:incoming>
      <bpmn:outgoing>Flow_0q5ucnq</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_02700ch" />
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_00oxmhk" name="RegisterEntryController">
      <bpmn:incoming>Flow_194nvtc</bpmn:incoming>
      <bpmn:outgoing>Flow_093hs7h</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="Event_13lode0">
      <bpmn:outgoing>Flow_194nvtc</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1eif9dt" sourceRef="Event_11vfobi" targetRef="Activity_11icepm" />
    <bpmn:sequenceFlow id="Flow_0r28ulj" sourceRef="Activity_11icepm" targetRef="Event_0ckh140" />
    <bpmn:sequenceFlow id="Flow_0q5ucnq" sourceRef="Event_1wo2qz0" targetRef="Event_11vfobi" />
    <bpmn:sequenceFlow id="Flow_093hs7h" sourceRef="Activity_00oxmhk" targetRef="Event_1wo2qz0" />
    <bpmn:sequenceFlow id="Flow_194nvtc" sourceRef="Event_13lode0" targetRef="Activity_00oxmhk" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1crftmq">
      <bpmndi:BPMNShape id="Participant_0iz61gi_di" bpmnElement="Participant_0iz61gi" isHorizontal="true">
        <dc:Bounds x="151" y="80" width="809" height="300" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1721v09_di" bpmnElement="Lane_1721v09" isHorizontal="true">
        <dc:Bounds x="181" y="220" width="779" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hvo7kf_di" bpmnElement="Lane_1hvo7kf" isHorizontal="true">
        <dc:Bounds x="181" y="80" width="779" height="140" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1k6mlf0" bpmnElement="Activity_15f6b65">
        <dc:Bounds x="655" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ni82hh_di" bpmnElement="Event_0ni82hh">
        <dc:Bounds x="827" y="302" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="815" y="273" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01ajovt_di" bpmnElement="Event_01ajovt">
        <dc:Bounds x="592" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0f0waik_di" bpmnElement="Event_0f0waik">
        <dc:Bounds x="472" y="132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="451" y="113" width="83" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_105o16f_di" bpmnElement="Activity_105o16f">
        <dc:Bounds x="300" y="110" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="212" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0zf19rh_di" bpmnElement="Flow_0zf19rh">
        <di:waypoint x="628" y="320" />
        <di:waypoint x="655" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bm4kse_di" bpmnElement="Flow_1bm4kse">
        <di:waypoint x="755" y="320" />
        <di:waypoint x="827" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04bvofx_di" bpmnElement="Flow_04bvofx">
        <di:waypoint x="508" y="150" />
        <di:waypoint x="545" y="150" />
        <di:waypoint x="545" y="320" />
        <di:waypoint x="592" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s777au_di" bpmnElement="Flow_1s777au">
        <di:waypoint x="400" y="150" />
        <di:waypoint x="472" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bi2paz_di" bpmnElement="Flow_0bi2paz">
        <di:waypoint x="248" y="150" />
        <di:waypoint x="300" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_0tvfn9k" bpmnElement="Participant_0fshrl1" isHorizontal="true">
        <dc:Bounds x="151" y="390" width="809" height="300" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1u2b3k2" bpmnElement="Lane_0fkup7j" isHorizontal="true">
        <dc:Bounds x="181" y="530" width="779" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0nn532n" bpmnElement="Lane_00yojh8" isHorizontal="true">
        <dc:Bounds x="181" y="390" width="779" height="140" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15c4zhw" bpmnElement="Activity_1fzzxfu">
        <dc:Bounds x="655" y="590" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0s3ezt6" bpmnElement="Event_1asiqu0">
        <dc:Bounds x="827" y="612" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="810" y="583" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0cz5q8c" bpmnElement="Event_0ir1471">
        <dc:Bounds x="592" y="612" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1s72a4f" bpmnElement="Event_1tlord1">
        <dc:Bounds x="472" y="442" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="447" y="406" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_08m299f" bpmnElement="Activity_1f5hlay">
        <dc:Bounds x="300" y="420" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0905mrh" bpmnElement="Event_1xab0yh">
        <dc:Bounds x="212" y="442" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0onasie" bpmnElement="Flow_17kha44">
        <di:waypoint x="628" y="630" />
        <di:waypoint x="655" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_08lvrvf" bpmnElement="Flow_1panu7y">
        <di:waypoint x="755" y="630" />
        <di:waypoint x="827" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_04nruhg" bpmnElement="Flow_0efvvc9">
        <di:waypoint x="508" y="460" />
        <di:waypoint x="545" y="460" />
        <di:waypoint x="545" y="630" />
        <di:waypoint x="592" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0uc25rg" bpmnElement="Flow_0g61x5q">
        <di:waypoint x="400" y="460" />
        <di:waypoint x="472" y="460" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1e2eb86" bpmnElement="Flow_1kt46dv">
        <di:waypoint x="248" y="460" />
        <di:waypoint x="300" y="460" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_0vudljd" bpmnElement="Participant_0o1b387" isHorizontal="true">
        <dc:Bounds x="151" y="700" width="809" height="300" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_190uyot" bpmnElement="Lane_1uj8rux" isHorizontal="true">
        <dc:Bounds x="181" y="840" width="779" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_02gr6rz" bpmnElement="Lane_1pggi2a" isHorizontal="true">
        <dc:Bounds x="181" y="700" width="779" height="140" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ovk4sl" bpmnElement="Activity_11icepm">
        <dc:Bounds x="655" y="900" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1pfzzuu" bpmnElement="Event_0ckh140">
        <dc:Bounds x="827" y="922" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="806" y="893" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1eapig0" bpmnElement="Event_11vfobi">
        <dc:Bounds x="592" y="922" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1n319rm" bpmnElement="Event_1wo2qz0">
        <dc:Bounds x="472" y="752" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="450" y="710" width="85" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0fm82fs" bpmnElement="Activity_00oxmhk">
        <dc:Bounds x="300" y="730" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0bbg6pe" bpmnElement="Event_13lode0">
        <dc:Bounds x="212" y="752" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0cw7zil" bpmnElement="Flow_1eif9dt">
        <di:waypoint x="628" y="940" />
        <di:waypoint x="655" y="940" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1lmoc8h" bpmnElement="Flow_0r28ulj">
        <di:waypoint x="755" y="940" />
        <di:waypoint x="827" y="940" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1skosq0" bpmnElement="Flow_0q5ucnq">
        <di:waypoint x="508" y="770" />
        <di:waypoint x="545" y="770" />
        <di:waypoint x="545" y="940" />
        <di:waypoint x="592" y="940" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1wb2ftn" bpmnElement="Flow_093hs7h">
        <di:waypoint x="400" y="770" />
        <di:waypoint x="472" y="770" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0o7b8uq" bpmnElement="Flow_194nvtc">
        <di:waypoint x="248" y="770" />
        <di:waypoint x="300" y="770" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
