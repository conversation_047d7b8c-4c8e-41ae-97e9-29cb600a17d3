<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0w63w7p" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1crftmq">
    <bpmn:participant id="Participant_0iz61gi" name="Replace Card" processRef="Process_061fgn0" />
  </bpmn:collaboration>
  <bpmn:process id="Process_061fgn0" isExecutable="true">
    <bpmn:laneSet id="LaneSet_0qswr6d">
      <bpmn:lane id="Lane_1hvo7kf">
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_105o16f</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0f0waik</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0c4my7k</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1ahm745</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_10r2w4v</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1721v09">
        <bpmn:flowNodeRef>Activity_15f6b65</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0ni82hh</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_05zgkr1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_01ajovt</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0h20fhe</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0bi2paz</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_105o16f" name="FishingLicenseController">
      <bpmn:incoming>Flow_0bi2paz</bpmn:incoming>
      <bpmn:outgoing>Flow_1s777au</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_0f0waik" name="OrderReplacementCardCommand">
      <bpmn:incoming>Flow_1s777au</bpmn:incoming>
      <bpmn:outgoing>Flow_04bvofx</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_13fupkm" />
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0bi2paz" sourceRef="StartEvent_1" targetRef="Activity_105o16f" />
    <bpmn:sequenceFlow id="Flow_1s777au" sourceRef="Activity_105o16f" targetRef="Event_0f0waik" />
    <bpmn:startEvent id="Event_0c4my7k">
      <bpmn:outgoing>Flow_1yh8v9n</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_1ahm745" name="OnlineServiceController">
      <bpmn:incoming>Flow_1yh8v9n</bpmn:incoming>
      <bpmn:outgoing>Flow_1besibl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_15f6b65" name="RegisterEntry">
      <bpmn:incoming>Flow_0zf19rh</bpmn:incoming>
      <bpmn:outgoing>Flow_1bm4kse</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vcv9vj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0iomyet</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0ni82hh" name="ReplacementCardOrderedEvent">
      <bpmn:incoming>Flow_1bm4kse</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0cgp628" />
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_05zgkr1" name="FishingTaxPayedEvent">
      <bpmn:incoming>Flow_0vcv9vj</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1woaxbx" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0zf19rh" sourceRef="Event_01ajovt" targetRef="Activity_15f6b65" />
    <bpmn:sequenceFlow id="Flow_1bm4kse" sourceRef="Activity_15f6b65" targetRef="Event_0ni82hh" />
    <bpmn:sequenceFlow id="Flow_0vcv9vj" sourceRef="Activity_15f6b65" targetRef="Event_05zgkr1" />
    <bpmn:intermediateThrowEvent id="Event_10r2w4v" name="OSReplaceCardCommand">
      <bpmn:incoming>Flow_1besibl</bpmn:incoming>
      <bpmn:outgoing>Flow_19pkgzj</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_065xo7n" />
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateCatchEvent id="Event_01ajovt">
      <bpmn:incoming>Flow_19pkgzj</bpmn:incoming>
      <bpmn:incoming>Flow_04bvofx</bpmn:incoming>
      <bpmn:outgoing>Flow_0zf19rh</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0hapdln" />
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_19pkgzj" sourceRef="Event_10r2w4v" targetRef="Event_01ajovt" />
    <bpmn:sequenceFlow id="Flow_04bvofx" sourceRef="Event_0f0waik" targetRef="Event_01ajovt" />
    <bpmn:sequenceFlow id="Flow_1yh8v9n" sourceRef="Event_0c4my7k" targetRef="Activity_1ahm745" />
    <bpmn:sequenceFlow id="Flow_1besibl" sourceRef="Activity_1ahm745" targetRef="Event_10r2w4v" />
    <bpmn:endEvent id="Event_0h20fhe" name="JurisdictionMovedEvent">
      <bpmn:incoming>Flow_0iomyet</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1ip9cv2" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0iomyet" sourceRef="Activity_15f6b65" targetRef="Event_0h20fhe" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1crftmq">
      <bpmndi:BPMNShape id="Participant_0iz61gi_di" bpmnElement="Participant_0iz61gi" isHorizontal="true">
        <dc:Bounds x="151" y="80" width="809" height="640" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1721v09_di" bpmnElement="Lane_1721v09" isHorizontal="true">
        <dc:Bounds x="181" y="350" width="779" height="370" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hvo7kf_di" bpmnElement="Lane_1hvo7kf" isHorizontal="true">
        <dc:Bounds x="181" y="80" width="779" height="270" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="201" y="142" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_105o16f_di" bpmnElement="Activity_105o16f">
        <dc:Bounds x="289" y="120" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0f0waik_di" bpmnElement="Event_0f0waik">
        <dc:Bounds x="461" y="142" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="438" y="105" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15t7tbn" bpmnElement="Event_0c4my7k">
        <dc:Bounds x="201" y="252" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_139ydk2" bpmnElement="Activity_1ahm745">
        <dc:Bounds x="289" y="230" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1k6mlf0" bpmnElement="Activity_15f6b65">
        <dc:Bounds x="655" y="410" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ni82hh_di" bpmnElement="Event_0ni82hh">
        <dc:Bounds x="827" y="432" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="805" y="396" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1hqed9y" bpmnElement="Event_05zgkr1">
        <dc:Bounds x="827" y="552" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="803" y="516" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_09p5ybc" bpmnElement="Event_10r2w4v">
        <dc:Bounds x="461" y="252" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="439" y="215" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01ajovt_di" bpmnElement="Event_01ajovt">
        <dc:Bounds x="592" y="432" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1xgcaze" bpmnElement="Event_0h20fhe">
        <dc:Bounds x="827" y="662" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="804" y="626" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0bi2paz_di" bpmnElement="Flow_0bi2paz">
        <di:waypoint x="237" y="160" />
        <di:waypoint x="289" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s777au_di" bpmnElement="Flow_1s777au">
        <di:waypoint x="389" y="160" />
        <di:waypoint x="461" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zf19rh_di" bpmnElement="Flow_0zf19rh">
        <di:waypoint x="628" y="450" />
        <di:waypoint x="655" y="450" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bm4kse_di" bpmnElement="Flow_1bm4kse">
        <di:waypoint x="755" y="450" />
        <di:waypoint x="827" y="450" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vcv9vj_di" bpmnElement="Flow_0vcv9vj">
        <di:waypoint x="755" y="450" />
        <di:waypoint x="791" y="450" />
        <di:waypoint x="791" y="570" />
        <di:waypoint x="827" y="570" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19pkgzj_di" bpmnElement="Flow_19pkgzj">
        <di:waypoint x="497" y="270" />
        <di:waypoint x="545" y="270" />
        <di:waypoint x="545" y="450" />
        <di:waypoint x="592" y="450" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04bvofx_di" bpmnElement="Flow_04bvofx">
        <di:waypoint x="497" y="160" />
        <di:waypoint x="545" y="160" />
        <di:waypoint x="545" y="450" />
        <di:waypoint x="592" y="450" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yh8v9n_di" bpmnElement="Flow_1yh8v9n">
        <di:waypoint x="237" y="270" />
        <di:waypoint x="289" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1besibl_di" bpmnElement="Flow_1besibl">
        <di:waypoint x="389" y="270" />
        <di:waypoint x="461" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0iomyet_di" bpmnElement="Flow_0iomyet">
        <di:waypoint x="755" y="450" />
        <di:waypoint x="791" y="450" />
        <di:waypoint x="791" y="680" />
        <di:waypoint x="827" y="680" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
