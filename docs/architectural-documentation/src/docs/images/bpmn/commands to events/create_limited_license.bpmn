<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0w63w7p" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1crftmq">
    <bpmn:participant id="Participant_0iz61gi" name="Create Limited License" processRef="Process_061fgn0" />
  </bpmn:collaboration>
  <bpmn:process id="Process_061fgn0" isExecutable="true">
    <bpmn:laneSet id="LaneSet_0qswr6d">
      <bpmn:lane id="Lane_1hvo7kf">
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_105o16f</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0f0waik</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1721v09">
        <bpmn:flowNodeRef>Activity_15f6b65</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0ni82hh</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_05zgkr1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_01ajovt</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1pzn775</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0ahwdvv</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:serviceTask id="Activity_15f6b65" name="RegisterEntry">
      <bpmn:incoming>Flow_0zf19rh</bpmn:incoming>
      <bpmn:outgoing>Flow_1bm4kse</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vcv9vj</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k96u2p</bpmn:outgoing>
      <bpmn:outgoing>Flow_02btmnq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0ni82hh" name="LimitedLicenseCreatedEvent">
      <bpmn:incoming>Flow_1bm4kse</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0cgp628" />
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_05zgkr1" name="PersonalDataChangedEvent">
      <bpmn:incoming>Flow_0vcv9vj</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1woaxbx" />
    </bpmn:endEvent>
    <bpmn:intermediateCatchEvent id="Event_01ajovt">
      <bpmn:incoming>Flow_04bvofx</bpmn:incoming>
      <bpmn:outgoing>Flow_0zf19rh</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0hapdln" />
    </bpmn:intermediateCatchEvent>
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0bi2paz</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_105o16f" name="FishingLicenseController">
      <bpmn:incoming>Flow_0bi2paz</bpmn:incoming>
      <bpmn:outgoing>Flow_1s777au</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_0f0waik" name="CreateLimitedLicenseCommand">
      <bpmn:incoming>Flow_1s777au</bpmn:incoming>
      <bpmn:outgoing>Flow_04bvofx</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_13fupkm" />
    </bpmn:intermediateThrowEvent>
    <bpmn:endEvent id="Event_1pzn775" name="FishingTaxPayedEvent">
      <bpmn:incoming>Flow_1k96u2p</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_075vco0" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0zf19rh" sourceRef="Event_01ajovt" targetRef="Activity_15f6b65" />
    <bpmn:sequenceFlow id="Flow_1bm4kse" sourceRef="Activity_15f6b65" targetRef="Event_0ni82hh" />
    <bpmn:sequenceFlow id="Flow_0vcv9vj" sourceRef="Activity_15f6b65" targetRef="Event_05zgkr1" />
    <bpmn:sequenceFlow id="Flow_1k96u2p" sourceRef="Activity_15f6b65" targetRef="Event_1pzn775" />
    <bpmn:sequenceFlow id="Flow_04bvofx" sourceRef="Event_0f0waik" targetRef="Event_01ajovt" />
    <bpmn:sequenceFlow id="Flow_0bi2paz" sourceRef="StartEvent_1" targetRef="Activity_105o16f" />
    <bpmn:sequenceFlow id="Flow_1s777au" sourceRef="Activity_105o16f" targetRef="Event_0f0waik" />
    <bpmn:endEvent id="Event_0ahwdvv" name="JurisdictionMovedEvent">
      <bpmn:incoming>Flow_02btmnq</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1f7rhx5" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_02btmnq" sourceRef="Activity_15f6b65" targetRef="Event_0ahwdvv" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1crftmq">
      <bpmndi:BPMNShape id="Participant_0iz61gi_di" bpmnElement="Participant_0iz61gi" isHorizontal="true">
        <dc:Bounds x="151" y="80" width="759" height="590" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hvo7kf_di" bpmnElement="Lane_1hvo7kf" isHorizontal="true">
        <dc:Bounds x="181" y="80" width="729" height="130" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1721v09_di" bpmnElement="Lane_1721v09" isHorizontal="true">
        <dc:Bounds x="181" y="210" width="729" height="460" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1k6mlf0" bpmnElement="Activity_15f6b65">
        <dc:Bounds x="655" y="270" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ni82hh_di" bpmnElement="Event_0ni82hh">
        <dc:Bounds x="827" y="292" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="803" y="256" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1hqed9y" bpmnElement="Event_05zgkr1">
        <dc:Bounds x="827" y="412" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="805" y="376" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01ajovt_di" bpmnElement="Event_01ajovt">
        <dc:Bounds x="592" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="212" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_105o16f_di" bpmnElement="Activity_105o16f">
        <dc:Bounds x="300" y="110" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0f0waik_di" bpmnElement="Event_0f0waik">
        <dc:Bounds x="472" y="132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="449" y="96" width="83" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0537ebx" bpmnElement="Event_1pzn775">
        <dc:Bounds x="827" y="512" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="804" y="476" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1em3b06" bpmnElement="Event_0ahwdvv">
        <dc:Bounds x="827" y="612" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="805" y="576" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0zf19rh_di" bpmnElement="Flow_0zf19rh">
        <di:waypoint x="628" y="310" />
        <di:waypoint x="655" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bm4kse_di" bpmnElement="Flow_1bm4kse">
        <di:waypoint x="755" y="310" />
        <di:waypoint x="827" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vcv9vj_di" bpmnElement="Flow_0vcv9vj">
        <di:waypoint x="755" y="310" />
        <di:waypoint x="791" y="310" />
        <di:waypoint x="791" y="430" />
        <di:waypoint x="827" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k96u2p_di" bpmnElement="Flow_1k96u2p">
        <di:waypoint x="755" y="310" />
        <di:waypoint x="791" y="310" />
        <di:waypoint x="791" y="530" />
        <di:waypoint x="827" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04bvofx_di" bpmnElement="Flow_04bvofx">
        <di:waypoint x="508" y="150" />
        <di:waypoint x="545" y="150" />
        <di:waypoint x="545" y="310" />
        <di:waypoint x="592" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bi2paz_di" bpmnElement="Flow_0bi2paz">
        <di:waypoint x="248" y="150" />
        <di:waypoint x="300" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s777au_di" bpmnElement="Flow_1s777au">
        <di:waypoint x="400" y="150" />
        <di:waypoint x="472" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02btmnq_di" bpmnElement="Flow_02btmnq">
        <di:waypoint x="755" y="310" />
        <di:waypoint x="791" y="310" />
        <di:waypoint x="791" y="630" />
        <di:waypoint x="827" y="630" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
