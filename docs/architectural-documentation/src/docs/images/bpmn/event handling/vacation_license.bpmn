<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1prd8mo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1heijwu">
    <bpmn:participant id="Participant_1mj9yvm" name="Limited License Applciation Created" processRef="Process_1ym35ia" />
    <bpmn:participant id="Participant_0hr27te" name="Limited License Applciation Rejected" processRef="Process_07aptvy" />
    <bpmn:participant id="Participant_0cmaryz" name="Limited License Created" processRef="Process_066y1t5" />
  </bpmn:collaboration>
  <bpmn:process id="Process_1ym35ia" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1qyqb0a">
      <bpmn:lane id="Lane_1hyemlq">
        <bpmn:flowNodeRef>Event_1qqr54o</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Gateway_00zkx50</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1o06oal</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1gfe613</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0bw5qvj</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_132qmb5</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:sequenceFlow id="Flow_1ylz1cf" sourceRef="Event_1qqr54o" targetRef="Gateway_00zkx50" />
    <bpmn:startEvent id="Event_1qqr54o" name="LimitedLienseApplicationCreatedEvent">
      <bpmn:outgoing>Flow_1ylz1cf</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_186jx1p" />
    </bpmn:startEvent>
    <bpmn:parallelGateway id="Gateway_00zkx50">
      <bpmn:incoming>Flow_1ylz1cf</bpmn:incoming>
      <bpmn:outgoing>Flow_1ufn6u2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1pln2dt</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_1ufn6u2" sourceRef="Gateway_00zkx50" targetRef="Activity_1o06oal" />
    <bpmn:task id="Activity_1o06oal" name="Views aktualisieren">
      <bpmn:incoming>Flow_1ufn6u2</bpmn:incoming>
      <bpmn:outgoing>Flow_0u4byx2</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_1gfe613" name="Nachricht an das Servicekonto senden">
      <bpmn:incoming>Flow_1pln2dt</bpmn:incoming>
      <bpmn:outgoing>Flow_1w49agq</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_0u4byx2" sourceRef="Activity_1o06oal" targetRef="Event_0bw5qvj" />
    <bpmn:endEvent id="Event_0bw5qvj">
      <bpmn:incoming>Flow_0u4byx2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1pln2dt" sourceRef="Gateway_00zkx50" targetRef="Activity_1gfe613" />
    <bpmn:endEvent id="Event_132qmb5">
      <bpmn:incoming>Flow_1w49agq</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1w49agq" sourceRef="Activity_1gfe613" targetRef="Event_132qmb5" />
  </bpmn:process>
  <bpmn:process id="Process_07aptvy" isExecutable="false">
    <bpmn:startEvent id="Event_0und70e" name="LimitedLicenseApplciationRejectedEvent">
      <bpmn:outgoing>Flow_09u4fob</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0vug6ti" />
    </bpmn:startEvent>
    <bpmn:task id="Activity_1741zr1" name="Views aktualisieren">
      <bpmn:incoming>Flow_0l8j63s</bpmn:incoming>
      <bpmn:outgoing>Flow_1rqm3gu</bpmn:outgoing>
    </bpmn:task>
    <bpmn:parallelGateway id="Gateway_1m6cn25">
      <bpmn:incoming>Flow_09u4fob</bpmn:incoming>
      <bpmn:outgoing>Flow_0l8j63s</bpmn:outgoing>
      <bpmn:outgoing>Flow_16eoy6v</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:endEvent id="Event_00t0fxb">
      <bpmn:incoming>Flow_1rqm3gu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09u4fob" sourceRef="Event_0und70e" targetRef="Gateway_1m6cn25" />
    <bpmn:sequenceFlow id="Flow_0l8j63s" sourceRef="Gateway_1m6cn25" targetRef="Activity_1741zr1" />
    <bpmn:sequenceFlow id="Flow_1rqm3gu" sourceRef="Activity_1741zr1" targetRef="Event_00t0fxb" />
    <bpmn:sequenceFlow id="Flow_16eoy6v" sourceRef="Gateway_1m6cn25" targetRef="Activity_13riysh" />
    <bpmn:task id="Activity_13riysh" name="Nachricht an das Servicekonto senden">
      <bpmn:incoming>Flow_16eoy6v</bpmn:incoming>
      <bpmn:outgoing>Flow_1k6n342</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_0xnkz65">
      <bpmn:incoming>Flow_1k6n342</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1k6n342" sourceRef="Activity_13riysh" targetRef="Event_0xnkz65" />
  </bpmn:process>
  <bpmn:process id="Process_066y1t5" isExecutable="false">
    <bpmn:startEvent id="Event_0sjby6k" name="LimitedLicenseCretedEvent">
      <bpmn:outgoing>Flow_15vqwpj</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0ldcbjh" />
    </bpmn:startEvent>
    <bpmn:task id="Activity_0i7r2el" name="Views aktualisieren">
      <bpmn:incoming>Flow_1wmd7q2</bpmn:incoming>
      <bpmn:outgoing>Flow_1nqri98</bpmn:outgoing>
    </bpmn:task>
    <bpmn:parallelGateway id="Gateway_1ghmy0h">
      <bpmn:incoming>Flow_15vqwpj</bpmn:incoming>
      <bpmn:outgoing>Flow_1wmd7q2</bpmn:outgoing>
      <bpmn:outgoing>Flow_18drcp4</bpmn:outgoing>
      <bpmn:outgoing>Flow_09qawgm</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:endEvent id="Event_129i41c">
      <bpmn:incoming>Flow_1nqri98</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:task id="Activity_0hfilc3" name="Dokumente an das Servicekonto senden">
      <bpmn:incoming>Flow_18drcp4</bpmn:incoming>
      <bpmn:outgoing>Flow_050a6sx</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_1rfhers">
      <bpmn:incoming>Flow_050a6sx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_15vqwpj" sourceRef="Event_0sjby6k" targetRef="Gateway_1ghmy0h" />
    <bpmn:sequenceFlow id="Flow_1wmd7q2" sourceRef="Gateway_1ghmy0h" targetRef="Activity_0i7r2el" />
    <bpmn:sequenceFlow id="Flow_1nqri98" sourceRef="Activity_0i7r2el" targetRef="Event_129i41c" />
    <bpmn:sequenceFlow id="Flow_18drcp4" sourceRef="Gateway_1ghmy0h" targetRef="Activity_0hfilc3" />
    <bpmn:sequenceFlow id="Flow_050a6sx" sourceRef="Activity_0hfilc3" targetRef="Event_1rfhers" />
    <bpmn:task id="Activity_1tw0w6y" name="Scheckkarte bestellen">
      <bpmn:incoming>Flow_09qawgm</bpmn:incoming>
      <bpmn:outgoing>Flow_1idqf4o</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_1pu8834">
      <bpmn:incoming>Flow_1idqf4o</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1idqf4o" sourceRef="Activity_1tw0w6y" targetRef="Event_1pu8834" />
    <bpmn:sequenceFlow id="Flow_09qawgm" sourceRef="Gateway_1ghmy0h" targetRef="Activity_1tw0w6y" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1heijwu">
      <bpmndi:BPMNShape id="Participant_1mj9yvm_di" bpmnElement="Participant_1mj9yvm" isHorizontal="true">
        <dc:Bounds x="132" y="80" width="528" height="240" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hyemlq_di" bpmnElement="Lane_1hyemlq" isHorizontal="true">
        <dc:Bounds x="162" y="80" width="498" height="240" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qqr54o_di" bpmnElement="Event_1qqr54o">
        <dc:Bounds x="192" y="132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="166" y="175" width="89" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_00zkx50_di" bpmnElement="Gateway_00zkx50">
        <dc:Bounds x="285" y="125" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o06oal_di" bpmnElement="Activity_1o06oal">
        <dc:Bounds x="400" y="110" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_179u8p8" bpmnElement="Activity_1gfe613">
        <dc:Bounds x="400" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rvi60e" bpmnElement="Event_0bw5qvj">
        <dc:Bounds x="582" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_002juae" bpmnElement="Event_132qmb5">
        <dc:Bounds x="582" y="242" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ylz1cf_di" bpmnElement="Flow_1ylz1cf">
        <di:waypoint x="228" y="150" />
        <di:waypoint x="285" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ufn6u2_di" bpmnElement="Flow_1ufn6u2">
        <di:waypoint x="335" y="150" />
        <di:waypoint x="400" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u4byx2_di" bpmnElement="Flow_0u4byx2">
        <di:waypoint x="500" y="150" />
        <di:waypoint x="582" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pln2dt_di" bpmnElement="Flow_1pln2dt">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="260" />
        <di:waypoint x="400" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w49agq_di" bpmnElement="Flow_1w49agq">
        <di:waypoint x="500" y="260" />
        <di:waypoint x="582" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Participant_0hr27te_di" bpmnElement="Participant_0hr27te" isHorizontal="true">
        <dc:Bounds x="132" y="340" width="526" height="210" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_02w8azp" bpmnElement="Event_0und70e">
        <dc:Bounds x="202" y="372" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="177" y="415" width="88" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1vi75zp" bpmnElement="Activity_1741zr1">
        <dc:Bounds x="410" y="350" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1sib1lh" bpmnElement="Gateway_1m6cn25">
        <dc:Bounds x="295" y="365" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1r6agne" bpmnElement="Event_00t0fxb">
        <dc:Bounds x="592" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rt4vv9" bpmnElement="Activity_13riysh">
        <dc:Bounds x="410" y="450" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0de43ex" bpmnElement="Event_0xnkz65">
        <dc:Bounds x="592" y="472" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0dvkjo5" bpmnElement="Flow_09u4fob">
        <di:waypoint x="238" y="390" />
        <di:waypoint x="295" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0sgiqr7" bpmnElement="Flow_0l8j63s">
        <di:waypoint x="345" y="390" />
        <di:waypoint x="410" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_02oxj69" bpmnElement="Flow_1rqm3gu">
        <di:waypoint x="510" y="390" />
        <di:waypoint x="592" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16eoy6v_di" bpmnElement="Flow_16eoy6v">
        <di:waypoint x="320" y="415" />
        <di:waypoint x="320" y="490" />
        <di:waypoint x="410" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k6n342_di" bpmnElement="Flow_1k6n342">
        <di:waypoint x="510" y="490" />
        <di:waypoint x="592" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_1mjq8r8" bpmnElement="Participant_0cmaryz" isHorizontal="true">
        <dc:Bounds x="132" y="580" width="526" height="310" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ewd71y" bpmnElement="Event_0sjby6k">
        <dc:Bounds x="202" y="612" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="177" y="655" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0vchoy6" bpmnElement="Activity_0i7r2el">
        <dc:Bounds x="410" y="590" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0crfrfg" bpmnElement="Gateway_1ghmy0h">
        <dc:Bounds x="295" y="605" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0o35e1w" bpmnElement="Event_129i41c">
        <dc:Bounds x="592" y="612" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qefp4k" bpmnElement="Activity_0hfilc3">
        <dc:Bounds x="410" y="690" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1dww07u" bpmnElement="Event_1rfhers">
        <dc:Bounds x="592" y="712" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0oikqbi" bpmnElement="Activity_1tw0w6y">
        <dc:Bounds x="410" y="790" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1os3rvi" bpmnElement="Event_1pu8834">
        <dc:Bounds x="592" y="812" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0hbkmqv" bpmnElement="Flow_15vqwpj">
        <di:waypoint x="238" y="630" />
        <di:waypoint x="295" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1l1sze3" bpmnElement="Flow_1wmd7q2">
        <di:waypoint x="345" y="630" />
        <di:waypoint x="410" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ehlmgy" bpmnElement="Flow_1nqri98">
        <di:waypoint x="510" y="630" />
        <di:waypoint x="592" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_04q70fm" bpmnElement="Flow_18drcp4">
        <di:waypoint x="320" y="655" />
        <di:waypoint x="320" y="730" />
        <di:waypoint x="410" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0cn15gp" bpmnElement="Flow_050a6sx">
        <di:waypoint x="510" y="730" />
        <di:waypoint x="592" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1k5w85f" bpmnElement="Flow_1idqf4o">
        <di:waypoint x="510" y="830" />
        <di:waypoint x="592" y="830" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09qawgm_di" bpmnElement="Flow_09qawgm">
        <di:waypoint x="320" y="655" />
        <di:waypoint x="320" y="830" />
        <di:waypoint x="410" y="830" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
