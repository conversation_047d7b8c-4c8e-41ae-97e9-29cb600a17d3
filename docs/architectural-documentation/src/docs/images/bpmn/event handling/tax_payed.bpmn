<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1prd8mo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1heijwu">
    <bpmn:participant id="Participant_1mj9yvm" name="Tax Payed" processRef="Process_1ym35ia" />
  </bpmn:collaboration>
  <bpmn:process id="Process_1ym35ia" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1qyqb0a">
      <bpmn:lane id="Lane_0xrwvpv">
        <bpmn:flowNodeRef>Gateway_00zkx50</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1qqr54o</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1hyemlq">
        <bpmn:flowNodeRef>Activity_1w7qzs8</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0wnvvba</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1qef1k1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0x3dwzb</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1uohq3j</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1ni80by</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_078et1z</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0o9dzkz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_060p3aw</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0xfpll3">
        <bpmn:flowNodeRef>Activity_1m6p91l</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1gazwal</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_09rqs5a</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0f6ijnq</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:sequenceFlow id="Flow_1ylz1cf" sourceRef="Event_1qqr54o" targetRef="Gateway_00zkx50" />
    <bpmn:sequenceFlow id="Flow_1e2cv8r" sourceRef="Activity_0o9dzkz" targetRef="Event_060p3aw" />
    <bpmn:sequenceFlow id="Flow_1sq8f28" sourceRef="Activity_078et1z" targetRef="Activity_0o9dzkz" />
    <bpmn:sequenceFlow id="Flow_1n2nwrh" sourceRef="Gateway_00zkx50" targetRef="Activity_078et1z" />
    <bpmn:sequenceFlow id="Flow_1nc8xvi" sourceRef="Activity_0x3dwzb" targetRef="Event_1uohq3j" />
    <bpmn:sequenceFlow id="Flow_1jt3y6n" sourceRef="Activity_1qef1k1" targetRef="Activity_0x3dwzb" />
    <bpmn:sequenceFlow id="Flow_1y5mos7" sourceRef="Gateway_00zkx50" targetRef="Activity_1qef1k1" />
    <bpmn:sequenceFlow id="Flow_1xttbxv" sourceRef="Activity_0wnvvba" targetRef="Event_1ni80by" />
    <bpmn:sequenceFlow id="Flow_1lvw11h" sourceRef="Activity_1w7qzs8" targetRef="Activity_0wnvvba" />
    <bpmn:sequenceFlow id="Flow_1ahgxfz" sourceRef="Gateway_00zkx50" targetRef="Activity_1w7qzs8" />
    <bpmn:startEvent id="Event_1qqr54o" name="FishingTaxPayedEvent">
      <bpmn:outgoing>Flow_1ylz1cf</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_186jx1p" />
    </bpmn:startEvent>
    <bpmn:parallelGateway id="Gateway_00zkx50">
      <bpmn:incoming>Flow_1ylz1cf</bpmn:incoming>
      <bpmn:outgoing>Flow_1ahgxfz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1y5mos7</bpmn:outgoing>
      <bpmn:outgoing>Flow_1n2nwrh</bpmn:outgoing>
      <bpmn:outgoing>Flow_1i6g2dt</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:dataStoreReference id="DataStoreReference_08oup82" name="FeesStatisticsView" />
    <bpmn:endEvent id="Event_060p3aw">
      <bpmn:incoming>Flow_1e2cv8r</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_0o9dzkz" name="TaxesStatisticsViewService">
      <bpmn:incoming>Flow_1sq8f28</bpmn:incoming>
      <bpmn:outgoing>Flow_1e2cv8r</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_1mtue80">
        <bpmn:targetRef>DataStoreReference_08oup82</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_078et1z" name="TaxesStatisticsHandler">
      <bpmn:incoming>Flow_1n2nwrh</bpmn:incoming>
      <bpmn:outgoing>Flow_1sq8f28</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1ni80by">
      <bpmn:incoming>Flow_1xttbxv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1uohq3j">
      <bpmn:incoming>Flow_1nc8xvi</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:dataStoreReference id="DataStoreReference_0132rdp" name="IdentifiactionDcoumentsView" />
    <bpmn:serviceTask id="Activity_0x3dwzb" name="IdentificationDocumentsVIewService">
      <bpmn:incoming>Flow_1jt3y6n</bpmn:incoming>
      <bpmn:outgoing>Flow_1nc8xvi</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_17p3vop">
        <bpmn:targetRef>DataStoreReference_0132rdp</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1qef1k1" name="IdentificationDocumentsViewHandler">
      <bpmn:incoming>Flow_1y5mos7</bpmn:incoming>
      <bpmn:outgoing>Flow_1jt3y6n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:dataStoreReference id="DataStoreReference_0kbef7o" name="RegisterEntrySearchView" />
    <bpmn:serviceTask id="Activity_0wnvvba" name="RegisterEntrySearchViewService">
      <bpmn:incoming>Flow_1lvw11h</bpmn:incoming>
      <bpmn:outgoing>Flow_1xttbxv</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_189zys4">
        <bpmn:targetRef>DataStoreReference_0kbef7o</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1w7qzs8" name="RegisterEntrySearchViewHandler">
      <bpmn:incoming>Flow_1ahgxfz</bpmn:incoming>
      <bpmn:outgoing>Flow_1lvw11h</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1i6g2dt" sourceRef="Gateway_00zkx50" targetRef="Activity_1gazwal" />
    <bpmn:serviceTask id="Activity_1m6p91l" name="OSInboxService">
      <bpmn:incoming>Flow_07nxxw6</bpmn:incoming>
      <bpmn:outgoing>Flow_0y0l699</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1gazwal" name="MessageServiceHandler">
      <bpmn:incoming>Flow_1i6g2dt</bpmn:incoming>
      <bpmn:outgoing>Flow_0lbxkvo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_09rqs5a" name="OSSuccessMessageService">
      <bpmn:incoming>Flow_0lbxkvo</bpmn:incoming>
      <bpmn:outgoing>Flow_07nxxw6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0f6ijnq">
      <bpmn:incoming>Flow_0y0l699</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_07nxxw6" sourceRef="Activity_09rqs5a" targetRef="Activity_1m6p91l" />
    <bpmn:sequenceFlow id="Flow_0y0l699" sourceRef="Activity_1m6p91l" targetRef="Event_0f6ijnq" />
    <bpmn:sequenceFlow id="Flow_0lbxkvo" sourceRef="Activity_1gazwal" targetRef="Activity_09rqs5a" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1heijwu">
      <bpmndi:BPMNShape id="Participant_1mj9yvm_di" bpmnElement="Participant_1mj9yvm" isHorizontal="true">
        <dc:Bounds x="132" y="80" width="828" height="930" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hyemlq_di" bpmnElement="Lane_1hyemlq" isHorizontal="true">
        <dc:Bounds x="162" y="220" width="798" height="670" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0xrwvpv_di" bpmnElement="Lane_0xrwvpv" isHorizontal="true">
        <dc:Bounds x="162" y="80" width="798" height="140" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0xfpll3_di" bpmnElement="Lane_0xfpll3" isHorizontal="true">
        <dc:Bounds x="162" y="890" width="798" height="120" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w7qzs8_di" bpmnElement="Activity_1w7qzs8">
        <dc:Bounds x="400" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1kzkggo" bpmnElement="Activity_0wnvvba">
        <dc:Bounds x="570" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DataStoreReference_0kbef7o_di" bpmnElement="DataStoreReference_0kbef7o">
        <dc:Bounds x="595" y="355" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="580" y="412" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0sml0cw" bpmnElement="Activity_1qef1k1">
        <dc:Bounds x="400" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1dvlwoi" bpmnElement="Activity_0x3dwzb">
        <dc:Bounds x="570" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ci317b" bpmnElement="DataStoreReference_0132rdp">
        <dc:Bounds x="595" y="575" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="584" y="632" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1uohq3j_di" bpmnElement="Event_1uohq3j">
        <dc:Bounds x="742" y="492" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ni80by_di" bpmnElement="Event_1ni80by">
        <dc:Bounds x="742" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1cspefr" bpmnElement="Activity_078et1z">
        <dc:Bounds x="400" y="690" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0e5k4k3" bpmnElement="Activity_0o9dzkz">
        <dc:Bounds x="570" y="690" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_060p3aw_di" bpmnElement="Event_060p3aw">
        <dc:Bounds x="742" y="712" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04x6szu" bpmnElement="DataStoreReference_08oup82">
        <dc:Bounds x="595" y="795" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="582" y="852" width="79" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_00zkx50_di" bpmnElement="Gateway_00zkx50">
        <dc:Bounds x="285" y="125" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qqr54o_di" bpmnElement="Event_1qqr54o">
        <dc:Bounds x="192" y="132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="168" y="175" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0e3rfhh" bpmnElement="Activity_1m6p91l">
        <dc:Bounds x="730" y="910" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0kxogdt" bpmnElement="Activity_1gazwal">
        <dc:Bounds x="400" y="910" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0k4b0b0" bpmnElement="Activity_09rqs5a">
        <dc:Bounds x="570" y="910" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0e7t3pj" bpmnElement="Event_0f6ijnq">
        <dc:Bounds x="882" y="932" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ahgxfz_di" bpmnElement="Flow_1ahgxfz">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="290" />
        <di:waypoint x="400" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lvw11h_di" bpmnElement="Flow_1lvw11h">
        <di:waypoint x="500" y="290" />
        <di:waypoint x="570" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xttbxv_di" bpmnElement="Flow_1xttbxv">
        <di:waypoint x="670" y="290" />
        <di:waypoint x="742" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1y5mos7_di" bpmnElement="Flow_1y5mos7">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="510" />
        <di:waypoint x="400" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jt3y6n_di" bpmnElement="Flow_1jt3y6n">
        <di:waypoint x="500" y="510" />
        <di:waypoint x="570" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nc8xvi_di" bpmnElement="Flow_1nc8xvi">
        <di:waypoint x="670" y="510" />
        <di:waypoint x="742" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n2nwrh_di" bpmnElement="Flow_1n2nwrh">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="730" />
        <di:waypoint x="400" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sq8f28_di" bpmnElement="Flow_1sq8f28">
        <di:waypoint x="500" y="730" />
        <di:waypoint x="570" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e2cv8r_di" bpmnElement="Flow_1e2cv8r">
        <di:waypoint x="670" y="730" />
        <di:waypoint x="742" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ylz1cf_di" bpmnElement="Flow_1ylz1cf">
        <di:waypoint x="228" y="150" />
        <di:waypoint x="285" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i6g2dt_di" bpmnElement="Flow_1i6g2dt">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="950" />
        <di:waypoint x="400" y="950" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_03bjsvl" bpmnElement="Flow_07nxxw6">
        <di:waypoint x="670" y="950" />
        <di:waypoint x="730" y="950" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y0l699_di" bpmnElement="Flow_0y0l699">
        <di:waypoint x="830" y="950" />
        <di:waypoint x="882" y="950" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0li0qhx" bpmnElement="Flow_0lbxkvo">
        <di:waypoint x="500" y="950" />
        <di:waypoint x="570" y="950" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_189zys4_di" bpmnElement="DataOutputAssociation_189zys4">
        <di:waypoint x="620" y="330" />
        <di:waypoint x="620" y="355" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_17p3vop_di" bpmnElement="DataOutputAssociation_17p3vop">
        <di:waypoint x="620" y="550" />
        <di:waypoint x="620" y="575" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_1mtue80_di" bpmnElement="DataOutputAssociation_1mtue80">
        <di:waypoint x="620" y="770" />
        <di:waypoint x="620" y="795" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
