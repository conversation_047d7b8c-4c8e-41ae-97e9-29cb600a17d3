<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1prd8mo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1heijwu">
    <bpmn:participant id="Participant_1mj9yvm" name="Banned" processRef="Process_1ym35ia" />
    <bpmn:participant id="Participant_0qwyn0h" name="Unbanned" processRef="Process_0ttoc4x" />
  </bpmn:collaboration>
  <bpmn:process id="Process_1ym35ia" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1qyqb0a">
      <bpmn:lane id="Lane_1hyemlq">
        <bpmn:flowNodeRef>Event_1qqr54o</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Gateway_00zkx50</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1o06oal</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0bw5qvj</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="Event_1qqr54o" name="BannedEvent">
      <bpmn:outgoing>Flow_1ylz1cf</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_186jx1p" />
    </bpmn:startEvent>
    <bpmn:parallelGateway id="Gateway_00zkx50">
      <bpmn:incoming>Flow_1ylz1cf</bpmn:incoming>
      <bpmn:outgoing>Flow_1ufn6u2</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:task id="Activity_1o06oal" name="Views aktualisieren">
      <bpmn:incoming>Flow_1ufn6u2</bpmn:incoming>
      <bpmn:outgoing>Flow_0u4byx2</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_0bw5qvj">
      <bpmn:incoming>Flow_0u4byx2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ylz1cf" sourceRef="Event_1qqr54o" targetRef="Gateway_00zkx50" />
    <bpmn:sequenceFlow id="Flow_1ufn6u2" sourceRef="Gateway_00zkx50" targetRef="Activity_1o06oal" />
    <bpmn:sequenceFlow id="Flow_0u4byx2" sourceRef="Activity_1o06oal" targetRef="Event_0bw5qvj" />
  </bpmn:process>
  <bpmn:process id="Process_0ttoc4x" isExecutable="true">
    <bpmn:laneSet id="LaneSet_135o5cl">
      <bpmn:lane id="Lane_1ppuhw8">
        <bpmn:flowNodeRef>Event_09feu4h</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Gateway_1wfqq6d</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1lcorgu</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1gpfmdb</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="Event_09feu4h" name="UnbannedEvent">
      <bpmn:outgoing>Flow_1yzj8ld</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0wwzwrz" />
    </bpmn:startEvent>
    <bpmn:parallelGateway id="Gateway_1wfqq6d">
      <bpmn:incoming>Flow_1yzj8ld</bpmn:incoming>
      <bpmn:outgoing>Flow_0w74zx8</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:task id="Activity_1lcorgu" name="Views aktualisieren">
      <bpmn:incoming>Flow_0w74zx8</bpmn:incoming>
      <bpmn:outgoing>Flow_1iyzvqy</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_1gpfmdb">
      <bpmn:incoming>Flow_1iyzvqy</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1yzj8ld" sourceRef="Event_09feu4h" targetRef="Gateway_1wfqq6d" />
    <bpmn:sequenceFlow id="Flow_0w74zx8" sourceRef="Gateway_1wfqq6d" targetRef="Activity_1lcorgu" />
    <bpmn:sequenceFlow id="Flow_1iyzvqy" sourceRef="Activity_1lcorgu" targetRef="Event_1gpfmdb" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1heijwu">
      <bpmndi:BPMNShape id="Participant_1mj9yvm_di" bpmnElement="Participant_1mj9yvm" isHorizontal="true">
        <dc:Bounds x="159" y="80" width="571" height="150" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hyemlq_di" bpmnElement="Lane_1hyemlq" isHorizontal="true">
        <dc:Bounds x="189" y="80" width="541" height="150" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qqr54o_di" bpmnElement="Event_1qqr54o">
        <dc:Bounds x="262" y="132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="248" y="175" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_00zkx50_di" bpmnElement="Gateway_00zkx50">
        <dc:Bounds x="355" y="125" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o06oal_di" bpmnElement="Activity_1o06oal">
        <dc:Bounds x="470" y="110" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rvi60e" bpmnElement="Event_0bw5qvj">
        <dc:Bounds x="652" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ylz1cf_di" bpmnElement="Flow_1ylz1cf">
        <di:waypoint x="298" y="150" />
        <di:waypoint x="355" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ufn6u2_di" bpmnElement="Flow_1ufn6u2">
        <di:waypoint x="405" y="150" />
        <di:waypoint x="470" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u4byx2_di" bpmnElement="Flow_0u4byx2">
        <di:waypoint x="570" y="150" />
        <di:waypoint x="652" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_010ffup" bpmnElement="Participant_0qwyn0h" isHorizontal="true">
        <dc:Bounds x="159" y="240" width="571" height="150" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0kdh7kb" bpmnElement="Lane_1ppuhw8" isHorizontal="true">
        <dc:Bounds x="189" y="240" width="541" height="150" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ew5a8g" bpmnElement="Event_09feu4h">
        <dc:Bounds x="262" y="292" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="242" y="335" width="80" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_17x1ncl" bpmnElement="Gateway_1wfqq6d">
        <dc:Bounds x="355" y="285" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1cgmkn0" bpmnElement="Activity_1lcorgu">
        <dc:Bounds x="470" y="270" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0jq0fqo" bpmnElement="Event_1gpfmdb">
        <dc:Bounds x="652" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_1cf3wrc" bpmnElement="Flow_1yzj8ld">
        <di:waypoint x="298" y="310" />
        <di:waypoint x="355" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_06pusae" bpmnElement="Flow_0w74zx8">
        <di:waypoint x="405" y="310" />
        <di:waypoint x="470" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_111hmov" bpmnElement="Flow_1iyzvqy">
        <di:waypoint x="570" y="310" />
        <di:waypoint x="652" y="310" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
