<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1prd8mo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1heijwu">
    <bpmn:participant id="Participant_1mj9yvm" name="Regular license Digitized" processRef="Process_1ym35ia" />
    <bpmn:participant id="Participant_0hr27te" name="Regular License Created" processRef="Process_07aptvy" />
    <bpmn:participant id="Participant_1av1pci" name="Replacement Card Ordered" processRef="Process_1pj07m2" />
  </bpmn:collaboration>
  <bpmn:process id="Process_1ym35ia" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1qyqb0a">
      <bpmn:lane id="Lane_1hyemlq">
        <bpmn:flowNodeRef>Event_1qqr54o</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Gateway_00zkx50</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1o06oal</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1gfe613</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0bw5qvj</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_132qmb5</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:sequenceFlow id="Flow_1ylz1cf" sourceRef="Event_1qqr54o" targetRef="Gateway_00zkx50" />
    <bpmn:startEvent id="Event_1qqr54o" name="RegularLicenseDigitizedEvent">
      <bpmn:outgoing>Flow_1ylz1cf</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_186jx1p" />
    </bpmn:startEvent>
    <bpmn:parallelGateway id="Gateway_00zkx50">
      <bpmn:incoming>Flow_1ylz1cf</bpmn:incoming>
      <bpmn:outgoing>Flow_1ufn6u2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1pln2dt</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_1ufn6u2" sourceRef="Gateway_00zkx50" targetRef="Activity_1o06oal" />
    <bpmn:task id="Activity_1o06oal" name="Views aktualisieren">
      <bpmn:incoming>Flow_1ufn6u2</bpmn:incoming>
      <bpmn:outgoing>Flow_0u4byx2</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_1gfe613" name="Scheckkarte bestellen">
      <bpmn:incoming>Flow_1pln2dt</bpmn:incoming>
      <bpmn:outgoing>Flow_1w49agq</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_0u4byx2" sourceRef="Activity_1o06oal" targetRef="Event_0bw5qvj" />
    <bpmn:endEvent id="Event_0bw5qvj">
      <bpmn:incoming>Flow_0u4byx2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1pln2dt" sourceRef="Gateway_00zkx50" targetRef="Activity_1gfe613" />
    <bpmn:endEvent id="Event_132qmb5">
      <bpmn:incoming>Flow_1w49agq</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1w49agq" sourceRef="Activity_1gfe613" targetRef="Event_132qmb5" />
  </bpmn:process>
  <bpmn:process id="Process_07aptvy" isExecutable="false">
    <bpmn:startEvent id="Event_0und70e" name="RegularLicenseDigitizedEvent">
      <bpmn:outgoing>Flow_09u4fob</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0vug6ti" />
    </bpmn:startEvent>
    <bpmn:task id="Activity_1741zr1" name="Views aktualisieren">
      <bpmn:incoming>Flow_0l8j63s</bpmn:incoming>
      <bpmn:outgoing>Flow_1rqm3gu</bpmn:outgoing>
    </bpmn:task>
    <bpmn:parallelGateway id="Gateway_1m6cn25">
      <bpmn:incoming>Flow_09u4fob</bpmn:incoming>
      <bpmn:outgoing>Flow_0l8j63s</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mz9q1g</bpmn:outgoing>
      <bpmn:outgoing>Flow_16eoy6v</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:endEvent id="Event_00t0fxb">
      <bpmn:incoming>Flow_1rqm3gu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09u4fob" sourceRef="Event_0und70e" targetRef="Gateway_1m6cn25" />
    <bpmn:sequenceFlow id="Flow_0l8j63s" sourceRef="Gateway_1m6cn25" targetRef="Activity_1741zr1" />
    <bpmn:sequenceFlow id="Flow_1rqm3gu" sourceRef="Activity_1741zr1" targetRef="Event_00t0fxb" />
    <bpmn:sequenceFlow id="Flow_1ur8j8s" sourceRef="Activity_0hk7d5f" targetRef="Event_0a7r9r8" />
    <bpmn:task id="Activity_0hk7d5f" name="Scheckkarte bestellen">
      <bpmn:incoming>Flow_1mz9q1g</bpmn:incoming>
      <bpmn:outgoing>Flow_1ur8j8s</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_0a7r9r8">
      <bpmn:incoming>Flow_1ur8j8s</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:task id="Activity_13riysh" name="Dokumente an das Servicekonto senden">
      <bpmn:incoming>Flow_16eoy6v</bpmn:incoming>
      <bpmn:outgoing>Flow_1k6n342</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_1mz9q1g" sourceRef="Gateway_1m6cn25" targetRef="Activity_0hk7d5f" />
    <bpmn:sequenceFlow id="Flow_16eoy6v" sourceRef="Gateway_1m6cn25" targetRef="Activity_13riysh" />
    <bpmn:endEvent id="Event_0xnkz65">
      <bpmn:incoming>Flow_1k6n342</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1k6n342" sourceRef="Activity_13riysh" targetRef="Event_0xnkz65" />
  </bpmn:process>
  <bpmn:process id="Process_1pj07m2" isExecutable="false">
    <bpmn:startEvent id="Event_0p2op84" name="RegularLicenseDigitizedEvent">
      <bpmn:outgoing>Flow_0y9fp2m</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1k2r32b" />
    </bpmn:startEvent>
    <bpmn:task id="Activity_0bv1pk5" name="Views aktualisieren">
      <bpmn:incoming>Flow_0b8t9uj</bpmn:incoming>
      <bpmn:outgoing>Flow_16qcjr9</bpmn:outgoing>
    </bpmn:task>
    <bpmn:parallelGateway id="Gateway_06d2hi4">
      <bpmn:incoming>Flow_0y9fp2m</bpmn:incoming>
      <bpmn:outgoing>Flow_0b8t9uj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0baubl0</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hmfgpp</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:task id="Activity_075rvsh" name="Scheckkarte bestellen">
      <bpmn:incoming>Flow_0baubl0</bpmn:incoming>
      <bpmn:outgoing>Flow_1lw0dbj</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_13xvf1y" name="Dokumente an das Servicekonto senden">
      <bpmn:incoming>Flow_0hmfgpp</bpmn:incoming>
      <bpmn:outgoing>Flow_0z03e2l</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_0bqj5uq">
      <bpmn:incoming>Flow_0z03e2l</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1c0l3kf">
      <bpmn:incoming>Flow_1lw0dbj</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1agh9ke">
      <bpmn:incoming>Flow_16qcjr9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0y9fp2m" sourceRef="Event_0p2op84" targetRef="Gateway_06d2hi4" />
    <bpmn:sequenceFlow id="Flow_0b8t9uj" sourceRef="Gateway_06d2hi4" targetRef="Activity_0bv1pk5" />
    <bpmn:sequenceFlow id="Flow_16qcjr9" sourceRef="Activity_0bv1pk5" targetRef="Event_1agh9ke" />
    <bpmn:sequenceFlow id="Flow_0baubl0" sourceRef="Gateway_06d2hi4" targetRef="Activity_075rvsh" />
    <bpmn:sequenceFlow id="Flow_0hmfgpp" sourceRef="Gateway_06d2hi4" targetRef="Activity_13xvf1y" />
    <bpmn:sequenceFlow id="Flow_1lw0dbj" sourceRef="Activity_075rvsh" targetRef="Event_1c0l3kf" />
    <bpmn:sequenceFlow id="Flow_0z03e2l" sourceRef="Activity_13xvf1y" targetRef="Event_0bqj5uq" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1heijwu">
      <bpmndi:BPMNShape id="Participant_1mj9yvm_di" bpmnElement="Participant_1mj9yvm" isHorizontal="true">
        <dc:Bounds x="132" y="80" width="528" height="240" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hyemlq_di" bpmnElement="Lane_1hyemlq" isHorizontal="true">
        <dc:Bounds x="162" y="80" width="498" height="240" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qqr54o_di" bpmnElement="Event_1qqr54o">
        <dc:Bounds x="192" y="132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="166" y="175" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_00zkx50_di" bpmnElement="Gateway_00zkx50">
        <dc:Bounds x="285" y="125" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o06oal_di" bpmnElement="Activity_1o06oal">
        <dc:Bounds x="400" y="110" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_179u8p8" bpmnElement="Activity_1gfe613">
        <dc:Bounds x="400" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rvi60e" bpmnElement="Event_0bw5qvj">
        <dc:Bounds x="582" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_002juae" bpmnElement="Event_132qmb5">
        <dc:Bounds x="582" y="242" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ylz1cf_di" bpmnElement="Flow_1ylz1cf">
        <di:waypoint x="228" y="150" />
        <di:waypoint x="285" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ufn6u2_di" bpmnElement="Flow_1ufn6u2">
        <di:waypoint x="335" y="150" />
        <di:waypoint x="400" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u4byx2_di" bpmnElement="Flow_0u4byx2">
        <di:waypoint x="500" y="150" />
        <di:waypoint x="582" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pln2dt_di" bpmnElement="Flow_1pln2dt">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="260" />
        <di:waypoint x="400" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w49agq_di" bpmnElement="Flow_1w49agq">
        <di:waypoint x="500" y="260" />
        <di:waypoint x="582" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Participant_0hr27te_di" bpmnElement="Participant_0hr27te" isHorizontal="true">
        <dc:Bounds x="132" y="340" width="526" height="310" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_02w8azp" bpmnElement="Event_0und70e">
        <dc:Bounds x="202" y="372" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="176" y="415" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1vi75zp" bpmnElement="Activity_1741zr1">
        <dc:Bounds x="410" y="350" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1sib1lh" bpmnElement="Gateway_1m6cn25">
        <dc:Bounds x="295" y="365" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1r6agne" bpmnElement="Event_00t0fxb">
        <dc:Bounds x="592" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1cxl1fp" bpmnElement="Activity_0hk7d5f">
        <dc:Bounds x="410" y="450" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_07hu6p7" bpmnElement="Event_0a7r9r8">
        <dc:Bounds x="592" y="472" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rt4vv9" bpmnElement="Activity_13riysh">
        <dc:Bounds x="410" y="550" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0de43ex" bpmnElement="Event_0xnkz65">
        <dc:Bounds x="592" y="572" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0dvkjo5" bpmnElement="Flow_09u4fob">
        <di:waypoint x="238" y="390" />
        <di:waypoint x="295" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0sgiqr7" bpmnElement="Flow_0l8j63s">
        <di:waypoint x="345" y="390" />
        <di:waypoint x="410" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_02oxj69" bpmnElement="Flow_1rqm3gu">
        <di:waypoint x="510" y="390" />
        <di:waypoint x="592" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1ch88nv" bpmnElement="Flow_1ur8j8s">
        <di:waypoint x="510" y="490" />
        <di:waypoint x="592" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mz9q1g_di" bpmnElement="Flow_1mz9q1g">
        <di:waypoint x="320" y="415" />
        <di:waypoint x="320" y="490" />
        <di:waypoint x="410" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16eoy6v_di" bpmnElement="Flow_16eoy6v">
        <di:waypoint x="320" y="415" />
        <di:waypoint x="320" y="590" />
        <di:waypoint x="410" y="590" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k6n342_di" bpmnElement="Flow_1k6n342">
        <di:waypoint x="510" y="590" />
        <di:waypoint x="592" y="590" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_1qjf6lb" bpmnElement="Participant_1av1pci" isHorizontal="true">
        <dc:Bounds x="132" y="680" width="526" height="320" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1m3rcxd" bpmnElement="Event_0p2op84">
        <dc:Bounds x="212" y="722" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="186" y="765" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1oo32tz" bpmnElement="Activity_0bv1pk5">
        <dc:Bounds x="420" y="700" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0n6byua" bpmnElement="Gateway_06d2hi4">
        <dc:Bounds x="305" y="715" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1izuaov" bpmnElement="Activity_075rvsh">
        <dc:Bounds x="420" y="800" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1kqhjt9" bpmnElement="Activity_13xvf1y">
        <dc:Bounds x="420" y="900" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_00ni9pd" bpmnElement="Event_0bqj5uq">
        <dc:Bounds x="602" y="922" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0h49i6b" bpmnElement="Event_1c0l3kf">
        <dc:Bounds x="602" y="822" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1u7gyhj" bpmnElement="Event_1agh9ke">
        <dc:Bounds x="602" y="722" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0c3n1jb" bpmnElement="Flow_0y9fp2m">
        <di:waypoint x="248" y="740" />
        <di:waypoint x="305" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1lrfugg" bpmnElement="Flow_0b8t9uj">
        <di:waypoint x="355" y="740" />
        <di:waypoint x="420" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0p5t5ok" bpmnElement="Flow_16qcjr9">
        <di:waypoint x="520" y="740" />
        <di:waypoint x="602" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1e9cd0t" bpmnElement="Flow_0baubl0">
        <di:waypoint x="330" y="765" />
        <di:waypoint x="330" y="840" />
        <di:waypoint x="420" y="840" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1hlam5a" bpmnElement="Flow_0hmfgpp">
        <di:waypoint x="330" y="765" />
        <di:waypoint x="330" y="940" />
        <di:waypoint x="420" y="940" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_159qv1t" bpmnElement="Flow_1lw0dbj">
        <di:waypoint x="520" y="840" />
        <di:waypoint x="602" y="840" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1dgl09k" bpmnElement="Flow_0z03e2l">
        <di:waypoint x="520" y="940" />
        <di:waypoint x="602" y="940" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
