<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1prd8mo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1heijwu">
    <bpmn:participant id="Participant_1mj9yvm" name="Person Created" processRef="Process_1ym35ia" />
    <bpmn:participant id="Participant_0nz1s20" name="Person Changed" processRef="Process_0t76bar" />
  </bpmn:collaboration>
  <bpmn:process id="Process_1ym35ia" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1qyqb0a">
      <bpmn:lane id="Lane_1hyemlq">
        <bpmn:flowNodeRef>Event_1qqr54o</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Gateway_00zkx50</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1o06oal</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1gfe613</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0bw5qvj</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_132qmb5</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:sequenceFlow id="Flow_1ylz1cf" sourceRef="Event_1qqr54o" targetRef="Gateway_00zkx50" />
    <bpmn:startEvent id="Event_1qqr54o" name="PersonCreatedEvent">
      <bpmn:outgoing>Flow_1ylz1cf</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_186jx1p" />
    </bpmn:startEvent>
    <bpmn:parallelGateway id="Gateway_00zkx50">
      <bpmn:incoming>Flow_1ylz1cf</bpmn:incoming>
      <bpmn:outgoing>Flow_1ufn6u2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1pln2dt</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_1ufn6u2" sourceRef="Gateway_00zkx50" targetRef="Activity_1o06oal" />
    <bpmn:task id="Activity_1o06oal" name="Views aktualisieren">
      <bpmn:incoming>Flow_1ufn6u2</bpmn:incoming>
      <bpmn:outgoing>Flow_0u4byx2</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_1gfe613" name="Dokumente an das Servicekonto Postfach senden">
      <bpmn:incoming>Flow_1pln2dt</bpmn:incoming>
      <bpmn:outgoing>Flow_1w49agq</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_0u4byx2" sourceRef="Activity_1o06oal" targetRef="Event_0bw5qvj" />
    <bpmn:endEvent id="Event_0bw5qvj">
      <bpmn:incoming>Flow_0u4byx2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1pln2dt" sourceRef="Gateway_00zkx50" targetRef="Activity_1gfe613" />
    <bpmn:endEvent id="Event_132qmb5">
      <bpmn:incoming>Flow_1w49agq</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1w49agq" sourceRef="Activity_1gfe613" targetRef="Event_132qmb5" />
  </bpmn:process>
  <bpmn:process id="Process_0t76bar" isExecutable="true">
    <bpmn:laneSet id="LaneSet_0lpaqyw">
      <bpmn:lane id="Lane_06rtep1">
        <bpmn:flowNodeRef>Event_0ypqkly</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Gateway_0p2g0np</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_18r73cp</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1rlydjd</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="Event_0ypqkly" name="PersonChangedEvent">
      <bpmn:outgoing>Flow_1tyu7k8</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_11zwxuf" />
    </bpmn:startEvent>
    <bpmn:parallelGateway id="Gateway_0p2g0np">
      <bpmn:incoming>Flow_1tyu7k8</bpmn:incoming>
      <bpmn:outgoing>Flow_0gvpsi0</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:task id="Activity_18r73cp" name="Views aktualisieren">
      <bpmn:incoming>Flow_0gvpsi0</bpmn:incoming>
      <bpmn:outgoing>Flow_1glnn5m</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_1rlydjd">
      <bpmn:incoming>Flow_1glnn5m</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1tyu7k8" sourceRef="Event_0ypqkly" targetRef="Gateway_0p2g0np" />
    <bpmn:sequenceFlow id="Flow_0gvpsi0" sourceRef="Gateway_0p2g0np" targetRef="Activity_18r73cp" />
    <bpmn:sequenceFlow id="Flow_1glnn5m" sourceRef="Activity_18r73cp" targetRef="Event_1rlydjd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1heijwu">
      <bpmndi:BPMNShape id="Participant_1mj9yvm_di" bpmnElement="Participant_1mj9yvm" isHorizontal="true">
        <dc:Bounds x="132" y="80" width="528" height="240" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hyemlq_di" bpmnElement="Lane_1hyemlq" isHorizontal="true">
        <dc:Bounds x="162" y="80" width="498" height="240" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qqr54o_di" bpmnElement="Event_1qqr54o">
        <dc:Bounds x="192" y="132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="167" y="175" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_00zkx50_di" bpmnElement="Gateway_00zkx50">
        <dc:Bounds x="285" y="125" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o06oal_di" bpmnElement="Activity_1o06oal">
        <dc:Bounds x="400" y="110" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_179u8p8" bpmnElement="Activity_1gfe613">
        <dc:Bounds x="400" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rvi60e" bpmnElement="Event_0bw5qvj">
        <dc:Bounds x="582" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_002juae" bpmnElement="Event_132qmb5">
        <dc:Bounds x="582" y="242" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ylz1cf_di" bpmnElement="Flow_1ylz1cf">
        <di:waypoint x="228" y="150" />
        <di:waypoint x="285" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ufn6u2_di" bpmnElement="Flow_1ufn6u2">
        <di:waypoint x="335" y="150" />
        <di:waypoint x="400" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u4byx2_di" bpmnElement="Flow_0u4byx2">
        <di:waypoint x="500" y="150" />
        <di:waypoint x="582" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pln2dt_di" bpmnElement="Flow_1pln2dt">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="260" />
        <di:waypoint x="400" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w49agq_di" bpmnElement="Flow_1w49agq">
        <di:waypoint x="500" y="260" />
        <di:waypoint x="582" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_1hmar8v" bpmnElement="Participant_0nz1s20" isHorizontal="true">
        <dc:Bounds x="132" y="330" width="528" height="130" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0b7jjvy" bpmnElement="Lane_06rtep1" isHorizontal="true">
        <dc:Bounds x="162" y="330" width="498" height="130" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lnxz4m" bpmnElement="Event_0ypqkly">
        <dc:Bounds x="192" y="382" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="167" y="425" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1jteifv" bpmnElement="Gateway_0p2g0np">
        <dc:Bounds x="285" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rbb4h7" bpmnElement="Activity_18r73cp">
        <dc:Bounds x="400" y="360" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1k7fxpl" bpmnElement="Event_1rlydjd">
        <dc:Bounds x="582" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_1imxbre" bpmnElement="Flow_1tyu7k8">
        <di:waypoint x="228" y="400" />
        <di:waypoint x="285" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_13wiqcc" bpmnElement="Flow_0gvpsi0">
        <di:waypoint x="335" y="400" />
        <di:waypoint x="400" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0vsp19n" bpmnElement="Flow_1glnn5m">
        <di:waypoint x="500" y="400" />
        <di:waypoint x="582" y="400" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
