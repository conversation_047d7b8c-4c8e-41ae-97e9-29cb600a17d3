@startuml
class RegisterEntrySearchView {
  UUID registerId
  String title
  String firstname
  String lastname
  String birthname
  String normalizedName
  String birthplace
  String normalizedBirthplace
  String birthdate
  String nationality
  Set<String> identificationNumbers
  SearchItem data
}
class SearchItem {
  String registerId
  SearchItemPerson person
  List<SearchItemFishingLicense> fishingLicenses
  List<SearchItemQualificationsProof> qualificationProofs
}
class SearchItemPerson {
  String title
  String firstname
  String lastname
  String birthdate
  String birthname
  String birthplace
}
class SearchItemFishingLicense {
  String number
}
class SearchItemQualificationsProof {
  String fishingCertificateId
}

RegisterEntrySearchView *-- SearchItem

SearchItem *-- SearchItemPerson
SearchItem "1" *-- "many" SearchItemFishingLicense
SearchItem "1" *-- "many" SearchItemQualificationsProof
@enduml