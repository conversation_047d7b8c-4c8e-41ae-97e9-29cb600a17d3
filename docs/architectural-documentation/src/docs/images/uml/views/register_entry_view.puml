@startuml RegisterEntryView
class RegisterEntryView {
    UUID registerId
    CLOB<RegisterEntry> data
}

class RegisterEntry {
    UUID registerId
    String serviceAccountId
    String inboxReference
}
class Person {
    String title
    String firstname
    String lastname
    String bithname
    String bithplace
    Birthdate bithdate
    Address address
    Address officeAddress
    String nationality
    String email
}
class Address {
    String office
    String deliverTo
    String street
    String streetNumber
    String postcode
    String city
    String detail
}
class Jurisdiction {
    String federalState
}

class DeletionFlag {
    DeletionReason reason
}

enum DeletionReason {
    GDPR_REQUEST
    GDPR_REQUEST_EXCEPT_CERTIFICATE
    PERSON_DECEASED
}

class Ban {
    UUID banID
    String fileNumber
    String reportedBy
    LocalDate at
    LocalDate from
    LocalDate to
}
class FishingLicense {
    String number
    String legacyNumber
    FederalState issuingFederalState
    LicenseType type
    List<ValidityPeriod> validityPeriods
    LimitedLicenseApproval limitedLicenseApproval
}
enum LicenseType {
    REGULAR
    VACATION
    LIMITED
    NONE
}


class LimitedLicenseApplication {
    UUID id
    LimitedLicenseApplicationStatus status
    LocalDate createdAt
    FederalState federalState
    String disabilityCertificateFileURL
}
enum LimitedLicenseApplicationStatus {
    PENDING
    REJECTED
}

class LimitedLicenseApproval {
    UUID limitedLicenseApprovalId
    SigningEmployee signingEmploye
    LocalDate createdAt
    String fileNumber
    String cashRegisterSign
    String justificationForLimitedDurationNotice
}

class SigningEmployee {
    String name
    String email
    String phone
    String personalSign
}

class ValidityPeriod {
    LocalDate validFrom
    LocalDate validTo
}
class QualificationsProof {
    QualificationsProofType type
    String fishingCertificateId
    String otherFormOfProofId
    String federalState
    String examinerId
    LocalDate passedOn
    String issuedBy
}
enum QualificationsProofType {
    CERTIFICATE
    LEGACY_LICENSE
    OTHER
}

class Fee {
    String federalState
    LocalDate validFrom
    LocalDate validTo
    PaymentInfo paymentInfo
}
class PaymentInfo {
    Double amount
    PaymentType type
    String transactionId
}
enum PaymentType {
    CASH
    CARD
    ONLINE
    BANK_TRANSFER
}
class Tax {
    String taxId
    String federalState
    LocalDate validFrom
    LocalDate validTo
    PaymentInfo paymentInfo
}
class IdentificationDocument {
    LocalDate issuedDate
    String documentId
    IdentificationDocumentType type
    FishingLicense fishingLicense
    Tax tax
}
enum IdentificationDocumentType {
    CARD
    PDF
}

Person *-- Address: "address, officeAddress"

FishingLicense "1" *-- "many" ValidityPeriod
FishingLicense -- LicenseType
FishingLicense o-- LimitedLicenseApproval

LimitedLicenseApproval *-- SigningEmployee

LimitedLicenseApplication -- LimitedLicenseApplicationStatus

QualificationsProof -- QualificationsProofType

PaymentInfo -- PaymentType

DeletionFlag -- DeletionReason

Fee *-- PaymentInfo
Tax *-- PaymentInfo

IdentificationDocument -- IdentificationDocumentType
IdentificationDocument o-- FishingLicense
IdentificationDocument o-- Tax
IdentificationDocument o-- LimitedLicenseApproval

RegisterEntry *--* Person
RegisterEntry *--o Jurisdiction
RegisterEntry *--o DeletionFlag
RegisterEntry *--o Ban
RegisterEntry *--o LimitedLicenseApplication
RegisterEntry "1" *-- "many" FishingLicense
RegisterEntry "1" *-- "many" QualificationsProof
RegisterEntry "1" *-- "many" Fee
RegisterEntry "1" *-- "many" Tax
RegisterEntry "1" *-- "many" IdentificationDocument


RegisterEntryView *-- RegisterEntry

'Person *-- IdentificationDocument
@enduml