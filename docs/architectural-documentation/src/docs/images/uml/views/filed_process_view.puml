@startuml

class FiledProcessView {
    long id
    UUID registerEntryId
    String actingInstitution
    FederalState federalStateOfInstitution
    Instant processTimeStamp
    FederalState federalState
    String issuedBy
    FiledProcessType filedProcessType
    FiledProcessData filedProcessData
}

enum FiledProcessType {
    QUALIFICATIONS_PROOF_CREATED
    JURISDICTION_CHANGED
    FISHING_LICENSE_CREATED
    FISHING_LICENSE_EXTENDED
    REPLACEMENT_CARD_ORDERED
    FISHING_TAX_CREATED
    BANNED
    UNBANNED
    LIMITED_LICENSE_APPLICATION_CREATED
    LIMITED_LICENSE_APPLICATION_REJECTED
}

class FiledProcessData {
    Person person
    String serviceAccountId
    QualificationsProof qualificationsProof
    List<Tax> taxes
    List<Fee> fees
    Ban ban
    AbstractConsentInfo consentInfo
    List<IdentificationDocument> identificationDocuments
    FishingLicense fishingLicense
    String fishingLicenseNumber
}

FiledProcessView -- FiledProcessType
FiledProcessView *--* FiledProcessData

@enduml