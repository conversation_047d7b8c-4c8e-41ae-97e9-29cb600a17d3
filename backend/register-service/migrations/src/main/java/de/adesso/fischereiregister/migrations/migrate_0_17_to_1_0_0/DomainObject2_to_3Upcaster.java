package de.adesso.fischereiregister.migrations.migrate_0_17_to_1_0_0;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;

import java.time.LocalDate;
import java.util.stream.Stream;

public class DomainObject2_to_3Upcaster {

    // Ban properties
    private static final String AT = "at";
    private static final String FROM = "from";

    private static final String SERVICE_ACCOUNT_LINK = "serviceAccountLink";
    private static final String SERVICE_ACCOUNT_ID = "serviceAccountId";

    private static final String FEDERAL_STATE = "federalState";

    public static ObjectNode upcastBan(ObjectNode ban) {
        if (ban.has(AT)) {
            LocalDate from = LocalDate.parse(ban.get(FROM).asText());
            LocalDate now = LocalDate.now();

            // Assume that the issued date is from or now, whatever came first (a future ban is issued now)
            // Since this is only regarding test data and no actual data has to be upcasted, this is fine
            LocalDate at = Stream.of(from, now).min(LocalDate::compareTo).orElse(now);
            ban.set(AT, TextNode.valueOf(at.toString()));
        }

        return ban;
    }

    public static ObjectNode upcastPerson(ObjectNode person) {
        if (person.has(SERVICE_ACCOUNT_LINK)) {
            person.remove(SERVICE_ACCOUNT_LINK);
        }
        if (person.has(SERVICE_ACCOUNT_ID)) {
            person.remove(SERVICE_ACCOUNT_ID);
        }

        return person;
    }

    public static ObjectNode upcastQualificationsProof(ObjectNode qualificationsProof) {
        // Federal State must always be set
        if (!qualificationsProof.has(FEDERAL_STATE) || !qualificationsProof.hasNonNull(FEDERAL_STATE)) {
            // Always assume SH since no data was set and only Test-data relevant
            qualificationsProof.set(FEDERAL_STATE, new TextNode("SH"));
        }

        return qualificationsProof;
    }
}
