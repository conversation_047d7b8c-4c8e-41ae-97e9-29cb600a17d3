package de.adesso.fischereiregister.migrations;

import de.adesso.fischereiregister.migrations.ports.EventsReplayPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicBoolean;

@Configuration
@Slf4j
public class MigrationsConfiguration implements InitializingBean {
    @Value("${migrations.replay_events_on_startup}")
    private boolean replayEventsOnStartup;

    @Value("${migrations.initial_delay_in_minutes}")
    private int initialDelayInMinutes;

    @Value("${migrations.retry.attempts}")
    private int maxRetryAttempts;

    @Value("${migrations.retry.delay_in_seconds}")
    private int retryDelayInSeconds;

    private final EventsReplayPort eventsReplayPort;
    private final ThreadPoolTaskScheduler taskScheduler;

    private final AtomicBoolean taskCompleted = new AtomicBoolean(false);

    private ScheduledFuture<?> scheduledTask;

    public MigrationsConfiguration(EventsReplayPort eventsReplayPort) {
        this.eventsReplayPort = eventsReplayPort;

        taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(1);
        taskScheduler.setThreadNamePrefix("delayed-task-");
        taskScheduler.initialize();
    }

    @Override
    public void afterPropertiesSet() {
        if (replayEventsOnStartup) {
            log.info("Application is ready. Scheduling Migration to run in {} minutes", initialDelayInMinutes);

            Instant executionTime = Instant.now().plus(Duration.ofMinutes(initialDelayInMinutes));
            scheduledTask = taskScheduler.schedule(() -> executeTaskWithRetry(1), executionTime);
        }
    }

    /**
     * Ensures Exception save execution of event replay mechanism
     *
     * @return True, when execution was successful
     */
    private boolean replayEventsSafely() {
        try {
            eventsReplayPort.replayAllEvents();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private void executeTaskWithRetry(int attempt) {
        if (taskCompleted.get()) {
            log.info("Migration has already been completed successfully. Skipping execution.");
            return;
        }

        log.info("Attempting to execute Migration (attempt {}/{})", attempt, maxRetryAttempts);

        boolean success = replayEventsSafely();

        if (success) {
            log.info("Migration successful on attempt {}", attempt);
            taskCompleted.set(true);
            if (scheduledTask != null) {
                scheduledTask.cancel(false);
            }
        } else if (attempt < maxRetryAttempts) {
            log.info("Migration failed on attempt {}. Scheduling retry in {} seconds", attempt, retryDelayInSeconds);
            scheduledTask = taskScheduler.schedule(
                    () -> executeTaskWithRetry(attempt + 1),
                    Instant.now().plus(Duration.ofSeconds(retryDelayInSeconds))
            );
        } else {
            log.error("Migration failed after maximum number of attempts ({})", maxRetryAttempts);
            throw new RuntimeException("Migration failed after maximum number of attempts: " + maxRetryAttempts);
        }
    }
}
