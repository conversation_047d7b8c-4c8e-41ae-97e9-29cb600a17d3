package de.adesso.fischereiregister.migrations.migrate_0_17_to_1_0_0;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.model.type.FederalState;
import lombok.AllArgsConstructor;
import org.axonframework.serialization.SimpleSerializedType;
import org.axonframework.serialization.upcasting.event.IntermediateEventRepresentation;
import org.axonframework.serialization.upcasting.event.SingleEventUpcaster;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Order(2)
@AllArgsConstructor
public class UnbannedEvent_0_to_3_Upcaster extends SingleEventUpcaster {
    private static final String PAYLOAD_TYPE = UnbannedEvent.class.getTypeName();
    private static final String EXPECTED_REVISION = null;
    private static final String UPCASTED_REVISION = "3.0";

    private static final String FEDERAL_STATE = "federalState";
    private static final String JURISDICTION = "jurisdiction";

    @Override
    protected boolean canUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        final String eventName = intermediateRepresentation.getType().getName();

        return eventName.equals(PAYLOAD_TYPE) &&
                Objects.equals(intermediateRepresentation.getType().getRevision(), EXPECTED_REVISION);
    }

    @Override
    protected IntermediateEventRepresentation doUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.upcastPayload(
                new SimpleSerializedType(PAYLOAD_TYPE, UPCASTED_REVISION),
                ObjectNode.class,
                event -> {
                    ObjectNode jurisdiction = JsonNodeFactory.instance.objectNode();
                    jurisdiction.put(FEDERAL_STATE, FederalState.SH.toString());

                    event.set(JURISDICTION, jurisdiction);

                    return event;
                }
        );
    }
}
