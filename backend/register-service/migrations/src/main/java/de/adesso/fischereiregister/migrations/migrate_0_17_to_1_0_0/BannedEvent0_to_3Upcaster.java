package de.adesso.fischereiregister.migrations.migrate_0_17_to_1_0_0;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.model.type.FederalState;
import org.axonframework.serialization.SimpleSerializedType;
import org.axonframework.serialization.upcasting.event.IntermediateEventRepresentation;
import org.axonframework.serialization.upcasting.event.SingleEventUpcaster;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
@Order(2)
public class BannedEvent0_to_3Upcaster extends SingleEventUpcaster {
    private static final String PAYLOAD_TYPE = BannedEvent.class.getTypeName();
    private static final String EXPECTED_REVISION = null;
    private static final String UPCASTED_REVISION = "3.0";

    private static final String FEDERAL_STATE = "federalState";
    private static final String JURISDICTION = "jurisdiction";

    private final Map<String, ObjectNode> context = new HashMap<>();

    @Override
    protected boolean canUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        final String eventName = intermediateRepresentation.getType().getName();

        return eventName.equals(PAYLOAD_TYPE) &&
                Objects.equals(intermediateRepresentation.getType().getRevision(), EXPECTED_REVISION);
    }

    @Override
    protected IntermediateEventRepresentation doUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.upcastPayload(
                new SimpleSerializedType(PAYLOAD_TYPE, UPCASTED_REVISION),
                ObjectNode.class,
                event -> {
                    ObjectNode jurisdiction = JsonNodeFactory.instance.objectNode();
                    jurisdiction.put(FEDERAL_STATE, FederalState.SH.toString());

                    event.set(JURISDICTION, jurisdiction);

                    final ObjectNode newBan = DomainObject2_to_3Upcaster.upcastBan((ObjectNode) event.get("ban"));
                    event.set("ban", newBan);

                    return event;
                }
        );
    }

}
