package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.DeletionFlag;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.UUID;

public record RegisterEntryMarkedForDeletionEvent(
        @TargetAggregateIdentifier UUID registerEntryId,
        DeletionFlag deletionFlag,
        String office,
        String userFederalState) implements AxonEvent {
}
