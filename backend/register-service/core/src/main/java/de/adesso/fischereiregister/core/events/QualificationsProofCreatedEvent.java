package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import org.axonframework.modelling.command.TargetAggregateIdentifier;
import org.axonframework.serialization.Revision;

import java.util.UUID;

@Revision("3.0")
public record QualificationsProofCreatedEvent(
        @TargetAggregateIdentifier UUID registerEntryId,
        QualificationsProof qualificationsProof,
        Person person) implements AxonEvent {

}