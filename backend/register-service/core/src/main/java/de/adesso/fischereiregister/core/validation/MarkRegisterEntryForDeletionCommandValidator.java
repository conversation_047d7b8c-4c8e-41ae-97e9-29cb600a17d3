package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.MarkRegisterEntryForDeletionCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class MarkRegisterEntryForDeletionCommandValidator extends AbstractValidator implements CommandValidator<MarkRegisterEntryForDeletionCommand> {

    public MarkRegisterEntryForDeletionCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(MarkRegisterEntryForDeletionCommand command, RegisterEntry registerEntry) throws ClientInputValidationException {
        if(registerEntry.getDeletionFlag() != null) {
            validationResult.addErrorNote("Register entry is already marked for deletion.");
        }

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }
}
