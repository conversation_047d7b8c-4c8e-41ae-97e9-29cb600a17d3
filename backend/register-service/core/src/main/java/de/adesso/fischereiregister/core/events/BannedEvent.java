package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import org.axonframework.modelling.command.TargetAggregateIdentifier;
import org.axonframework.serialization.Revision;

import java.util.UUID;

@Revision("3.0")
public record BannedEvent(
        @TargetAggregateIdentifier UUID registerId,
        Ban ban,
        Jurisdiction jurisdiction,
        String issuedByOffice
) implements AxonEvent {
}
