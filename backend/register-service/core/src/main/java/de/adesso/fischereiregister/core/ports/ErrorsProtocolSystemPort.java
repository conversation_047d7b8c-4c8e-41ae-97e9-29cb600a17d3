package de.adesso.fischereiregister.core.ports;

/**
 * Port for reporting system errors in the errors protocol.
 * This port is used to report system errors that occur in the application.
 */
public interface ErrorsProtocolSystemPort {

    /**
     * Reports a system error for a specific federal state and year.
     *
     * @param federalState the federal state where the error occurred
     * @param year         the year in which the error occurred
     */
    void reportSystemError(String office, String federalState, int year);
}
