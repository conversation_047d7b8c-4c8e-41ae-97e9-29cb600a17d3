package de.adesso.fischereiregister.core.model.user;

import java.util.Collection;
import java.util.Set;

/**
 * Enumeration containing all relevant roles for the backend.
 */
public enum UserRole {
    INSPECTOR("INSPECTOR"),
    OFFICIAL("OFFICIAL"),
    EXAM_DATA_CREATOR("EXAM_DATA_CREATOR"),
    LIMITED_LICENSE_CREATOR("LIMITED_LICENSE_CREATOR"),
    LIMITED_LICENSE_APPROVAL_READER("LIMITED_LICENSE_APPROVAL_READER"),
    ADMIN("ADMIN"),
    ONLINE_SERVICE("ONLINE_SERVICE"),
    BAN_MANAGER("BAN_MANAGER"),
    CARD_PRINTER("CARD_PRINTER"),
    EXAM_SOFTWARE("EXAM_SOFTWARE"),
    REGISTER_FILE_READER("REGISTER_FILE_READER");

    private final String value;

    UserRole(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static final Collection<UserRole> ALL_ROLES = Set.of(UserRole.values());
}
