package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import org.axonframework.modelling.command.TargetAggregateIdentifier;
import org.axonframework.serialization.Revision;

import java.util.List;
import java.util.UUID;

@Revision("3.0")
public record FishingTaxPayedEvent(@TargetAggregateIdentifier UUID registerId,
                                   TaxConsentInfo consentInfo,
                                   Person person,
                                   List<Tax> taxes,
                                   String salt,
                                   List<IdentificationDocument> identificationDocuments,
                                   String issuedByOffice,
                                   String inboxReference,
                                   // if null no message will be send to the online service portal (OS)
                                   String serviceAccountId,
                                   // id of the service account in the online service portal (OS)
                                   String transactionId, // transaction id of the online service portal (OS)
                                   SubmissionType submissionType
) implements AxonEvent {

    public FishingTaxPayedEvent {
        assert taxes != null : "The list of taxes should not be null";
        assert identificationDocuments != null : "The list of identification documents should not be null";
    }
}
