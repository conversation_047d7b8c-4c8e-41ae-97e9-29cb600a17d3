package de.adesso.fischereiregister.core.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDate;
import java.util.UUID;

/**
 * Represents the approval for a limited license.
 */
@Getter
@Setter
@FieldNameConstants
public class LimitedLicenseApproval {

    private UUID limitedLicenseApprovalId;

    private SigningEmployee signingEmployee;

    private LocalDate createdAt;

    private String fileNumber;

    private String cashRegisterSign;

    private String justificationForLimitedDurationNotice;

}
