package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.BanPermanentlyCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Component
public class BanPermanentlyCommandProcessor implements CommandProcessor<BanPermanentlyCommand> {
    @Override
    public List<AxonEvent> process(BanPermanentlyCommand command, RegisterEntry registerEntry) {

        final Ban newBan = new Ban();
        newBan.setBanId(command.banId() == null ? UUID.randomUUID() : command.banId());
        newBan.setFileNumber(command.fileNumber());
        newBan.setReportedBy(command.reportedBy());
        newBan.setAt(LocalDate.now());
        newBan.setFrom(command.from());
        newBan.setTo(null);

        return List.of(new BannedEvent(command.registerId(), newBan, registerEntry.getJurisdiction(), command.userDetails().getOffice()));
    }
}
