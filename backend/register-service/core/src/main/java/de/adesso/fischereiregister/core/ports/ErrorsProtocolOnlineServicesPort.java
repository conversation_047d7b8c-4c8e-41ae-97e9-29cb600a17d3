package de.adesso.fischereiregister.core.ports;

/**
 * Port for reporting system errors in the errors protocol.
 * This port is used to report system errors that occur in the application.
 */
public interface ErrorsProtocolOnlineServicesPort {

    /**
     * Reports an online service error for a specific federal state and year.
     *
     * @param federalState the federal state where the error occurred
     * @param year         the year in which the error occurred
     */
    void reportOSError(String federalState, int year);
}
