package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.MarkRegisterEntryForDeletionCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.RegisterEntryMarkedForDeletionEvent;
import de.adesso.fischereiregister.core.model.DeletionFlag;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class MarkRegisterEntryForDeletionCommandProcessor implements CommandProcessor<MarkRegisterEntryForDeletionCommand> {

    @Override
    public List<AxonEvent> process(MarkRegisterEntryForDeletionCommand command, RegisterEntry registerEntry) {
        return List.of(createEvent(command));
    }

    private RegisterEntryMarkedForDeletionEvent createEvent(MarkRegisterEntryForDeletionCommand command) {
        DeletionFlag deletionFlag = new DeletionFlag(command.deletionReason());
        return new RegisterEntryMarkedForDeletionEvent(
                command.registerId(),
                deletionFlag,
                command.userDetails().getOffice(),
                command.userDetails().getFederalState());
    }
}
