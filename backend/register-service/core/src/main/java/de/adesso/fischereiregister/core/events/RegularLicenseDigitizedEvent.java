package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import org.axonframework.modelling.command.TargetAggregateIdentifier;
import org.axonframework.serialization.Revision;

import java.util.List;
import java.util.UUID;

@Revision("3.0")
public record RegularLicenseDigitizedEvent(
        @TargetAggregateIdentifier UUID registerId,
        String salt,
        Person person,
        Jurisdiction jurisdiction,
        FishingLicense fishingLicense,
        List<Fee> fees,
        List<Tax> taxes,
        List<QualificationsProof> qualificationsProofs,
        List<IdentificationDocument> identificationDocuments,
        ConsentInfo consentInfo,
        String issuedByOffice,
        String issuedByAddress) implements AxonEvent {

    public RegularLicenseDigitizedEvent {
        assert fees != null : "The list of fees should not be null";
        assert taxes != null : "The list of taxes should not be null";
        assert qualificationsProofs != null : "The list of qualificationsProofs should not be null";
        assert identificationDocuments != null : "The list of identificationDocuments should not be null";
    }
} 
