package de.adesso.fischereiregister.core.ports.contracts.statistics;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorsStatistics {

    private Integer year;
    private ErrorsStatisticsData data;

    public ErrorsStatistics(Integer year,
                            Integer onlineServiceErrors,
                            Integer cardErrors,
                            Integer systemErrors) {
        this.year = year;
        this.data = new ErrorsStatisticsData(
                onlineServiceErrors,
                cardErrors,
                systemErrors
        );
    }
}
