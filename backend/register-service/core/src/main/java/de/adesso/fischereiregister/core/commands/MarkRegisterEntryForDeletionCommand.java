package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.type.DeletionReason;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.UUID;

public record MarkRegisterEntryForDeletionCommand(
        @TargetAggregateIdentifier
        UUID registerId,
        DeletionReason deletionReason,
        UserDetails userDetails) {
}
