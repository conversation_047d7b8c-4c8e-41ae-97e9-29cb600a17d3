package de.adesso.fischereiregister.core.utils;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.type.LicenseType;

/**
 * This class contains business logic  methods for working with identification documents.
 * maybe using the name utils is not the best choice, but it's a good starting point
 */
public class IdentificationDocumentUtils {

    private IdentificationDocumentUtils() {
        throw new UnsupportedOperationException("Utility class should not be instantiated");
    }

    /**
     * returns true if the document contains restricted information.
     * a document is said to have restricted information if it contains info about the citizens health condition
     * for example info about disability like in the case of a limited license
     */
    public static boolean hasRestrictedInformation(IdentificationDocument identificationDocument) {
        return identificationDocument.getLimitedLicenseApproval() != null || (identificationDocument.getFishingLicense() != null && identificationDocument.getFishingLicense().getType() == LicenseType.LIMITED);
    }
}
