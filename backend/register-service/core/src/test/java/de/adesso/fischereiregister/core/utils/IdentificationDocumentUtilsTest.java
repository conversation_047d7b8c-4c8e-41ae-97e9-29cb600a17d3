package de.adesso.fischereiregister.core.utils;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class IdentificationDocumentUtilsTest {

    @Test
    @DisplayName("IdentificationDocumentUtils.hasRestrictedInformation Should return true when document has limited license approval")
    public void testHasRestrictedInformationReturnsTrueWhenDocumentHasLimitedLicenseApproval() {
        // given
        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setLimitedLicenseApproval(new LimitedLicenseApproval());

        // when
        boolean result = IdentificationDocumentUtils.hasRestrictedInformation(identificationDocument);

        // then
        assertTrue(result);
    }

    @Test
    @DisplayName("IdentificationDocumentUtils.hasRestrictedInformation Should return true when document has limited fishing license")
    public void testHasRestrictedInformationReturnsTrueWhenDocumentHasLimitedFishingLicense() {
        // given
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.LIMITED);

        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setFishingLicense(fishingLicense);

        // when
        boolean result = IdentificationDocumentUtils.hasRestrictedInformation(identificationDocument);

        // then
        assertTrue(result);
    }

    @Test
    @DisplayName("IdentificationDocumentUtils.hasRestrictedInformation Should return false when document has regular fishing license")
    public void testHasRestrictedInformationReturnsFalseWhenDocumentHasRegularFishingLicense() {
        // given
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.REGULAR);

        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setFishingLicense(fishingLicense);

        // when
        boolean result = IdentificationDocumentUtils.hasRestrictedInformation(identificationDocument);

        // then
        assertFalse(result);
    }

    @Test
    @DisplayName("IdentificationDocumentUtils.hasRestrictedInformation Should return false when document has no license info")
    public void testHasRestrictedInformationReturnsFalseWhenDocumentHasNoLicenseInfo() {
        // given
        IdentificationDocument identificationDocument = new IdentificationDocument();

        // when
        boolean result = IdentificationDocumentUtils.hasRestrictedInformation(identificationDocument);

        // then
        assertFalse(result);
    }

    @Test
    @DisplayName("IdentificationDocumentUtils.hasRestrictedInformation Should return false when document has tax info only")
    public void testHasRestrictedInformationReturnsFalseWhenDocumentHasTaxInfoOnly() {
        // given
        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setTax(new Tax());

        // when
        boolean result = IdentificationDocumentUtils.hasRestrictedInformation(identificationDocument);

        // then
        assertFalse(result);
    }
}