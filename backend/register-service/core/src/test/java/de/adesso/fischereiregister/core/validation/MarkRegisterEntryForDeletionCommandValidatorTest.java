package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.MarkRegisterEntryForDeletionCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.model.DeletionFlag;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.DeletionReason;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static org.junit.jupiter.api.Assertions.assertThrows;

class MarkRegisterEntryForDeletionCommandValidatorTest {

    private MarkRegisterEntryForDeletionCommandValidator validator;

    @Mock
    private CountryService countryService;

    @Mock
    private TenantRulesValidationPort tenantRulesValidationPort;

    @BeforeEach
    void setUp() {
        validator = new MarkRegisterEntryForDeletionCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    void validateOrThrow_ShouldThrowException_WhenRegisterEntryAlreadyMarkedForDeletion() {
        // Arrange
        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setDeletionFlag(new DeletionFlag(DeletionReason.GDPR_REQUEST)); // Simulate entry already marked for deletion
        MarkRegisterEntryForDeletionCommand command = new MarkRegisterEntryForDeletionCommand(registerEntry.getRegisterId(),
                DeletionReason.GDPR_REQUEST,
                null); // User details can be null for this test

        // Act & Assert
        assertThrows(ClientInputValidationException.class, () -> {
            validator.validateOrThrow(command, registerEntry);
        });
    }

    @Test
    void validateOrThrow_ShouldNotThrowException_WhenRegisterEntryNotMarkedForDeletion() {
        // Arrange
        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setDeletionFlag(null); // Simulate entry not marked for deletion
        MarkRegisterEntryForDeletionCommand command = new MarkRegisterEntryForDeletionCommand(registerEntry.getRegisterId(),
                DeletionReason.GDPR_REQUEST,
                null); // User details can be null for this test

        // Act & Assert
        validator.validateOrThrow(command, registerEntry); // Should not throw an exception
    }
}