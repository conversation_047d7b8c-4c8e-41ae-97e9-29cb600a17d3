<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="errors_protocol">

    <changeSet id="1.0.0" author="madalina-iuliana.gheorghe">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="errors_protocol"/>
            </not>
        </preConditions>
        <createTable tableName="errors_protocol">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="error_type" type="varchar(50)">
                <constraints nullable="false" primaryKey="false"/>
            </column>
            <column name="federal_state" type="varchar(2)">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="count" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="office" type="varchar(255)">
                <constraints nullable="true" primaryKey="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="1.0.0-index" author="madalina-iuliana.gheorghe">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists indexName="idx_errors_protocol_id" tableName="errors_protocol"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_errors_protocol_id" tableName="errors_protocol">
            <column name="id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1.0.0-index-year-federal_state" author="madalina-iuliana.gheorghe">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists indexName="idx_errors_protocol_year_federal_state" tableName="errors_protocol"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_errors_protocol_year_federal_state" tableName="errors_protocol">
            <column name="year"/>
            <column name="federal_state"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>