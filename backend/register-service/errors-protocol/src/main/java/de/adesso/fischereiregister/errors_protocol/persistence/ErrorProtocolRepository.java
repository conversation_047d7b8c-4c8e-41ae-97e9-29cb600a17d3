package de.adesso.fischereiregister.errors_protocol.persistence;

import de.adesso.fischereiregister.core.ports.contracts.statistics.ErrorsStatistics;
import de.adesso.fischereiregister.errors_protocol.persistence.model.ErrorType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for managing Protocol entities.
 * This interface extends CrudRepository to provide basic CRUD operations.
 */
@Component
@Repository
public interface ErrorProtocolRepository extends CrudRepository<ErrorProtocol, UUID> {
    /**
     * Retrieves error statistics for the specified years and federal state.
     *
     * @param years         List of years to filter the statistics.
     * @param federalState  Federal state to filter the statistics.
     * @return List of ErrorsStatistics containing the error counts for each year.
     */
    @Query("""
            SELECT new de.adesso.fischereiregister.core.ports.contracts.statistics.ErrorsStatistics(
                cast(y.year as integer),
                cast(coalesce(osErrors.osErrors, 0) as integer),
                cast(coalesce(cardErrors.cardErrors, 0) as integer),
                cast(coalesce(systemErrors.systemErrors, 0) as integer)
            )
            FROM (
                SELECT DISTINCT y3.year AS year
                FROM ErrorProtocol y3
                WHERE y3.year IN :years
            ) AS y
            LEFT JOIN (
                SELECT ep.year AS year, sum(ep.count) AS osErrors
                FROM ErrorProtocol ep
                WHERE ep.errorType = 'ONLINE_SERVICE_ERROR'
                  AND ep.year IN :years
                  AND (:federalState IS NULL OR (COALESCE(ep.federalState, "") = :federalState))
                  AND (:office IS NULL OR (COALESCE(ep.office, "") = :office))
                GROUP BY ep.year, ep.errorType
            ) AS osErrors ON y.year = osErrors.year
            LEFT JOIN (
                SELECT ep.year AS year, sum(ep.count) AS cardErrors
                FROM ErrorProtocol ep
                WHERE ep.errorType = 'CARD_ORDER_ERROR'
                  AND ep.year IN :years
                  AND (:federalState IS NULL OR (COALESCE(ep.federalState, "") = :federalState))
                  AND (:office IS NULL OR (COALESCE(ep.office, "") = :office))
                GROUP BY ep.year, ep.errorType
            ) AS cardErrors ON y.year = cardErrors.year
            LEFT JOIN (
                SELECT ep.year AS year, sum(ep.count) AS systemErrors
                FROM ErrorProtocol ep
                WHERE ep.errorType = 'SYSTEM_ERROR'
                  AND ep.year IN :years
                  AND (:federalState IS NULL OR (COALESCE(ep.federalState, "") = :federalState))
                  AND (:office IS NULL OR (COALESCE(ep.office, "") = :office))
                GROUP BY ep.year, ep.errorType
            ) AS systemErrors ON y.year = systemErrors.year
            """)
    List<ErrorsStatistics> selectErrorStatisticsByYearsAndFederalStateAndOffice(
            @Param("years") List<Integer> years,
            @Param("federalState") String federalState,
            @Param("office") String office);


    /** Finds an ErrorProtocol by error type, federal state, and year.
     *
     * @param errorType      The type of error to search for.
     * @param federalState   The federal state to filter by.
     * @param year           The year to filter by.
     * @return An Optional containing the ErrorProtocol if found, otherwise empty.
     */
    @Query("""
             SELECT ep
             FROM ErrorProtocol ep
             WHERE ep.errorType = :errorType
               AND ep.federalState = :federalState
               AND ep.year = :year
            """)
    Optional<ErrorProtocol> findByErrorTypeAndFederalStateAndYear(
            @Param("errorType") ErrorType errorType,
            @Param("federalState") String federalState,
            @Param("year") int year);

    @Query("""
             SELECT ep
             FROM ErrorProtocol ep
             WHERE ep.errorType = :errorType
               AND ep.federalState = :federalState
               AND ep.year = :year
               AND coalesce(ep.office, "") = :office
            """)
    Optional<ErrorProtocol> findByErrorTypeAndFederalStateAndYearAndOffice(
            @Param("errorType") ErrorType errorType,
            @Param("federalState") String federalState,
            @Param("year") int year,
            @Param("office") String office);

    /**
     * Retrieves a list of distinct years from the ErrorProtocol table.
     *
     * @return A list of distinct years in descending order.
     */
    @Query("""
             SELECT DISTINCT ep.year
             FROM ErrorProtocol ep
                ORDER BY ep.year DESC
            """)
    List<Integer> selectYears();
}
