package de.adesso.fischereiregister.errors_protocol.persistence;

import de.adesso.fischereiregister.errors_protocol.persistence.model.ErrorType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "errors_protocol")
@Getter
@Setter
public class ErrorProtocol {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ErrorType errorType;

    @Column(nullable = false)
    private String federalState;

    @Column(nullable = false)
    private int year;

    @Column(nullable = false)
    private int count;

    // this is nullable and right now only used for card orders
    @Column
    private String office;

}