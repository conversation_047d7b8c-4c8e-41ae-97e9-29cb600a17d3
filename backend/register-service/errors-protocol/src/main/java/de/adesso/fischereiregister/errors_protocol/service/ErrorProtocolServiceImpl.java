package de.adesso.fischereiregister.errors_protocol.service;

import de.adesso.fischereiregister.core.ports.contracts.statistics.ErrorsStatistics;
import de.adesso.fischereiregister.errors_protocol.persistence.ErrorProtocolRepository;
import de.adesso.fischereiregister.errors_protocol.persistence.model.ErrorType;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class ErrorProtocolServiceImpl implements ErrorProtocolService {
    ErrorProtocolRepository errorProtocolRepository;

    /**
     * This method is used to report a failure that occurred in the system.
     * It will update the error count for the given federal state and year and office (if applicable).
     * If no entry exists, it will create a new one with a count of 1.
     *
     * @param office        The office where the error occurred. (this will only be filled for card orders)
     * @param errorType    The type of error that occurred.
     * @param federalState The federal state where the error occurred.
     * @param year         The year when the error occurred.
     */
    private void reportFailure(ErrorType errorType, String federalState, String office, int year) {
        // select row using failureType, federal state, office and year or insert new row in database
        errorProtocolRepository.findByErrorTypeAndFederalStateAndYearAndOffice(errorType, federalState, year, office)
                .ifPresentOrElse(
                        errorProtocol -> {
                            errorProtocol.setCount(errorProtocol.getCount() + 1);
                            errorProtocolRepository.save(errorProtocol);
                        },
                        () -> {
                            var newErrorProtocol = new de.adesso.fischereiregister.errors_protocol.persistence.ErrorProtocol();
                            newErrorProtocol.setErrorType(errorType);
                            newErrorProtocol.setFederalState(federalState);
                            newErrorProtocol.setYear(year);
                            newErrorProtocol.setCount(1);
                            newErrorProtocol.setOffice(office);
                            errorProtocolRepository.save(newErrorProtocol);
                        }
                );
    }

    private void reportFailure(ErrorType errorType, String federalState, int year) {
        // select row using failureType, federal state and year and add an error to count or insert new row in database with count = 1
        errorProtocolRepository.findByErrorTypeAndFederalStateAndYear(errorType, federalState, year)
                .ifPresentOrElse(
                        errorProtocol -> {
                            errorProtocol.setCount(errorProtocol.getCount() + 1);
                            errorProtocolRepository.save(errorProtocol);
                        },
                        () -> {
                            var newErrorProtocol = new de.adesso.fischereiregister.errors_protocol.persistence.ErrorProtocol();
                            newErrorProtocol.setErrorType(errorType);
                            newErrorProtocol.setFederalState(federalState);
                            newErrorProtocol.setYear(year);
                            newErrorProtocol.setCount(1);
                            errorProtocolRepository.save(newErrorProtocol);
                        }
                );
    }

    @Override
    public List<ErrorsStatistics> selectErrorStatisticsByYearsAndFederalStateAndOffice(List<Integer> years, String federalState, String office) {
        List<Integer> yearsForSelection = years;
        if(yearsForSelection == null || yearsForSelection.isEmpty()) {
            yearsForSelection = errorProtocolRepository.selectYears();
        }

        return errorProtocolRepository.selectErrorStatisticsByYearsAndFederalStateAndOffice(yearsForSelection, federalState, office);
    }

    @Override
    public void reportOSError(String federalState, int year) {
        this.reportFailure(ErrorType.ONLINE_SERVICE_ERROR, federalState, year);
    }

    @Override
    public void reportSystemError(String office, String federalState, int year) {
        this.reportFailure(ErrorType.SYSTEM_ERROR, federalState, office, year);
    }

    @Override
    public void reportCardOrdersError(String office, String federalState, int year) {
        this.reportFailure(ErrorType.CARD_ORDER_ERROR, federalState, office, year);
    }

    @Override
    public List<Integer> getAvailableYears() {
        return errorProtocolRepository.selectYears();
    }
}