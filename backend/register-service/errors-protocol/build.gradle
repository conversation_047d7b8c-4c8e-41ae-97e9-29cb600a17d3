group = 'de.adesso.fischereiregister'
version = parent.project.version

dependencies {
    compileOnly 'org.projectlombok:lombok:1.18.36'
    annotationProcessor 'org.projectlombok:lombok:1.18.36'

    platform('org.axonframework:axon-bom:4.10.4')


    implementation project(':core')

    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'


    testImplementation project(':testutils')

    testImplementation 'org.springframework.boot:spring-boot-starter-test:3.4.6'
    testImplementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.1'
    testImplementation "org.axonframework:axon-test"
    testImplementation(platform("org.junit:junit-bom:5.12.1"))
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
}
