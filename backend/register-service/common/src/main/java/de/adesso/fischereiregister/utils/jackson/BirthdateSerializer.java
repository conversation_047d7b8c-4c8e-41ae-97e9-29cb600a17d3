package de.adesso.fischereiregister.utils.jackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import de.adesso.fischereiregister.core.model.Birthdate;

import java.io.IOException;

public class BirthdateSerializer extends StdSerializer<Birthdate> {

    public BirthdateSerializer() {
        this(null);
    }

    public BirthdateSerializer(Class<Birthdate> t) {
        super(t);
    }

    @Override
    public void serialize(
            Birthdate value, JsonGenerator jGen, SerializerProvider provider)
            throws IOException {

        jGen.writeString(value.toString());
    }

    @Override
    public Class<Birthdate> handledType() {
        return Birthdate.class;
    }
}
