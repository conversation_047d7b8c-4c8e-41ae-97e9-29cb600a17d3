package de.adesso.fischereiregister.utils.jackson;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import de.adesso.fischereiregister.core.model.Birthdate;

import java.io.IOException;

public class BirthdateDeserializer extends StdDeserializer<Birthdate> {


    public BirthdateDeserializer() {
        this(null);
    }

    public BirthdateDeserializer(Class<?> vc) {
        super(vc);
    }

    @Override
    public Birthdate deserialize(JsonParser jp, DeserializationContext ctxt)
            throws IOException {
        return Birthdate.parse(jp.getText());
    }

    @Override
    public Class<Birthdate> handledType() {
        return Birthdate.class;
    }

}
