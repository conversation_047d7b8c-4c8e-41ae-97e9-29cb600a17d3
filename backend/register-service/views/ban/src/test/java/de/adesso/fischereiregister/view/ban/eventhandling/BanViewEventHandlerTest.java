package de.adesso.fischereiregister.view.ban.eventhandling;

import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.view.ban.services.BanViewService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class BanViewEventHandlerTest {

    @Mock
    BanViewService service;

    @InjectMocks
    BanViewEventHandler eventHandler;

    @Test
    @DisplayName("BanViewEventHandler.on(BannedEvent) Should create ban view when ban is created")
    void testBanEventHandling() {
        //  Given
        UUID registerEntryId = UUID.randomUUID();
        LocalDate dateTo = LocalDate.of(2020, 1, 1);
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");

        Ban ban = new Ban();
        ban.setTo(dateTo);
        BannedEvent event = new BannedEvent(registerEntryId, ban, jurisdiction, "Test Office");

        // When
        eventHandler.on(event);

        // Then
        verify(service, times(1)).create(registerEntryId, ban, "SH");
    }

    @Test
    @DisplayName("BanViewEventHandler.on(UnbannedEvent) Should delete ban view when ban is deleted")
    void testUnbanEventHandling() {
        //  Given
        UUID registerEntryId = UUID.randomUUID();
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");

        UnbannedEvent event = new UnbannedEvent(registerEntryId, jurisdiction);

        // When
        eventHandler.on(event);

        // Then
        verify(service, times(1)).deleteByRegisterEntryId(registerEntryId);
    }

    @Test
    @DisplayName("BanViewEventHandler.on(JurisdictionMovedEvent) Should update federal state when ban exists")
    void testJurisdictionMovedEventHandling() {
        //  Given
        UUID registerEntryId = UUID.randomUUID();
        Jurisdiction previousJurisdiction = new Jurisdiction();
        previousJurisdiction.setFederalState("SH");
        Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState("BY");

        Ban ban = new Ban();
        ban.setTo(LocalDate.of(2020, 1, 1));
        JurisdictionMovedEvent event = new JurisdictionMovedEvent(registerEntryId, previousJurisdiction, newJurisdiction, null, ban, List.of(), null, null, List.of(), null);

        // When
        eventHandler.on(event);

        // Then
        verify(service, times(1)).updateFederalState(registerEntryId, "BY");
    }

    @Test
    @DisplayName("BanViewEventHandler.on(JurisdictionMovedEvent) Should not update federal state when ban does not exist")
    void testJurisdictionMovedEventHandlingNoBan() {
        //  Given
        UUID registerEntryId = UUID.randomUUID();
        Jurisdiction previousJurisdiction = new Jurisdiction();
        previousJurisdiction.setFederalState("SH");
        Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState("BY");

        JurisdictionMovedEvent event = new JurisdictionMovedEvent(registerEntryId, previousJurisdiction, newJurisdiction, null, null, List.of(), null, null, List.of(), null);

        // When
        eventHandler.on(event);

        // Then
        verify(service, never()).updateFederalState(registerEntryId, "BY");
    }

}
