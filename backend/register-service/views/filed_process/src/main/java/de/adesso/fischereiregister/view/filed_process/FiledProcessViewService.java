package de.adesso.fischereiregister.view.filed_process;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.view.filed_process.eventhandling.FiledProcessViewHandler;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessView;

import java.util.List;
import java.util.UUID;

/**
 * This service handles the creation and retrieving of processes for the register file (Vorgangsakte).
 * <p>
 * The data of the events is passed into the corresponding methods by the {@link FiledProcessViewHandler},
 * so for each event a process is created. These processes can be retrieved for the register file for specific register
 * entry. In a register file, only the processes for the federal state of the requesting institution are shown.
 * </p>
 */
public interface FiledProcessViewService {

    void truncateView();

    /**
     * Get all processes for a register entry id filtered by the federal state.
     *
     * @param registerEntryId The register entry id for which the processes are retrieved.
     * @param federalState    The federal state to which the processes have to belong to.
     * @return A list of the found processes, can be empty.
     */
    List<FiledProcessView> getFiledProcesses(UUID registerEntryId, FederalState federalState);

    void createQualificationsProofCreatedProcess(ProcessHeaderData headerData,
                                                 FiledProcessData data);

    void createJurisdictionMovedProcess(ProcessHeaderData headerData,
                                        FiledProcessData data);

    void createFishingLicenseCreatedProcess(ProcessHeaderData headerData,
                                            FiledProcessData data);

    void createFishingLicenseExtendedProcess(ProcessHeaderData headerData,
                                             FiledProcessData data);

    void createReplacementCardOrderedProcess(ProcessHeaderData headerData,
                                             FiledProcessData data);

    void createFishingTaxPayedProcess(ProcessHeaderData headerData,
                                      FiledProcessData data);

    void createBannedProcess(ProcessHeaderData headerData,
                             FiledProcessData data);

    void createUnbannedProcess(ProcessHeaderData headerData);

    void createLimitedLicenseApplicationProcess(ProcessHeaderData headerData,
                                                FiledProcessData data);

    void createLimitedLicenseRejectedProcess(ProcessHeaderData headerData);

    void createMarkedForDeletionProcess(ProcessHeaderData headerData,
                                        FiledProcessData data);
}
