package de.adesso.fischereiregister.view.filed_process;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessType;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessView;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessViewRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
public class FiledProcessViewServiceImpl implements FiledProcessViewService {
    private final FiledProcessViewRepository filedProcessViewRepository;
    private final JdbcTemplate jdbcTemplate;

    @Override
    @Transactional
    public void truncateView() {
        jdbcTemplate.execute("TRUNCATE TABLE filed_process_view");
    }

    @Override
    @Transactional
    public List<FiledProcessView> getFiledProcesses(UUID registerEntryId, FederalState federalState) {
        List<FiledProcessView> processesForRegisterEntry = filedProcessViewRepository.findByRegisterEntryId(registerEntryId);
        if (processesForRegisterEntry.isEmpty()) {
            throw new EntityNotFoundException();
        }

        return filedProcessViewRepository.findByRegisterEntryIdAndFederalStateOfInstitution(registerEntryId, federalState);
    }

    @Override
    public void createQualificationsProofCreatedProcess(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = this.createView(headerData, data);
        process.setFiledProcessType(FiledProcessType.QUALIFICATIONS_PROOF_CREATED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createJurisdictionMovedProcess(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = this.createView(headerData, data);
        process.setFiledProcessType(FiledProcessType.JURISDICTION_CHANGED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createFishingLicenseCreatedProcess(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = this.createView(headerData, data);
        process.setFiledProcessType(FiledProcessType.FISHING_LICENSE_CREATED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createFishingLicenseExtendedProcess(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = this.createView(headerData, data);
        process.setFiledProcessType(FiledProcessType.FISHING_LICENSE_EXTENDED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createReplacementCardOrderedProcess(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = this.createView(headerData, data);
        process.setFiledProcessType(FiledProcessType.REPLACEMENT_CARD_ORDERED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createFishingTaxPayedProcess(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = this.createView(headerData, data);
        process.setFiledProcessType(FiledProcessType.FISHING_TAX_CREATED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createBannedProcess(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = this.createView(headerData, data);
        process.setFiledProcessType(FiledProcessType.BANNED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createUnbannedProcess(ProcessHeaderData headerData) {
        FiledProcessView process = this.createView(headerData);
        process.setFiledProcessType(FiledProcessType.UNBANNED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createLimitedLicenseApplicationProcess(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = this.createView(headerData, data);
        process.setFiledProcessType(FiledProcessType.LIMITED_LICENSE_APPLICATION_CREATED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createLimitedLicenseRejectedProcess(ProcessHeaderData headerData) {
        FiledProcessView process = this.createView(headerData);
        process.setFiledProcessType(FiledProcessType.LIMITED_LICENSE_APPLICATION_REJECTED);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createMarkedForDeletionProcess(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = this.createView(headerData, data);
        process.setFiledProcessType(FiledProcessType.MARKED_FOR_DELETION);

        this.filedProcessViewRepository.save(process);
    }

    private FiledProcessView createView(ProcessHeaderData headerData) {
        return createView(headerData, FiledProcessData.builder().build());
    }

    private FiledProcessView createView(ProcessHeaderData headerData, FiledProcessData data) {
        FiledProcessView process = new FiledProcessView();
        process.setRegisterEntryId(headerData.getRegisterEntryId());
        process.setActingInstitution(headerData.getActingInstitution());
        process.setFederalStateOfInstitution(headerData.getFederalStateOfInstitution());
        process.setProcessTimestamp(headerData.getTimestamp());
        process.setFederalState(headerData.getFederalState());
        process.setIssuedBy(headerData.getIssuedBy());

        process.setFiledProcessData(data);

        return process;
    }
}
