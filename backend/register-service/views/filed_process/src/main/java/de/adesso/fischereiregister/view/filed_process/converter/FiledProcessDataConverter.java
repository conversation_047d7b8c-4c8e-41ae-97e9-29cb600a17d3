package de.adesso.fischereiregister.view.filed_process.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.consent.AbstractConsentInfo;
import de.adesso.fischereiregister.utils.jackson.BirthdateDeserializer;
import de.adesso.fischereiregister.utils.jackson.BirthdateSerializer;
import de.adesso.fischereiregister.view.filed_process.FiledProcessData;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Converter
@Component
public class FiledProcessDataConverter implements AttributeConverter<FiledProcessData, String> {
    private final ObjectMapper objectMapper;

    @Autowired
    public FiledProcessDataConverter(ObjectMapper mapper) {
        objectMapper = mapper.copy();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(createBirthdateModule());
        objectMapper.addMixIn(AbstractConsentInfo.class, AbstractConsentInfoMixIn.class);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }

    @Override
    public String convertToDatabaseColumn(FiledProcessData attribute) throws RuntimeException {
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize FiledProcessData to JSON", e);
        }
    }

    @Override
    public FiledProcessData convertToEntityAttribute(String dbData) throws RuntimeException {
        try {
            return objectMapper.readValue(dbData, FiledProcessData.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to deserialize JSON to FiledProcessData", e);
        }
    }

    private SimpleModule createBirthdateModule() {
        return new SimpleModule()
                .addSerializer(Birthdate.class, new BirthdateSerializer())
                .addDeserializer(Birthdate.class, new BirthdateDeserializer());
    }
}
