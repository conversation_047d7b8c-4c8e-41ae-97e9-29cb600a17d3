package de.adesso.fischereiregister.view.filed_process;

import de.adesso.fischereiregister.core.model.type.FederalState;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

import java.time.Instant;
import java.util.UUID;

@Getter
@AllArgsConstructor
@Builder
public class ProcessHeaderData {
    UUID registerEntryId;
    String actingInstitution;
    FederalState federalStateOfInstitution;
    Instant timestamp;
    FederalState federalState;
    String issuedBy;
}
