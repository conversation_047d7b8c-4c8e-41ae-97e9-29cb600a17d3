package de.adesso.fischereiregister.view.filed_process.converter;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.LimitedLicenseConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        property = "type"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = ConsentInfo.class),
        @JsonSubTypes.Type(value = JurisdictionConsentInfo.class),
        @JsonSubTypes.Type(value = LimitedLicenseConsentInfo.class),
        @JsonSubTypes.Type(value = TaxConsentInfo.class)
})
public interface AbstractConsentInfoMixIn {
}
