package de.adesso.fischereiregister.view.filed_process.persistence;

import de.adesso.fischereiregister.core.model.type.FederalState;
import org.springframework.data.repository.CrudRepository;

import java.util.List;
import java.util.UUID;

public interface FiledProcessViewRepository extends CrudRepository<FiledProcessView, Long> {

    /**
     * Finds all processes for the given registerEntryId.
     *
     * @param registerEntryId The register entry id for which the processes are retrieved.
     * @return A list of the found processes, can be empty.
     */
    List<FiledProcessView> findByRegisterEntryId(UUID registerEntryId);

    /**
     * Finds all processes for the given registerEntryId filtered by the federalState.
     *
     * @param registerEntryId The register entry id for which the processes are retrieved.
     * @param federalState    The federal state to which the processes have to belong to.
     * @return A list of the found processes, can be empty.
     */
    List<FiledProcessView> findByRegisterEntryIdAndFederalStateOfInstitution(UUID registerEntryId,
                                                                             FederalState federalState);
}
