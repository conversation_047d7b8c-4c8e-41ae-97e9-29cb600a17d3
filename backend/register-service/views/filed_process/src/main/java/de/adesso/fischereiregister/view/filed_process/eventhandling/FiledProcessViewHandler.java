package de.adesso.fischereiregister.view.filed_process.eventhandling;

import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegisterEntryMarkedForDeletionEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.view.filed_process.FiledProcessData;
import de.adesso.fischereiregister.view.filed_process.FiledProcessViewService;
import de.adesso.fischereiregister.view.filed_process.ProcessHeaderData;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.axonframework.eventhandling.Timestamp;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

/**
 * All events besides the PersonalDataChangedEvent are listed in the process file. For each event the process with the
 * corresponding data is created.
 */
@Component
public class FiledProcessViewHandler {
    private final FiledProcessViewService filedProcessViewService;

    public FiledProcessViewHandler(FiledProcessViewService filedProcessViewService) {
        this.filedProcessViewService = filedProcessViewService;
    }

    @ResetHandler
    @Transactional
    public void onReset() {
        filedProcessViewService.truncateView();
    }


    @EventHandler
    @Transactional
    public void on(BannedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .ban(event.ban())
                .build();

        filedProcessViewService.createBannedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(FishingTaxPayedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(FederalState.valueOf(event.taxes().getFirst().getFederalState()))
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .serviceAccountId(event.serviceAccountId())
                .taxes(event.taxes())
                .consentInfo(event.consentInfo())
                .identificationDocuments(event.identificationDocuments())
                .build();

        filedProcessViewService.createFishingTaxPayedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(JurisdictionMovedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(FederalState.valueOf(event.newJurisdiction().getFederalState()))
                .federalState(FederalState.valueOf(event.newJurisdiction().getFederalState()))
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .taxes(event.taxes())
                .consentInfo(event.consentInfo())
                .identificationDocuments(event.identificationDocuments())
                .build();

        filedProcessViewService.createJurisdictionMovedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(LicenseExtendedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(FederalState.valueOf(
                        event.fees().getFirst().getFederalState()))
                .timestamp(timestamp)
                .federalState(null)
                .issuedBy(event.issuedByOffice())
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .serviceAccountId(event.serviceAccountId())
                .taxes(event.taxes())
                .fees(event.fees())
                .identificationDocuments(event.identificationDocuments())
                .fishingLicenseNumber(event.licenseNumber())
                .consentInfo(event.consentInfo())
                .build();

        filedProcessViewService.createFishingLicenseExtendedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(LimitedLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.fishingLicense().getIssuingFederalState())
                .timestamp(timestamp)
                .federalState(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .issuedBy(event.issuedByOffice())
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .serviceAccountId(event.serviceAccountId())
                .taxes(event.taxes())
                .fees(event.fees())
                .identificationDocuments(event.identificationDocuments())
                .fishingLicense(event.fishingLicense())
                .consentInfo(event.limitedLicenseConsentInfo())
                .build();

        filedProcessViewService.createFishingLicenseCreatedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(LimitedLicenseApplicationCreatedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(null)
                .federalStateOfInstitution(event.limitedLicenseApplication().getFederalState())
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .serviceAccountId(event.serviceAccountId())
                .fees(event.fees())
                .consentInfo(event.consentInfo())
                .build();

        filedProcessViewService.createLimitedLicenseApplicationProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(LimitedLicenseApplicationRejectedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.limitedLicenseApplication().getFederalState())
                .timestamp(timestamp)
                .build();

        filedProcessViewService.createLimitedLicenseRejectedProcess(processHeaderData);
    }

    @EventHandler
    @Transactional
    public void on(PersonCreatedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(FederalState.valueOf(event.federalState()))
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .serviceAccountId(event.serviceAccountId())
                .taxes(event.taxes())
                .identificationDocuments(event.identificationDocuments())
                .consentInfo(event.consentInfo())
                .build();

        filedProcessViewService.createFishingTaxPayedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(QualificationsProofCreatedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(null)
                .federalStateOfInstitution(FederalState.valueOf(
                        event.qualificationsProof().getFederalState()))
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .qualificationsProof(event.qualificationsProof())
                .build();

        filedProcessViewService.createQualificationsProofCreatedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(RegularLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.fishingLicense().getIssuingFederalState())
                .timestamp(timestamp)
                .federalState(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .issuedBy(event.issuedByOffice())
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .serviceAccountId(event.serviceAccountId())
                .taxes(event.taxes())
                .fees(event.fees())
                .identificationDocuments(event.identificationDocuments())
                .fishingLicense(event.fishingLicense())
                .consentInfo(event.consentInfo())
                .build();

        filedProcessViewService.createFishingLicenseCreatedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(RegularLicenseDigitizedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.fishingLicense().getIssuingFederalState())
                .timestamp(timestamp)
                .federalState(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .issuedBy(event.issuedByOffice())
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .taxes(event.taxes())
                .fees(event.fees())
                .identificationDocuments(event.identificationDocuments())
                .fishingLicense(event.fishingLicense())
                .consentInfo(event.consentInfo())
                .build();

        filedProcessViewService.createFishingLicenseCreatedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(ReplacementCardOrderedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.federalState())
                .timestamp(timestamp)
                .federalState(event.federalState())
                .issuedBy(event.issuedByOffice())
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .serviceAccountId(event.serviceAccountId())
                .taxes(event.taxes())
                .fees(event.fees())
                .identificationDocuments(event.identificationDocuments())
                .fishingLicense(event.fishingLicense())
                .consentInfo(event.consentInfo())
                .build();

        filedProcessViewService.createReplacementCardOrderedProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(UnbannedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .federalStateOfInstitution(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .timestamp(timestamp)
                .build();

        filedProcessViewService.createUnbannedProcess(processHeaderData);
    }

    @EventHandler
    @Transactional
    public void on(RegisterEntryMarkedForDeletionEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .federalStateOfInstitution(FederalState.valueOf(event.userFederalState()))
                .actingInstitution(event.office())
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .deletionFlag(event.deletionFlag())
                .build();

        filedProcessViewService.createMarkedForDeletionProcess(processHeaderData, data);
    }

    @EventHandler
    @Transactional
    public void on(VacationLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        final ProcessHeaderData headerData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.fishingLicense().getIssuingFederalState())
                .timestamp(timestamp)
                .federalState(event.fishingLicense().getIssuingFederalState())
                .issuedBy(event.issuedByOffice())
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(event.person())
                .serviceAccountId(event.serviceAccountId())
                .taxes(event.taxes())
                .fees(event.fees())
                .identificationDocuments(event.identificationDocuments())
                .fishingLicense(event.fishingLicense())
                .consentInfo(event.consentInfo())
                .build();

        filedProcessViewService.createFishingLicenseCreatedProcess(headerData, data);
    }
}
