<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="filed_process_view">

    <changeSet id="1.0.0" author="hannes.borgemehn">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="filed_process_view"/>
            </not>
        </preConditions>

        <createTable tableName="filed_process_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="register_entry_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="acting_institution" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="federal_state_of_institution" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="process_timestamp" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="federal_state" type="VARCHAR(2)">
                <constraints nullable="true"/>
            </column>
            <column name="issued_by" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="filed_process_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="filed_process_data" type="mediumtext">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
