ARG DOCKER_REGISTRY_URL=docker.io
ARG GRADLE_REGISTRY_USER_ID
ARG GRADLE_REGISTRY_TOKEN

FROM ${DOCKER_REGISTRY_URL}/gradle:8.12-jdk21-alpine AS builder
ARG MAVEN_SDK_REGISTRY_USER_ID
ARG MAVEN_SDK_REGISTRY_TOKEN
WORKDIR /app
ENV GRADLE_REGISTRY_USER_ID ${GRADLE_REGISTRY_USER_ID}
ENV GRADLE_REGISTRY_TOKEN ${GRADLE_REGISTRY_TOKEN}
ENV MAVEN_SDK_REGISTRY_USER_ID ${MAVEN_SDK_REGISTRY_USER_ID}
ENV MAVEN_SDK_REGISTRY_TOKEN ${MAVEN_SDK_REGISTRY_TOKEN}

# Add new modules here
COPY build.gradle settings.gradle ./
COPY core core
COPY views views
COPY message-service message-service
COPY card_orders card_orders
COPY migrations migrations
COPY inspector-protocol inspector-protocol
COPY errors-protocol errors-protocol
COPY common common

RUN gradle dependencies --write-locks
COPY ./src ./src
RUN gradle clean build -x test

FROM ${DOCKER_REGISTRY_URL}/eclipse-temurin:21-jdk-alpine

# Update packages to fix security vulnerabilities
RUN apk update && \
    apk upgrade && \
    apk add --no-cache binutils>=2.43.1-r2 libexpat>=2.7.0-r0 sqlite-libs>=3.48.0-r1

RUN addgroup -g 1001 appgroup && \
    adduser -D -u 1001 -G appgroup -h /app -s /bin/sh appuser && \
    mkdir -p /app && \
    chown -R appuser:appgroup /app
WORKDIR /app
EXPOSE 8080
RUN chown -R appuser:appgroup /app
COPY --from=builder --chown=appuser:appgroup /app/build/libs /app/libs
RUN find /app/libs -name "*.jar" ! -name "*-plain.jar" -exec mv {} /app/ \;
USER appuser

ENTRYPOINT ["sh", "-c", "java ${JAVA_OPTS} -jar /app/*.jar"]