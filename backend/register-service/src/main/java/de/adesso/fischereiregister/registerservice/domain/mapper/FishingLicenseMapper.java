package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.FishingLicense;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default", uses = {LicenseTypeMapper.class})
public interface FishingLicenseMapper {

    FishingLicenseMapper INSTANCE = Mappers.getMapper(FishingLicenseMapper.class);

    org.openapitools.model.FishingLicense toDto(de.adesso.fischereiregister.core.model.FishingLicense fishingLicense);

    org.openapitools.model.FishingLicenseFP toDtoFP(FishingLicense fishingLicense);
}