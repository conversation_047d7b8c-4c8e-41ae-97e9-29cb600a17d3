package de.adesso.fischereiregister.registerservice.register_entry_view;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.DeletionReason;
import lombok.Builder;
import lombok.Getter;

import java.util.List;
import java.util.UUID;

@Builder
@Getter
public class RegisterEntryViewServiceParameters {
    private final UUID registerEntryId;
    private final Person person;
    private final Jurisdiction jurisdiction;
    private final Ban ban;
    private final FishingLicense fishingLicense;
    private final List<Fee> fees;
    private final List<Tax> taxes;
    private final List<QualificationsProof> qualificationsProofs;
    private final List<IdentificationDocument> identificationDocuments;
    private final LimitedLicenseApplication limitedLicenseApplication;
    private final DeletionReason deletionReason;
}
