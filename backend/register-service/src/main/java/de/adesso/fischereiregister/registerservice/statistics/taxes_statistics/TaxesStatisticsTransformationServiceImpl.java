package de.adesso.fischereiregister.registerservice.statistics.taxes_statistics;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class TaxesStatisticsTransformationServiceImpl implements TaxesStatisticsTransformationService {

    @Override
    public List<TaxesStatistics> transformToTaxesStatistics(List<TaxesStatisticsView> statisticsViews, List<Integer> yearsToQuery) {
        try {
            // Group statistics by year
            Map<Integer, List<TaxesStatisticsView>> statsByYear = statisticsViews.stream()
                    .collect(Collectors.groupingBy(TaxesStatisticsView::getYear));

            List<TaxesStatistics> result = new ArrayList<>();

            // Process each requested year
            for (Integer year : yearsToQuery) {
                List<TaxesStatisticsView> yearStats = statsByYear.get(year);
                List<TaxesStatisticsDataEntry> data = processYearStatistics(yearStats);

                TaxesStatistics taxesStatistics = new TaxesStatistics(year, data);
                result.add(taxesStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(TaxesStatistics::year).reversed());

            return result;
        } catch (Exception e) {
            log.error("Error transforming TaxesStatisticsView to TaxesStatistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to transform TaxesStatisticsView to TaxesStatistics", e);
        }
    }

    private List<TaxesStatisticsDataEntry> processYearStatistics(List<TaxesStatisticsView> yearStats) {
        if (yearStats == null || yearStats.isEmpty()) {
            return createZeroEntries();
        }

        Map<SubmissionType, Map<Integer, List<TaxesStatisticsView>>> statsBySourceAndDuration = groupBySourceAndDuration(yearStats);

        return createDataEntries(statsBySourceAndDuration);
    }

    private Map<SubmissionType, Map<Integer, List<TaxesStatisticsView>>> groupBySourceAndDuration(List<TaxesStatisticsView> yearStats) {
        return yearStats.stream()
                .collect(Collectors.groupingBy(TaxesStatisticsView::getSource,
                        Collectors.groupingBy(TaxesStatisticsView::getDuration)));
    }

    private List<TaxesStatisticsDataEntry> createDataEntries(Map<SubmissionType, Map<Integer, List<TaxesStatisticsView>>> statsBySourceAndDuration) {
        List<TaxesStatisticsDataEntry> data = new ArrayList<>();

        for (Map.Entry<SubmissionType, Map<Integer, List<TaxesStatisticsView>>> sourceEntry : statsBySourceAndDuration.entrySet()) {
            SubmissionType source = sourceEntry.getKey();
            SubmissionType remainingSource = source == SubmissionType.ONLINE ? SubmissionType.ANALOG : SubmissionType.ONLINE;

            Map<Integer, List<TaxesStatisticsView>> durationStats = sourceEntry.getValue();

            for (Map.Entry<Integer, List<TaxesStatisticsView>> durationEntry : durationStats.entrySet()) {
                int duration = durationEntry.getKey();
                List<TaxesStatisticsView> durationSourceStats = durationEntry.getValue();

                TaxesStatisticsDataEntry dataItem = createDataEntry(source, duration, durationSourceStats);
                data.add(dataItem);

                TaxesStatisticsDataEntry remainingDataItem = new TaxesStatisticsDataEntry(remainingSource, duration, 0, 0.0);
                data.add(remainingDataItem); // Ensure all submission types are included
            }
        }

        return data;
    }

    private TaxesStatisticsDataEntry createDataEntry(SubmissionType source, int duration, List<TaxesStatisticsView> durationSourceStats) {
        int totalCount = durationSourceStats.stream()
                .mapToInt(TaxesStatisticsView::getCount)
                .sum();

        double totalRevenue = durationSourceStats.stream()
                .mapToDouble(TaxesStatisticsView::getRevenue)
                .sum();

        return new TaxesStatisticsDataEntry(source, duration, totalCount, totalRevenue);
    }

    private List<TaxesStatisticsDataEntry> createZeroEntries() {
        return List.of(
                new TaxesStatisticsDataEntry(SubmissionType.ONLINE, 1, 0, 0.0),
                new TaxesStatisticsDataEntry(SubmissionType.ANALOG, 1, 0, 0.0)
        );
    }

}
