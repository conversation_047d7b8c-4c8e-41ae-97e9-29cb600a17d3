package de.adesso.fischereiregister.registerservice.register_file;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.register_file.mapper.FiledProcessMapper;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.view.filed_process.FiledProcessViewService;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessView;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.model.RegisterFileResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@RestController
@Slf4j
@AllArgsConstructor
public class RegisterFileController implements api.RegisterFileApi {
    FiledProcessViewService filedProcessViewService;
    UserDetailsService userDetailsService;

    @Override
    public ResponseEntity<?> registerFileControllerGet(String registerEntryId) {
        try {
            RegisterFileResponse response = new RegisterFileResponse();

            response.setRegisterEntryId(registerEntryId);
            response.setCreatedAt(Instant.now().toString());
            response.setCreatedByInstitution(userDetailsService.getOffice().orElseThrow(() -> {
                        log.error("Failed to retrieve user details: User details are missing or corrupted.");
                        return new IllegalStateException("The User Auth Information is missing or corrupted");
                    }
            ));

            List<FiledProcessView> filedProcessViews = filedProcessViewService.getFiledProcesses(UUID.fromString(registerEntryId),
                    FederalState.valueOf(userDetailsService.getFederalState()));

            response.setFiledProcesses(FiledProcessMapper.INSTANCE.toDtos(filedProcessViews));
            return ResponseEntity.ok(response);

        } catch (EntityNotFoundException e) {
            log.warn("RegisterEntry with id {} not found, error response: {}", registerEntryId, e.getMessage());
            return ResponseEntity.notFound().build();

        }

    }

}
