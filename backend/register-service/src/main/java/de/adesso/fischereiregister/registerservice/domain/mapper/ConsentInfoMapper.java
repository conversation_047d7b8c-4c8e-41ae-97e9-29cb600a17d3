package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.consent.AbstractConsentInfo;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.LimitedLicenseConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ConsentInfoMapper {

    ConsentInfoMapper INSTANCE = Mappers.getMapper(ConsentInfoMapper.class);

    ConsentInfo toConsentInfo(org.openapitools.model.ConsentInfo apiModelConsentInfo);

    default org.openapitools.model.ConsentInfoFP toDtoFP(AbstractConsentInfo consentInfo) {
        if (consentInfo == null) {
            return null;
        }
        
        return switch (consentInfo) {
            case LimitedLicenseConsentInfo c -> toDtoFP(c);
            case JurisdictionConsentInfo c -> toDtoFP(c);
            case TaxConsentInfo c -> toDtoFP(c);
            case ConsentInfo c -> toDtoFP(c); // ConsentInfo has to come last, as it is parent of other types.
            default -> throw new IllegalArgumentException("Unsupported type: " + consentInfo.getClass());
        };
    }

    @Mapping(target = "disabilityCertificateVerified", ignore = true)
    @Mapping(target = "proofOfMoveVerified", ignore = true)
    org.openapitools.model.ConsentInfoFP toDtoFP(ConsentInfo consentInfo);

    @Mapping(target = "disabilityCertificateVerified", ignore = true)
    org.openapitools.model.ConsentInfoFP toDtoFP(JurisdictionConsentInfo consentInfo);

    @Mapping(target = "proofOfMoveVerified", ignore = true)
    @Mapping(target = "disabilityCertificateVerified", source = "disablityCertificateVerified")
    org.openapitools.model.ConsentInfoFP toDtoFP(LimitedLicenseConsentInfo consentInfo);

    @Mapping(target = "selfDisclosureAccepted", ignore = true)
    @Mapping(target = "disabilityCertificateVerified", ignore = true)
    @Mapping(target = "proofOfMoveVerified", ignore = true)
    org.openapitools.model.ConsentInfoFP toDtoFP(TaxConsentInfo consentInfo);
}
