package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.core.ports.contracts.statistics.ErrorsStatistics;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "default", uses = ErrorsStatisticsDataMapper.class)
public interface ErrorsStatisticsMapper {

    ErrorsStatisticsMapper INSTANCE = Mappers.getMapper(ErrorsStatisticsMapper.class);

    List<org.openapitools.model.ErrorsStatistics> toResponse(List<ErrorsStatistics> list);
}
