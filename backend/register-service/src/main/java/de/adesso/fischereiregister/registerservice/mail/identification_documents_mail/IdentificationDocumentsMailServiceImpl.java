package de.adesso.fischereiregister.registerservice.mail.identification_documents_mail;

import de.adesso.fischereiregister.core.exceptions.RestrictedDocumentDeliveryException;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.utils.IdentificationDocumentUtils;
import de.adesso.fischereiregister.registerservice.fishing_license_export.FishingLicenseExportService;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.mail.MailService;
import de.adesso.fischereiregister.registerservice.mail.MailTemplateResolutionServiceImpl;
import de.adesso.fischereiregister.registerservice.mail.enums.MailTemplate;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewService;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class IdentificationDocumentsMailServiceImpl implements IdentificationDocumentsMailService {

    private final MailTemplateResolutionServiceImpl mailTemplateResolutionService;
    private final FishingLicenseExportService fishingLicenseExportService;
    private final RegisterEntryViewService registerEntryViewService;
    private final MailService mailService;

    @Value("${spring.mail.from.address}")
    private String fromAddress;

    @Override
    public void createAndSendMail(
            UUID registerId,
            List<String> documentIds,
            String to,
            MailTemplate mailTemplate,
            UserDetails userDetails
    ) throws RestrictedDocumentDeliveryException {
        final FederalState userFederalState = FederalState.valueOf(userDetails.getFederalState());
        final RegisterEntry registerEntry = registerEntryViewService.findByRegisterId(registerId).getData();

        final List<IdentificationDocument> identificationDocuments = registerEntry.getIdentificationDocuments()
                .stream()
                .filter(document -> documentIds.contains(document.getDocumentId()))
                .toList();

        final List<String> restrictedDocumentIds = identificationDocuments.stream()
                .filter(IdentificationDocumentUtils::hasRestrictedInformation)
                .map(IdentificationDocument::getDocumentId)
                .toList();

        if (!restrictedDocumentIds.isEmpty()) {
            throw new RestrictedDocumentDeliveryException(
                    "Tried sending documents with restricted information via email. Document(s) with ID: " + restrictedDocumentIds
            );
        }

        final String mailSubject = mailTemplateResolutionService.getSubject(userFederalState, mailTemplate);
        final String mailText = mailTemplateResolutionService.getText(userFederalState, mailTemplate, registerEntry.getPerson());

        final List<RenderedContent> attachments = exportAttachments(identificationDocuments, registerEntry.getRegisterId());

        mailService.sendMail(
                to,
                fromAddress,
                mailSubject,
                mailText,
                attachments
        );
    }

    private List<RenderedContent> exportAttachments(List<IdentificationDocument> identificationDocuments, UUID registerEntryId) {

        final List<RenderedContent> fishingLicenseExportAttachments = identificationDocuments.stream()
                .filter(document -> document.getFishingLicense() != null)
                .map(document -> fishingLicenseExportService.exportFishingLicense(registerEntryId, document.getDocumentId()))
                .toList();

        final List<RenderedContent> fishingTaxExportAttachments = identificationDocuments.stream()
                .filter(document -> document.getTax() != null)
                .map(document -> fishingLicenseExportService.exportFishingTaxDocument(registerEntryId, document.getDocumentId()))
                .toList();

        final List<RenderedContent> attachments = new ArrayList<>();
        attachments.addAll(fishingTaxExportAttachments);
        attachments.addAll(fishingLicenseExportAttachments);
        return attachments;
    }
}
