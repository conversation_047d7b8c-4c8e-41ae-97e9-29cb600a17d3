package de.adesso.fischereiregister.registerservice.online_services.message;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.ports.ErrorsProtocolOnlineServicesPort;
import de.adesso.fischereiregister.message.service.OSInboxService;
import de.adesso.fischereiregister.message.service.model.OSMessage;
import de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRequestStatus;
import lombok.AllArgsConstructor;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
@AllArgsConstructor
@Slf4j
@Generated
public class OSFailureMessageServiceImpl implements OSFailureMessageService {

    private final OSInboxService osInboxService;
    private final ErrorsProtocolOnlineServicesPort protocolService;
    private final OSMessageTemplateResolutionService osMessageTemplateResolutionService;

    @Override
    public void handleFailure(OSRequestStatus osRequestStatus, String inboxReference, String licenseNumber, Person person, FederalState federalState, OSMessageTemplate osMessageTemplate) {
        final String subject = osMessageTemplateResolutionService.getSubject(federalState, osMessageTemplate);
        final String body = osMessageTemplateResolutionService.getText(federalState, osMessageTemplate, person);
        final String displayName = osMessageTemplateResolutionService.getDisplayName(federalState, osMessageTemplate);

        final OSMessage message = new OSMessage(subject, displayName, body);

        switch (osRequestStatus) {
            case PERSON_NOT_FOUND_CREATE_NEW_REGISTER_ENTRY ->
                    log.error("Request failed. No Register found with the person data given, and creating a new register entry failed.");
            case MULTIPLE_PERSONS_FOUND ->
                    log.error("Request failed. Cannot determine the register entry. Multiple persons found with the same data.");
            case PERSON_NOT_CHANGEABLE -> log.error("Request failed. Person data is not changeable via this action.");
            default -> log.error("Request failed. Unexpected Error");
        }

        protocolService.reportOSError(federalState.toString(), LocalDate.now().getYear());
        osInboxService.sendMessage(inboxReference, message);
    }

}
