package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "default", uses = {FishingLicenseMapper.class})
public interface DocumentMapper {
    DocumentMapper INSTANCE = Mappers.getMapper(DocumentMapper.class);

    org.openapitools.model.IdentificationDocument toDto(IdentificationDocument document);

    List<org.openapitools.model.IdentificationDocument> toDtos(List<IdentificationDocument> document);

    @Mapping(target = "fishingTaxId", source = "tax.taxId")
    @Mapping(target = "fishingLicenseId", source = "fishingLicense.number")
    org.openapitools.model.IdentificationDocumentFP toDtoFP(IdentificationDocument document);

    List<org.openapitools.model.IdentificationDocumentFP> toDtosFP(List<IdentificationDocument> documents);
}
