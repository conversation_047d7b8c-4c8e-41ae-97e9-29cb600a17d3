package de.adesso.fischereiregister.registerservice.register_entry_view;

import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegisterEntryMarkedForDeletionEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.Tax;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class RegisterEntryViewHandler {

    private final RegisterEntryViewService registerEntryViewService;

    @ResetHandler
    @Transactional
    public void onReset() {
        // When replay/reset is triggered, view has to be emptied
        registerEntryViewService.truncateView();
    }

    @EventHandler
    @Transactional
    public void on(RegularLicenseDigitizedEvent event) {
        registerEntryViewService.createRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerId())
                .person(event.person())
                .jurisdiction(event.jurisdiction())
                .fishingLicense(event.fishingLicense())
                .fees(event.fees())
                .taxes(event.taxes())
                .qualificationsProofs(event.qualificationsProofs())
                .identificationDocuments(event.identificationDocuments())
                .build());
    }

    @EventHandler
    @Transactional
    public void on(QualificationsProofCreatedEvent event) {
        registerEntryViewService.createRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerEntryId())
                .person(event.person())
                .qualificationsProofs(List.of(event.qualificationsProof()))
                .build());
    }

    @EventHandler
    @Transactional
    public void on(PersonCreatedEvent event) {

        final List<Tax> allTaxes = new ArrayList<>(event.taxes());
        allTaxes.addAll(event.payedTaxes());

        registerEntryViewService.createRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerId())
                .person(event.person())
                .taxes(allTaxes)
                .identificationDocuments(event.identificationDocuments())
                .build());
    }

    @EventHandler
    @Transactional
    public void on(FishingTaxPayedEvent event) {
        registerEntryViewService.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerId())
                .person(event.person())
                .taxes(event.taxes())
                .identificationDocuments(event.identificationDocuments())
                .build());
    }

    @EventHandler
    @Transactional
    public void on(PersonalDataChangedEvent event) {
        registerEntryViewService.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerId())
                .person(event.person())
                .taxes(event.taxes())
                .identificationDocuments(event.identificationDocuments())
                .build());
    }

    @EventHandler
    @Transactional
    public void on(JurisdictionMovedEvent event) {
        registerEntryViewService.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerId())
                .jurisdiction(event.newJurisdiction())
                .taxes(event.taxes())
                .identificationDocuments(event.identificationDocuments())
                .build());
    }

    @EventHandler
    @Transactional
    public void on(BannedEvent event) {
        registerEntryViewService.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerId())
                .ban(event.ban())
                .build());
    }


    @EventHandler
    @Transactional
    public void on(RegularLicenseCreatedEvent event) {
        registerEntryViewService.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerId())
                .jurisdiction(event.jurisdiction())
                .person(event.person())
                .fees(event.fees())
                .taxes(event.taxes())
                .fishingLicense(event.fishingLicense())
                .identificationDocuments(event.identificationDocuments())
                .build());
    }

    @EventHandler
    @Transactional
    public void on(LimitedLicenseApplicationCreatedEvent event) {
        registerEntryViewService.createOrUpdateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerEntryId())
                .fees(event.fees())
                .person(event.person())
                .limitedLicenseApplication(event.limitedLicenseApplication())
                .build()
        );
    }

    @EventHandler
    @Transactional
    public void on(LimitedLicenseApplicationRejectedEvent event) {
        registerEntryViewService.rejectLimitedLicenseApplication(event.registerEntryId(), event.limitedLicenseApplication().getId());
    }

    @EventHandler
    @Transactional
    public void on(LimitedLicenseCreatedEvent event) {
        registerEntryViewService.createOrUpdateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerId())
                .jurisdiction(event.jurisdiction())
                .person(event.person())
                .fees(event.fees())
                .taxes(event.taxes())
                .fishingLicense(event.fishingLicense())
                .identificationDocuments(event.identificationDocuments())
                .build());
    
        registerEntryViewService.acceptLimitedLicenseApplication(event.registerId());
    }

    @EventHandler
    @Transactional
    public void on(ReplacementCardOrderedEvent event) {
        registerEntryViewService.reorderCardAndUpdateRegisterEntry(
                event.registerId(),
                event.person(),
                event.fees(),
                event.taxes(),
                event.identificationDocuments(),
                event.fishingLicense().getNumber(),
                event.federalState()
        );
    }

    @EventHandler
    @Transactional
    public void on(UnbannedEvent event) {
        registerEntryViewService.deleteBan(
                event.registerEntryId()
        );
    }

    @EventHandler
    @Transactional
    public void on(VacationLicenseCreatedEvent event) {
        registerEntryViewService.createOrUpdateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerEntryId())
                .person(event.person())
                .fees(event.fees())
                .taxes(event.taxes())
                .identificationDocuments(event.identificationDocuments())
                .fishingLicense(event.fishingLicense())
                .build());
    }

    @EventHandler
    @Transactional
    public void on(LicenseExtendedEvent event) {
        registerEntryViewService.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerEntryId())
                .person(event.person())
                .fees(event.fees())
                .taxes(event.taxes())
                .identificationDocuments(event.identificationDocuments())
                .build());
        registerEntryViewService.extendFishingLicense(event.registerEntryId(), event.licenseNumber(), event.validityPeriod());
    }

    @EventHandler
    @Transactional
    public void on(RegisterEntryMarkedForDeletionEvent event) {
        registerEntryViewService.updateRegisterEntryView(RegisterEntryViewServiceParameters.builder()
                .registerEntryId(event.registerEntryId())
                .deletionReason(event.deletionFlag().getDeletionReason())
                .build());
    }
}
