package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;


@Mapper(componentModel = "default", uses = {AddressMapper.class})
public interface PersonMapper {

    PersonMapper INSTANCE = Mappers.getMapper(PersonMapper.class);

    @Mapping(target = "email", ignore = true)
    @Mapping(target = "birthdate", expression = "java(de.adesso.fischereiregister.core.model.Birthdate.parse(dto.getBirthdate()))")
    @Mapping(target = "title", qualifiedByName = "normalizeEmptyField")
    @Mapping(target = "nationality", qualifiedByName = "normalizeEmptyField")
    @Mapping(target = "address", ignore = true)
    @Mapping(target = "officeAddress", ignore = true)
    Person toPerson(org.openapitools.model.Person dto);

    @Mapping(target = "email", ignore = true)
    @Mapping(target = "birthdate", expression = "java(de.adesso.fischereiregister.core.model.Birthdate.parse(dto.getBirthdate()))")
    @Mapping(target = "title", qualifiedByName = "normalizeEmptyField")
    @Mapping(target = "nationality", qualifiedByName = "normalizeEmptyField")
    Person toPerson(org.openapitools.model.PersonWithAddress dto);

    @Mapping(target = "email", ignore = true)
    @Mapping(target = "birthdate", expression = "java(de.adesso.fischereiregister.core.model.Birthdate.parse(personOS.getBirthdate()))")
    @Mapping(target = "title", qualifiedByName = "normalizeEmptyField")
    @Mapping(target = "nationality", qualifiedByName = "normalizeEmptyField")
    Person toPerson(org.openapitools.model.PersonOS personOS);

    @Mapping(target = "nationality", ignore = true)
    @Mapping(target = "birthdate", expression = "java(de.adesso.fischereiregister.core.model.Birthdate.parse(personES.getBirthdate()))")
    Person toPerson(org.openapitools.model.PersonES personES);

    @Mapping(target = "email", ignore = true)
    @Mapping(target = "birthdate", expression = "java(de.adesso.fischereiregister.core.model.Birthdate.parse(personWithAddressOS.getBirthdate()))")
    @Mapping(target = "title", qualifiedByName = "normalizeEmptyField")
    @Mapping(target = "nationality", qualifiedByName = "normalizeEmptyField")
    Person toPersonFromOSWithAddressOS(org.openapitools.model.PersonWithAddressOS personWithAddressOS);

    @Mapping(target = "title", qualifiedByName = "normalizeEmptyField")
    @Mapping(target = "nationality", qualifiedByName = "normalizeEmptyField")
    org.openapitools.model.Person toDto(Person person);

    @Mapping(target = "title", qualifiedByName = "normalizeEmptyField")
    @Mapping(target = "nationality", qualifiedByName = "normalizeEmptyField")
    org.openapitools.model.PersonFP toDtoFP(Person person);

    default String map(Birthdate birthdate) {
        return birthdate == null ? null : birthdate.toString();
    }

    @Named("normalizeEmptyField")
    static String normalizeEmptyField(String field) {
        if (field == null || field.trim().isEmpty()) {
            return null;
        }

        return field;
    }
}
