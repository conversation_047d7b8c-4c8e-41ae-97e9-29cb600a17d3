package de.adesso.fischereiregister.registerservice.statistics;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.model.FederalStateAbbreviation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class StatisticsMetadataController implements api.StatisticsMetadataApi {

    private final StatisticsMetadataService statisticsMetadataService;

    @Override
    public ResponseEntity<List<String>> statisticsMetadataControllerGetCertificationIssuers(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Fetching available certification issuers for years: {} and federalState: {}", year, federalState);

            List<String> availableIssuers;

            if (year != null && !year.isEmpty() && federalState != null) {
                // Filter by both years and federal state
                availableIssuers = statisticsMetadataService.getAvailableCertificationIssuersByYearsAndFederalState(year, federalState.getValue());
            } else if (year != null && !year.isEmpty()) {
                // Filter by years only
                availableIssuers = statisticsMetadataService.getAvailableCertificationIssuersByYears(year);
            } else if (federalState != null) {
                // Filter by federal state only
                availableIssuers = statisticsMetadataService.getAvailableCertificationIssuersByFederalState(federalState.getValue());
            } else {
                // No filters
                availableIssuers = statisticsMetadataService.getAvailableCertificationIssuers();
            }

            log.info("Found {} available certification issuers", availableIssuers.size());
            return ResponseEntity.ok(availableIssuers);

        } catch (Exception e) {
            log.error("Error fetching available certification issuers: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available certification issuers", e);
        }
    }

    @Override
    public ResponseEntity<List<String>> statisticsMetadataControllerGetOffices(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Fetching available tax offices for years: {} and federalState: {}", year, federalState);

            List<String> availableOffices;

            if (year != null && !year.isEmpty() && federalState != null) {
                // Filter by both years and federal state
                availableOffices = statisticsMetadataService.getAvailableOfficesByYearsAndFederalState(year, federalState.getValue());
            } else if (year != null && !year.isEmpty()) {
                // Filter by years only
                availableOffices = statisticsMetadataService.getAvailableOfficesByYears(year);
            } else if (federalState != null) {
                // Filter by federal state only
                availableOffices = statisticsMetadataService.getAvailableOfficesByFederalState(federalState.getValue());
            } else {
                // No filters
                availableOffices = statisticsMetadataService.getAvailableOffices();
            }

            log.info("Found {} available tax offices", availableOffices.size());
            return ResponseEntity.ok(availableOffices);

        } catch (Exception e) {
            log.error("Error fetching available tax offices: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available tax offices", e);
        }
    }

    @Override
    public ResponseEntity<?> statisticsMetadataControllerGetYears() {
        List<Integer> availableYears;
        try {
            log.info("Fetching available years for statistics metadata");

            availableYears = statisticsMetadataService.getAvailableYears();

            log.info("Found {} available years", availableYears.size());
            return ResponseEntity.ok(availableYears);

        } catch (Exception e) {
            log.error("Error fetching available years: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available years", e);
        }
    }
}
