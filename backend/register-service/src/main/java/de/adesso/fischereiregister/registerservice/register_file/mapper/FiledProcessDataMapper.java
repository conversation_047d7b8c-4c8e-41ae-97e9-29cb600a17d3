package de.adesso.fischereiregister.registerservice.register_file.mapper;

import de.adesso.fischereiregister.registerservice.domain.mapper.BanMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.ConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.DocumentMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.FeeMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.FishingLicenseMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.LicenseTypeMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.QualificationsProofMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxMapper;
import de.adesso.fischereiregister.view.filed_process.FiledProcessData;
import org.mapstruct.Mapper;

@Mapper(uses = {
        PersonMapper.class,
        QualificationsProofMapper.class,
        TaxMapper.class,
        FeeMapper.class,
        BanMapper.class,
        ConsentInfoMapper.class,
        FishingLicenseMapper.class,
        LicenseTypeMapper.class,
        DocumentMapper.class
})
public interface FiledProcessDataMapper {

    org.openapitools.model.FiledProcessData toDto(FiledProcessData data);
}
