package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.type.DeletionReason;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface DeletionReasonMapper {
    DeletionReasonMapper INSTANCE = Mappers.getMapper(DeletionReasonMapper.class);

    org.openapitools.model.DeletionReason toDto(DeletionReason deletionReason);

    DeletionReason toDomain(org.openapitools.model.DeletionReason deletionReason);

}
