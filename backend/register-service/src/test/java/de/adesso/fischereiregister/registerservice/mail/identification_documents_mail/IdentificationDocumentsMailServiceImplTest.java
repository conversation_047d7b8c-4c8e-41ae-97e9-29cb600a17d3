package de.adesso.fischereiregister.registerservice.mail.identification_documents_mail;

import de.adesso.fischereiregister.core.exceptions.RestrictedDocumentDeliveryException;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.fishing_license_export.FishingLicenseExportService;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import de.adesso.fischereiregister.registerservice.mail.MailService;
import de.adesso.fischereiregister.registerservice.mail.MailTemplateResolutionServiceImpl;
import de.adesso.fischereiregister.registerservice.mail.enums.MailTemplate;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewService;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IdentificationDocumentsMailServiceImplTest {

    @Mock
    private TenantConfigurationService tenantConfigurationService;

    @Mock
    private MailTemplateResolutionServiceImpl mailTemplateResolutionService;

    @Mock
    private FishingLicenseExportService fishingLicenseExportService;

    @Mock
    private RegisterEntryViewService registerEntryViewService;

    @Mock
    private MailService mailService;

    @InjectMocks
    private IdentificationDocumentsMailServiceImpl identificationDocumentsMailServiceImpl;

    @Captor
    private ArgumentCaptor<String> toCaptor;

    @Captor
    private ArgumentCaptor<String> fromCaptor;

    @Captor
    private ArgumentCaptor<String> subjectCaptor;

    @Captor
    private ArgumentCaptor<String> textCaptor;

    @Captor
    private ArgumentCaptor<List<RenderedContent>> attachmentsCaptor;

    private static final UUID REGISTER_ID = UUID.randomUUID();
    private static final String FROM_ADDRESS = "<EMAIL>";
    private static final String TO_ADDRESS = "<EMAIL>";
    private static final FederalState FEDERAL_STATE = FederalState.SH;
    private static final String USER_FEDERAL_STATE = "SH";
    private static final String PERSON_FIRSTNAME = "Max";
    private static final String PERSON_LASTNAME = "Mustermann";
    private static final String PERSON_FULLNAME = "Max Mustermann";
    private static final String EXPECTED_SUBJECT = "Test Subject";
    private static final String EXPECTED_TEXT = "Hello " + PERSON_FULLNAME;
    private static final String DOCUMENT_ID = "03f4f136-2a3f-43e5-852a-99c0487095cb";
    private static final String RESTRICTED_DOCUMENT_ID = "restricted-doc-id";
    private static final RenderedContent EXPECTED_ATTACHMENT = new RenderedContent("file.pdf", RenderedContentType.PDF, new byte[0]);

    private UserDetails userDetails;
    private IdentificationDocument fishingLicenseDocument;
    private IdentificationDocument taxDocument;
    private IdentificationDocument restrictedDocument;
    private RegisterEntryView registerEntryView;

    @BeforeEach
    void setUp() {
        userDetails = new UserDetails("anyUserId", USER_FEDERAL_STATE, "anyOffice", "anyOfficeAddress", null, Collections.emptyList());

        Person person = new Person();
        person.setFirstname(PERSON_FIRSTNAME);
        person.setLastname(PERSON_LASTNAME);

        fishingLicenseDocument = new IdentificationDocument();
        fishingLicenseDocument.setDocumentId(DOCUMENT_ID);
        fishingLicenseDocument.setFishingLicense(new FishingLicense());

        taxDocument = new IdentificationDocument();
        taxDocument.setDocumentId(DOCUMENT_ID);
        taxDocument.setTax(new Tax());

        final FishingLicense limitedLicense = new FishingLicense();
        limitedLicense.setType(LicenseType.LIMITED);

        restrictedDocument = new IdentificationDocument();
        restrictedDocument.setDocumentId(RESTRICTED_DOCUMENT_ID);
        restrictedDocument.setFishingLicense(limitedLicense);

        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setPerson(person);
        registerEntry.setRegisterId(REGISTER_ID);
        registerEntry.getIdentificationDocuments().addAll(List.of(fishingLicenseDocument, taxDocument));

        registerEntryView = new RegisterEntryView();
        registerEntryView.setData(registerEntry);

        ReflectionTestUtils.setField(identificationDocumentsMailServiceImpl, "fromAddress", FROM_ADDRESS);
    }

    @Test
    @DisplayName("identificationDocumentsMailServiceImpl.createAndSendMail should process valid request and send email")
    void createAndSendMail_WithValidParameters_ShouldSendMail() {
        // Given
        when(registerEntryViewService.findByRegisterId(REGISTER_ID)).thenReturn(registerEntryView);

        when(mailTemplateResolutionService.getText(eq(FEDERAL_STATE), eq(MailTemplate.REGULAR_FISHING_LICENSE_CREATED),any())).thenReturn(EXPECTED_TEXT);
        when(mailTemplateResolutionService.getSubject(eq(FEDERAL_STATE), eq(MailTemplate.REGULAR_FISHING_LICENSE_CREATED))).thenReturn(EXPECTED_SUBJECT);

        when(fishingLicenseExportService.exportFishingLicense(eq(REGISTER_ID), eq(fishingLicenseDocument.getDocumentId()))).thenReturn(EXPECTED_ATTACHMENT);
        when(fishingLicenseExportService.exportFishingTaxDocument(eq(REGISTER_ID), eq(taxDocument.getDocumentId()))).thenReturn(EXPECTED_ATTACHMENT);

        // When
        identificationDocumentsMailServiceImpl.createAndSendMail(
                REGISTER_ID,
                List.of(DOCUMENT_ID),
                TO_ADDRESS,
                MailTemplate.REGULAR_FISHING_LICENSE_CREATED,
                userDetails
        );

        // Then
        verify(registerEntryViewService).findByRegisterId(REGISTER_ID);
        verify(mailService).sendMail(
                toCaptor.capture(),
                fromCaptor.capture(),
                subjectCaptor.capture(),
                textCaptor.capture(),
                attachmentsCaptor.capture()
        );

        assertEquals(TO_ADDRESS, toCaptor.getValue());
        assertEquals(FROM_ADDRESS, fromCaptor.getValue());
        assertEquals(EXPECTED_SUBJECT, subjectCaptor.getValue());
        assertEquals(EXPECTED_TEXT, textCaptor.getValue());
        assertEquals(EXPECTED_ATTACHMENT.getFullFilename(), attachmentsCaptor.getValue().getFirst().getFullFilename());
        assertEquals(EXPECTED_ATTACHMENT.content(), attachmentsCaptor.getValue().getFirst().content());
    }

    @Test
    @DisplayName("identificationDocumentsMailServiceImpl.createAndSendMail should throw exception when restricted document is included")
    void createAndSendMail_WithRestrictedDocument_ShouldThrowException() {
        // Given
        RegisterEntry registerEntryWithRestrictedDoc = new RegisterEntry();
        registerEntryWithRestrictedDoc.setPerson(new Person());
        registerEntryWithRestrictedDoc.setRegisterId(REGISTER_ID);
        registerEntryWithRestrictedDoc.getIdentificationDocuments().add(restrictedDocument);

        RegisterEntryView registerEntryViewWithRestrictedDoc = new RegisterEntryView();
        registerEntryViewWithRestrictedDoc.setData(registerEntryWithRestrictedDoc);

        when(registerEntryViewService.findByRegisterId(REGISTER_ID)).thenReturn(registerEntryViewWithRestrictedDoc);

        // When / Then
        RestrictedDocumentDeliveryException exception = assertThrows(RestrictedDocumentDeliveryException.class, () ->
                identificationDocumentsMailServiceImpl.createAndSendMail(
                        REGISTER_ID,
                        List.of(RESTRICTED_DOCUMENT_ID),
                        TO_ADDRESS,
                        MailTemplate.REGULAR_FISHING_LICENSE_CREATED,
                        userDetails
                ));

        assertEquals("Tried sending documents with restricted information via email. Document(s) with ID: [" + RESTRICTED_DOCUMENT_ID + "]",
                exception.getMessage());
    }

    @Test
    @DisplayName("identificationDocumentsMailServiceImpl.createAndSendMail should throw exception when any document in list is restricted")
    void createAndSendMail_WithMixedDocumentsIncludingRestricted_ShouldThrowException() {
        // Given
        RegisterEntry registerEntryWithMixedDocs = new RegisterEntry();
        registerEntryWithMixedDocs.setPerson(new Person());
        registerEntryWithMixedDocs.setRegisterId(REGISTER_ID);
        registerEntryWithMixedDocs.getIdentificationDocuments().addAll(List.of(fishingLicenseDocument, restrictedDocument));

        RegisterEntryView registerEntryViewWithMixedDocs = new RegisterEntryView();
        registerEntryViewWithMixedDocs.setData(registerEntryWithMixedDocs);

        when(registerEntryViewService.findByRegisterId(REGISTER_ID)).thenReturn(registerEntryViewWithMixedDocs);

        // When / Then
        RestrictedDocumentDeliveryException exception = assertThrows(RestrictedDocumentDeliveryException.class, () ->
                identificationDocumentsMailServiceImpl.createAndSendMail(
                        REGISTER_ID,
                        List.of(fishingLicenseDocument.getDocumentId(), RESTRICTED_DOCUMENT_ID),
                        TO_ADDRESS,
                        MailTemplate.REGULAR_FISHING_LICENSE_CREATED,
                        userDetails
                ));

        assertEquals("Tried sending documents with restricted information via email. Document(s) with ID: [" + RESTRICTED_DOCUMENT_ID + "]",
                exception.getMessage());
    }
}