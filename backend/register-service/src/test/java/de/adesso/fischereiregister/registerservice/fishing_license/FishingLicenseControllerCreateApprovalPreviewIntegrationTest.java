package de.adesso.fischereiregister.registerservice.fishing_license;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.Address;
import org.openapitools.model.CreateApprovalPreviewForLimitedLicenseRequest;
import org.openapitools.model.LimitedLicenseApproval;
import org.openapitools.model.PersonWithAddress;
import org.openapitools.model.SigningEmployee;
import org.openapitools.model.ValidityPeriod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.containsString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class FishingLicenseControllerCreateApprovalPreviewIntegrationTest {

    @Autowired
    private MockMvc mvc;

    private CreateApprovalPreviewForLimitedLicenseRequest createValidRequest() {
        CreateApprovalPreviewForLimitedLicenseRequest request = new CreateApprovalPreviewForLimitedLicenseRequest();

        final Address address = new Address();
        address.setDetail("Apartment 101");
        address.setStreet("Main Street");
        address.setStreetNumber("123");
        address.setPostcode("12345");
        address.setCity("Springfield");

        // Create person
        final PersonWithAddress person = new PersonWithAddress();
        person.setFirstname("firstname");
        person.setLastname("Mustermann");
        person.setBirthname("Mustermann");
        person.setBirthdate("01.01.1990");
        person.setBirthplace("Berlin");
        person.setNationality("deutsch");
        person.setAddress(address);

        // Create validity period
        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        validityPeriod.setValidTo(LocalDate.now().plusYears(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        // Create signing employee
        SigningEmployee signingEmployee = new SigningEmployee();
        signingEmployee.setName("Max Mustermann");
        signingEmployee.setEmail("<EMAIL>");
        signingEmployee.setPhone("+49123456789");
        signingEmployee.setPersonalSign("JD-001");

        // Create limited license approval
        LimitedLicenseApproval limitedLicenseApproval = new LimitedLicenseApproval();
        limitedLicenseApproval.setSigningEmployee(signingEmployee);
        limitedLicenseApproval.setCreatedAt(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        limitedLicenseApproval.setFileNumber("FILE-2024-001");
        limitedLicenseApproval.setCashRegisterSign("CASH-001");
        limitedLicenseApproval.setJustificationForLimitedDurationNotice("Medical condition requires limited duration");

        request.setPerson(person);
        request.setValidityPeriod(validityPeriod);
        request.setLimitedLicenseApproval(limitedLicenseApproval);

        return request;
    }

    @Test
    @DisplayName("""
            POST /api/fishing-licenses/limited:create-approval-preview
            Verify that the create approval preview endpoint returns a 200 OK status code and PDF content.
            """)
    void testCreateApprovalPreviewForLimitedLicense_Success() throws Exception {
        // given
        final CreateApprovalPreviewForLimitedLicenseRequest request = createValidRequest();

        // when & then
        mvc.perform(MockMvcRequestBuilders
                        .post("/register-entries/fishing-licenses/limited/approval-preview")
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_PDF))
                .andExpect(header().string("Content-Disposition", 
                    containsString("attachment; filename=")));
    }
}
