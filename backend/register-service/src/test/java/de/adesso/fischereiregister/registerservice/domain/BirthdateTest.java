package de.adesso.fischereiregister.registerservice.domain;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.utils.jackson.BirthdateDeserializer;
import de.adesso.fischereiregister.utils.jackson.BirthdateSerializer;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;


public class BirthdateTest {

    @DisplayName("Birthdate.parse should throw an exception for null")
    @Test
    public void parseNullTest() {
        //GIVEN
        //WHEN
        assertThrows(IllegalArgumentException.class, () -> Birthdate.parse(null));
        //THEN
    }

    @DisplayName("Birthdate.parse should throw an exception for an empty String")
    @Test
    public void parseEmptyTest() {
        //GIVEN
        //WHEN
        assertThrows(IllegalArgumentException.class, () -> Birthdate.parse(""));
        //THEN
    }

    @DisplayName("Birthdate.parse should throw an exception for an absolutely impossible date (Day > 31)")
    @Test
    public void parseImpossibleDayValueTest() {
        //GIVEN
        //WHEN
        assertThrows(IllegalArgumentException.class, () -> Birthdate.parse("32.02.2000"));
        //THEN
    }

    @DisplayName("Birthdate.parse should throw an exception for an absolutely impossible date (Month > 12)")
    @Test
    public void parseImpossibleMonthValueTest() {
        //GIVEN
        //WHEN
        assertThrows(IllegalArgumentException.class, () -> Birthdate.parse("01.13.2000"));
        //THEN
    }

    @DisplayName("Birthdate.parse should throw an exception for an impossible date 29th of Feb. in an odd year")
    @Test
    public void parseImpossibleLeapDayValueTest() {
        //GIVEN
        //WHEN
        assertThrows(IllegalArgumentException.class, () -> Birthdate.parse("29.02.2001"));
        //THEN
    }


    @DisplayName("Birthdate.parse should succeed for a typical valid date")
    @Test
    public void parseValidTest() {
        //GIVEN
        //WHEN
        Birthdate.parse("01.01.2000");
        //THEN
    }

    @DisplayName("Birthdate.parse should succeed for a partially known date, when everything but the day of month is known (00.01.2001)")
    @Test
    public void parseUnknownDayOfMonthValidTest() {
        //GIVEN
        //WHEN
        Birthdate.parse("00.01.2000");
        //THEN
    }

    @DisplayName("Birthdate.parse should fail for a partially known date, when month is above 12 (00.13.2001)")
    @Test
    public void parseUnknownDayOfMonthInValidTest() {
        //GIVEN
        //WHEN
        assertThrows(IllegalArgumentException.class, () -> Birthdate.parse("00.13.2001"));
        //THEN

    }

    @DisplayName("Birthdate.parse should succeed for a partially known date, when only the year is known (00.00.2001)")
    @Test
    public void parseUnknownDayOfMonthUnknownMonthValidTest() {
        //GIVEN
        //WHEN
        Birthdate.parse("00.00.2000");
        //THEN
    }


    @DisplayName("Birthdate.toString() has to return a valid String")
    @Test
    public void toStringTest() {
        //GIVEN
        final String startingString = "00.00.2000";
        final Birthdate birthdate = Birthdate.parse(startingString);
        //WHEN
        final String birthdateAsString = birthdate.toString();
        //THEN
        assertEquals(startingString, birthdateAsString);

    }

    @DisplayName("Birthdate has to be serializable to and from json")
    @Test
    public void serializeTest() throws JsonProcessingException {
        //GIVEN
        final Birthdate birthdate = Birthdate.parse("00.00.2000");
        final ObjectMapper objectMapper = new ObjectMapper();
        final SimpleModule module = new SimpleModule()
                .addSerializer(new BirthdateSerializer())
                .addDeserializer(Birthdate.class, new BirthdateDeserializer());

        objectMapper.registerModule(module);

        //WHEN
        final String birthdateAsString = objectMapper.writeValueAsString(birthdate);


        final Birthdate birthdateFromString = objectMapper.readValue(birthdateAsString, Birthdate.class);
        //THEN
        assertNotNull(birthdateFromString);

    }
}
