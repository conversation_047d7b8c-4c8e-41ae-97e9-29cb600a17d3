package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.view.ban.services.BanViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BansStatisticsServiceImplTest {

    @Mock
    private BanViewService banViewService;

    private BansStatisticsServiceImpl bansStatisticsService;

    @BeforeEach
    void setUp() {
        bansStatisticsService = new BansStatisticsServiceImpl(banViewService);
    }

    @Test
    @DisplayName("getStatisticsByFederalStateAndYears should return correct statistics for given federal state and years")
    void getStatisticsByFederalStateAndYears_ShouldReturnCorrectStatistics() {
        // given
        String federalState = "BY";
        List<Integer> years = List.of(2023, 2024);

        // Mock BanViewService responses for 2023
        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2023)).thenReturn(5);

        // Mock BanViewService responses for 2024
        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2024)).thenReturn(8);

        // when
        List<BansStatistics> result = bansStatisticsService.getStatisticsByFederalStateAndYears(federalState, years);

        // then
        assertThat(result).hasSize(2);

        // Check 2024 statistics (should be first due to descending order)
        BansStatistics stats2024 = result.get(0);
        assertThat(stats2024.year()).isEqualTo(2024);
        assertThat(stats2024.data().issued()).isEqualTo(8);

        // Check 2023 statistics
        BansStatistics stats2023 = result.get(1);
        assertThat(stats2023.year()).isEqualTo(2023);
        assertThat(stats2023.data().issued()).isEqualTo(5);
    }

    @Test
    @DisplayName("getStatisticsByYears should return correct statistics for given years")
    void getStatisticsByYears_ShouldReturnCorrectStatistics() {
        // given
        List<Integer> years = List.of(2023, 2024);

        // Mock BanViewService responses for 2023
        when(banViewService.getIssuedAmountByYear(2023)).thenReturn(10);

        // Mock BanViewService responses for 2024
        when(banViewService.getIssuedAmountByYear(2024)).thenReturn(15);

        // when
        List<BansStatistics> result = bansStatisticsService.getStatisticsByYears(years);

        // then
        assertThat(result).hasSize(2);

        // Check 2024 statistics (should be first due to descending order)
        BansStatistics stats2024 = result.get(0);
        assertThat(stats2024.year()).isEqualTo(2024);
        assertThat(stats2024.data().issued()).isEqualTo(15);

        // Check 2023 statistics
        BansStatistics stats2023 = result.get(1);
        assertThat(stats2023.year()).isEqualTo(2023);
        assertThat(stats2023.data().issued()).isEqualTo(10);
    }

    @Test
    @DisplayName("getStatisticsByYears should handle empty years list")
    void getStatisticsByYears_ShouldHandleEmptyYearsList() {
        // given
        List<Integer> years = List.of();

        // when
        List<BansStatistics> result = bansStatisticsService.getStatisticsByYears(years);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("getAvailableYears should return available years from BanViewService")
    void getAvailableYears_ShouldReturnAvailableYears() {
        // given
        List<Integer> expectedYears = List.of(2024, 2023, 2022);
        when(banViewService.getAvailableYears()).thenReturn(expectedYears);

        // when
        List<Integer> result = bansStatisticsService.getAvailableYears();

        // then
        assertThat(result).isEqualTo(expectedYears);
    }

    @Test
    @DisplayName("getStatisticsByFederalStateAndYears should handle exceptions from BanViewService")
    void getStatisticsByFederalStateAndYears_ShouldHandleExceptions() {
        // given
        String federalState = "BY";
        List<Integer> years = List.of(2023);

        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2023))
                .thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> bansStatisticsService.getStatisticsByFederalStateAndYears(federalState, years))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to fetch ban statistics");
    }

    @Test
    @DisplayName("getStatisticsByYears should handle exceptions from BanViewService")
    void getStatisticsByYears_ShouldHandleExceptions() {
        // given
        List<Integer> years = List.of(2023);

        when(banViewService.getIssuedAmountByYear(2023))
                .thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> bansStatisticsService.getStatisticsByYears(years))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to fetch ban statistics");
    }

    @Test
    @DisplayName("getAvailableYears should handle exceptions from BanViewService")
    void getAvailableYears_ShouldHandleExceptions() {
        // given
        when(banViewService.getAvailableYears())
                .thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> bansStatisticsService.getAvailableYears())
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to fetch available years");
    }

    @Test
    @DisplayName("Results should be sorted by year in descending order")
    void results_ShouldBeSortedByYearDescending() {
        // given
        String federalState = "BY";
        List<Integer> years = List.of(2022, 2024, 2023); // Unsorted input

        // Mock responses for all years
        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2022)).thenReturn(1);

        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2023)).thenReturn(2);

        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2024)).thenReturn(3);
        // when
        List<BansStatistics> result = bansStatisticsService.getStatisticsByFederalStateAndYears(federalState, years);

        // then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).year()).isEqualTo(2024); // First should be highest year
        assertThat(result.get(1).year()).isEqualTo(2023);
        assertThat(result.get(2).year()).isEqualTo(2022); // Last should be lowest year
    }
}
