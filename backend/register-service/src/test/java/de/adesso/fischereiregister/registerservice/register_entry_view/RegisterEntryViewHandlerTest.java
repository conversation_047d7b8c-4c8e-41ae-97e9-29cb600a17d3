package de.adesso.fischereiregister.registerservice.register_entry_view;


import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class RegisterEntryViewHandlerTest {


    @Mock
    RegisterEntryViewService service;

    @InjectMocks
    private RegisterEntryViewHandler eventHandler;

    @Test
    public void testOnPayFishingTaxEvent() {

        final UUID registerId = DomainTestData.registerId;
        final Person person = DomainTestData.createPerson();
        final TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();
        final List<Tax> taxes = DomainTestData.createAnalogTaxesWithOneTax();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();


        final FishingTaxPayedEvent mockEvent = new FishingTaxPayedEvent(
                registerId,
                consentInfo,
                person,
                taxes,
                null,
                identificationDocuments,
                null,
                null,
                null,
                null,
                SubmissionType.ANALOG);

        eventHandler.on(mockEvent);

        // Assert: Verify that the service method was called with the correct parameter
        ArgumentCaptor<RegisterEntryViewServiceParameters> argumentCaptor = ArgumentCaptor.forClass(RegisterEntryViewServiceParameters.class);
        verify(service, times(1)).updateRegisterEntryView(argumentCaptor.capture());

        RegisterEntryViewServiceParameters parameters = argumentCaptor.getValue();
        assertEquals(registerId, parameters.getRegisterEntryId());
        assertEquals(person, parameters.getPerson());
        assertEquals(taxes, parameters.getTaxes());
        assertEquals(identificationDocuments, parameters.getIdentificationDocuments());
    }

    @Test
    @DisplayName("RegisterEntryViewHandler.on() should correctly handle a PersonalDataChangedEvent")
    public void testOnPersonalDataChangedEvent() {

        // Act: Call the event handler method
        final UUID registerId = DomainTestData.registerId;
        final Person person = DomainTestData.createPerson();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();
        final List<Tax> taxes = DomainTestData.createAnalogTaxesWithOneTax();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();


        final PersonalDataChangedEvent mockEvent = new PersonalDataChangedEvent(registerId, person, taxes, consentInfo, null, identificationDocuments, null);

        eventHandler.on(mockEvent);

        // Assert: Verify that the service method was called with the correct parameter
        ArgumentCaptor<RegisterEntryViewServiceParameters> argumentCaptor = ArgumentCaptor.forClass(RegisterEntryViewServiceParameters.class);
        verify(service, times(1)).updateRegisterEntryView(argumentCaptor.capture());

        RegisterEntryViewServiceParameters parameters = argumentCaptor.getValue();
        assertEquals(registerId, parameters.getRegisterEntryId());
        assertEquals(person, parameters.getPerson());
        assertEquals(taxes, parameters.getTaxes());
        assertEquals(identificationDocuments, parameters.getIdentificationDocuments());
    }

    @Test
    @DisplayName("RegisterEntryViewHandler.on() should correctly handle a JurisdictionMovedEvent")
    public void testOnJurisdictionMovedEvent() {

        // Act: Call the event handler method
        final UUID registerId = DomainTestData.registerId;
        final Jurisdiction jurisdiction = DomainTestData.createJurisdiction();
        final JurisdictionConsentInfo consentInfo = TestDataUtil.createJurisdictionConsentInfo();
        final List<Tax> taxes = DomainTestData.createAnalogTaxesWithOneTax();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        final String salt = "salt";
        final String office = "office";

        JurisdictionMovedEvent mockEvent = new JurisdictionMovedEvent(registerId, null, jurisdiction, consentInfo, null, taxes, salt, office, identificationDocuments, SubmissionType.ANALOG);

        eventHandler.on(mockEvent);

        // Assert: Verify that the service method was called with the correct parameter
        ArgumentCaptor<RegisterEntryViewServiceParameters> argumentCaptor = ArgumentCaptor.forClass(RegisterEntryViewServiceParameters.class);
        verify(service, times(1)).updateRegisterEntryView(argumentCaptor.capture());

        RegisterEntryViewServiceParameters parameters = argumentCaptor.getValue();
        assertEquals(registerId, parameters.getRegisterEntryId());
        assertEquals(jurisdiction, parameters.getJurisdiction());
        assertEquals(taxes, parameters.getTaxes());
        assertEquals(identificationDocuments, parameters.getIdentificationDocuments());
    }

    @Test
    @DisplayName("RegisterEntryViewHandler.on() should correctly handle a FishingLicenseCreatedEvent")
    public void testOnFishingLicenseCreatedEvent() {

        // Act: Call the event handler method
        final UUID registerId = DomainTestData.registerId;
        final Person person = DomainTestData.createPerson();

        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();
        final List<Tax> taxes = DomainTestData.createAnalogTaxesWithOneTax();
        final List<Fee> fees = DomainTestData.createAnalogFeesWithOneFee();
        final FishingLicense fishingLicense = TestDataUtil.createFishingLicense();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        final String issuedByAddress = "issuedByAddress";
        final String issuedByOffice = "issuedByOffice";
        final Jurisdiction jurisdiction = DomainTestData.createJurisdiction();


        final RegularLicenseCreatedEvent mockEvent = new RegularLicenseCreatedEvent(
                registerId,
                "salt",
                consentInfo,
                person,
                fees,
                taxes,
                fishingLicense,
                identificationDocuments,
                jurisdiction,
                issuedByOffice,
                issuedByAddress,
                null,
                null,
                null,
                SubmissionType.ANALOG
        );

        eventHandler.on(mockEvent);

        // Assert: Verify that the service method was called with the correct parameter
        ArgumentCaptor<RegisterEntryViewServiceParameters> argumentCaptor = ArgumentCaptor.forClass(RegisterEntryViewServiceParameters.class);
        verify(service, times(1)).updateRegisterEntryView(argumentCaptor.capture());

        RegisterEntryViewServiceParameters parameters = argumentCaptor.getValue();
        assertEquals(registerId, parameters.getRegisterEntryId());
        assertEquals(jurisdiction, parameters.getJurisdiction());
        assertEquals(person, parameters.getPerson());
        assertEquals(taxes, parameters.getTaxes());
        assertEquals(fishingLicense, parameters.getFishingLicense());
        assertEquals(fees, parameters.getFees());
        assertEquals(identificationDocuments, parameters.getIdentificationDocuments());
    }


    @Test
    @DisplayName("RegisterEntryViewHandler.on() should correctly handle a QualificationsProofCreatedEvent")
    public void testOnQualificationsProofCreatedEvent() {
        //GIVEN
        final QualificationsProof proof = new QualificationsProof();
        final Person person = DomainTestData.createPerson();
        final UUID registerEntryId = UUID.randomUUID();
        final QualificationsProofCreatedEvent event = new QualificationsProofCreatedEvent(registerEntryId,
                proof,
                person
        );

        //WHEN
        eventHandler.on(event);
        //THEN
        ArgumentCaptor<RegisterEntryViewServiceParameters> argumentCaptor = ArgumentCaptor.forClass(RegisterEntryViewServiceParameters.class);
        verify(service, times(1)).createRegisterEntryView(argumentCaptor.capture());

        RegisterEntryViewServiceParameters parameters = argumentCaptor.getValue();
        assertEquals(registerEntryId, parameters.getRegisterEntryId());
        assertEquals(person, parameters.getPerson());
        assertEquals(1, parameters.getQualificationsProofs().size());
        assertEquals(proof, parameters.getQualificationsProofs().getFirst());
    }


    @Test
    @DisplayName("RegisterEntryViewHandler.on() should correctly handle a PersonCreatedEvent")
    public void testPersonCreatedEvent() {
        //GIVEN
        final Person person = DomainTestData.createPerson();
        final UUID registerEntryId = UUID.randomUUID();
        final TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        final PersonCreatedEvent event = new PersonCreatedEvent(registerEntryId,
                person,
                List.of(),
                List.of(),
                null,
                identificationDocuments,
                consentInfo,
                null,
                null,
                null,
                null,
                null,
                SubmissionType.ANALOG
        );

        //WHEN
        eventHandler.on(event);
        //THEN
        ArgumentCaptor<RegisterEntryViewServiceParameters> argumentCaptor = ArgumentCaptor.forClass(RegisterEntryViewServiceParameters.class);
        verify(service, times(1)).createRegisterEntryView(argumentCaptor.capture());

        RegisterEntryViewServiceParameters parameters = argumentCaptor.getValue();
        assertEquals(registerEntryId, parameters.getRegisterEntryId());
        assertEquals(person, parameters.getPerson());
        assertEquals(identificationDocuments, parameters.getIdentificationDocuments());
    }

    @Test
    @DisplayName("RegisterEntryViewService.on(ReplacementCardOrderedEvent) should make an call to the registerEntryViewService")
    public void testOnReplacementCardOrderedEvent() {
        //GIVEN

        final UUID registerId = DomainTestData.registerId;
        final Person person = DomainTestData.createPerson();
        final FishingLicense fishingLicense = TestDataUtil.createFishingLicense();
        final List<Fee> fees = List.of(new Fee());
        final List<Tax> taxes = List.of(DomainTestData.createAnalogTax());
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();
        final FederalState federalState = FederalState.BE;

        final ReplacementCardOrderedEvent event = new ReplacementCardOrderedEvent(registerId,
                fishingLicense,
                person,
                identificationDocuments,
                null,
                federalState,
                null,
                null,
                fees,
                taxes,
                consentInfo,
                null,
                null,
                null,
                SubmissionType.ANALOG
        );
        //WHEN
        eventHandler.on(event);
        //THEN
        verify(service, times(1)).reorderCardAndUpdateRegisterEntry(
                registerId,
                person,
                fees,
                taxes,
                identificationDocuments,
                fishingLicense.getNumber(),
                federalState
        );

    }


    @Test
    @DisplayName("Tests that PermanentlyBannedEvent is processed: RegisterEntryViewHandler.on(PermanentlyBannedEvent) should make an call to the RegisterEntryViewService.updateRegisterEntryView")
    public void testPermanentlyBannedEvent() {
        //GIVEN

        final UUID registerId = DomainTestData.registerId;
        final UUID banId = TestDataUtil.banId;
        final Jurisdiction jurisdiction = DomainTestData.createJurisdiction();

        Ban ban = new Ban();
        ban.setBanId(banId);
        ban.setFileNumber("");
        ban.setReportedBy("");
        ban.setFrom(LocalDate.now());
        ban.setTo(LocalDate.now().plusYears(60));
        final BannedEvent event = new BannedEvent(registerId, ban, jurisdiction, "office");
        //WHEN
        eventHandler.on(event);

        //THEN
        ArgumentCaptor<RegisterEntryViewServiceParameters> argumentCaptor = ArgumentCaptor.forClass(RegisterEntryViewServiceParameters.class);
        verify(service, times(1)).updateRegisterEntryView(argumentCaptor.capture());

        RegisterEntryViewServiceParameters parameters = argumentCaptor.getValue();
        assertEquals(registerId, parameters.getRegisterEntryId());
        assertEquals(ban, parameters.getBan());
    }

    @Test
    @DisplayName("Tests that BanDeletedEvent is handled: RegisterEntryViewHandler.on(BanDeletedEvent) should make an call to the RegisterEntryViewService.deleteBan")
    public void testOnDeleteBan() {
        //GIVEN

        final UUID registerId = DomainTestData.registerId;
        final Jurisdiction jurisdiction = DomainTestData.createJurisdiction();

        final UnbannedEvent event = new UnbannedEvent(registerId, jurisdiction);

        //WHEN
        eventHandler.on(event);

        //THEN
        verify(service, times(1)).deleteBan(
                registerId
        );

    }

    @Test
    @DisplayName("Tests that LimitedLicenseApplicationCreatedEvent is handled: RegisterEntryViewHandler.on(LimitedLicenseApplicationCreatedEvent) should make a call to the RegisterEntryViewService.createOrUpdate")
    public void testOnLimitedLicenseApplicationCreated() {
        // GIVEN
        final UUID registerEntryId = DomainTestData.registerId;
        final Fee fee = DomainTestData.createDigitalFee();
        final Person person = DomainTestData.createPersonWithAddress();
        final LimitedLicenseApplication limitedLicenseApplication = DomainTestData.createLimitedLicenseApplication();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();
        final String inboxReference = "inboxReference";
        final String serviceAccountId = "serviceAccountId";

        final LimitedLicenseApplicationCreatedEvent event = new LimitedLicenseApplicationCreatedEvent(
                registerEntryId,
                limitedLicenseApplication,
                person,
                List.of(fee),
                consentInfo,
                inboxReference,
                serviceAccountId
        );

        // WHEN
        eventHandler.on(event);

        // THEN
        verify(service).createOrUpdateRegisterEntryView(ArgumentMatchers.assertArg(arg -> {
            assertEquals(registerEntryId, arg.getRegisterEntryId());
            assertEquals(limitedLicenseApplication, arg.getLimitedLicenseApplication());
            assertEquals(person, arg.getPerson());
            assertEquals(List.of(fee), arg.getFees());
        }));
    }

    @Test
    @DisplayName("Tests whether LimitedLicenseApplicationRejectedEvent is handled correctly.")
    public void testOnLimitedLicenseApplicationRejected() {
        // GIVEN
        final UUID registerEntryId = DomainTestData.registerId;
        final LimitedLicenseApplication limitedLicenseApplication = DomainTestData.createLimitedLicenseApplication();

        final LimitedLicenseApplicationRejectedEvent event = new LimitedLicenseApplicationRejectedEvent(
                registerEntryId,
                limitedLicenseApplication,
                DomainTestData.createPersonWithAddress(),
                "issuedByOffice",
                "inboxReference"
        );

        // WHEN
        eventHandler.on(event);

        // THEN
        verify(service).rejectLimitedLicenseApplication(eq(registerEntryId), eq(limitedLicenseApplication.getId()));
    }

    @Test
    public void testVacationCreation() {

        //GIVEN
        VacationLicenseCreatedEvent event = mock(VacationLicenseCreatedEvent.class);

        //WHEN
        eventHandler.on(event);

        //THEN
        verify(service, times(1)).createOrUpdateRegisterEntryView(
                any()
        );
    }

    @Test
    public void testLimitedLicenseCreation() {

        //GIVEN
        LimitedLicenseCreatedEvent event = mock(LimitedLicenseCreatedEvent.class);

        //WHEN
        eventHandler.on(event);

        //THEN
        verify(service, times(1)).createOrUpdateRegisterEntryView(
                any()
        );
        verify(service, times(1)).acceptLimitedLicenseApplication(any());
    }

}
