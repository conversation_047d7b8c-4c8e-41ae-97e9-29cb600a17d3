package de.adesso.fischereiregister.registerservice.identification_document;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewHandler;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.openapitools.model.IdentificationDocumentsMailTemplateType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessagePreparator;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class SendMailIdentificationDocumentReadIntegrationTest {

    private static final String PATH = "/register-entries/{registerEntryId}/identification-documents:send-mail";

    private static final String PARAM_DOCUMENT_ID = "documentId";
    private static final String PARAM_EMAIL_ADDRESS = "emailAddress";
    private static final String PARAM_TEMPLATE_TYPE = "templateType";

    @Autowired
    private RegisterEntryViewHandler registerEntryViewHandler;

    @Autowired
    private IdentificationDocumentViewHandler identificationDocumentViewHandler;

    @MockitoSpyBean
    JavaMailSender javaMailSender;

    @Autowired
    private MockMvc mvc;

    private UUID registerEntryId;

    private IdentificationDocument taxDocument;

    private IdentificationDocument limitedLicenseDocument;


    @BeforeAll
    void setUp() {
        registerEntryId = UUID.randomUUID();

        Tax tax = DomainTestData.createAnalogTax();

        taxDocument = new IdentificationDocument();
        taxDocument.setType(IdentificationDocumentType.PDF);
        taxDocument.setDocumentId(UUID.randomUUID().toString());
        taxDocument.setTax(tax);

        FishingLicense limitedLicense = DomainTestData.createLicense();
        limitedLicense.setType(LicenseType.LIMITED);

        limitedLicenseDocument = new IdentificationDocument();
        limitedLicenseDocument.setType(IdentificationDocumentType.PDF);
        limitedLicenseDocument.setDocumentId(UUID.randomUUID().toString());
        limitedLicenseDocument.setFishingLicense(limitedLicense);

        // Create events and call handler directly
        RegularLicenseDigitizedEvent regularLicenseDigitizedEvent = createRegularLicenseDigitizedEvent(registerEntryId, List.of(taxDocument));
        LimitedLicenseCreatedEvent limitedLicenseEvent = createLimitedLicenseEvent(registerEntryId, List.of(limitedLicenseDocument));

        registerEntryViewHandler.on(regularLicenseDigitizedEvent);
        registerEntryViewHandler.on(limitedLicenseEvent);

        identificationDocumentViewHandler.on(regularLicenseDigitizedEvent);
        identificationDocumentViewHandler.on(limitedLicenseEvent);

        Mockito.doNothing()
                .when(javaMailSender)
                .send(any(MimeMessagePreparator.class));

    }

    private RegularLicenseDigitizedEvent createRegularLicenseDigitizedEvent(UUID registerEntryId, List<IdentificationDocument> identificationDocuments) {
        // Create Person
        Person person = DomainTestData.createPerson();

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");

        // Create RegularLicenseDigitizedEvent
        return new RegularLicenseDigitizedEvent(
                registerEntryId,
                "salt",
                person,
                jurisdiction,
                new FishingLicense(),
                List.of(), // fees
                List.of(), // taxes
                List.of(), // qualificationsProofs
                identificationDocuments, // identificationDocuments
                null, // consentInfo
                "Test Office", // issuedByOffice
                "Test Address" // issuedByAddress
        );
    }

    private LimitedLicenseCreatedEvent createLimitedLicenseEvent(UUID registerEntryId, List<IdentificationDocument> identificationDocuments) {
        // Create Person
        Person person = DomainTestData.createPerson();

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");

        // Create RegularLicenseCreatedEvent
        return new LimitedLicenseCreatedEvent(
                registerEntryId,
                "salt",
                null, // consentInfo
                person, // person
                List.of(), // fees
                List.of(), // taxes
                new FishingLicense(),
                identificationDocuments, // identificationDocuments
                jurisdiction,
                "testOffice", // issuedByOffice
                "Test Address", // issuedByAddress
                null, // inboxReference
                null, // serviceAccountId
                null, // transactionId
                SubmissionType.ANALOG
        );
    }


    @Test
    @DisplayName("""
            POST /register-entries/{registerEntryId}/identification-documents:send-mail
            Verify that sending a mail for valid identification documents works.
            """)
    void callGetBansStatisticsSuccessful() throws Exception {
        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId.toString());

        mvc.perform(MockMvcRequestBuilders.post(path)
                        .param(PARAM_DOCUMENT_ID, taxDocument.getDocumentId())
                        .param(PARAM_EMAIL_ADDRESS, "<EMAIL>")
                        .param(PARAM_TEMPLATE_TYPE, IdentificationDocumentsMailTemplateType.FISHING_TAX_INFO.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(javaMailSender).send(any(MimeMessagePreparator.class));
    }

    @Test
    @DisplayName("""
            POST /register-entries/{registerEntryId}/identification-documents:send-mail
            Verify that sending a mail for a limited license document fails.
            """)
    void callGetBansStatisticsFails() throws Exception {
        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId.toString());

        mvc.perform(MockMvcRequestBuilders.post(path)
                        .param(PARAM_DOCUMENT_ID, limitedLicenseDocument.getDocumentId())
                        .param(PARAM_EMAIL_ADDRESS, "<EMAIL>")
                        .param(PARAM_TEMPLATE_TYPE, IdentificationDocumentsMailTemplateType.LIMITED_FISHING_LICENSE_INFO.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        verify(javaMailSender, never()).send(any(MimeMessagePreparator.class));
    }

}
