package de.adesso.fischereiregister.registerservice.online_services;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.commands.OSCreateLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.openapitools.model.ConsentInfo;
import org.openapitools.model.CreateLimitedLicenseRequestOS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
// Use PER_METHOD, since double document ids in test-data csv lead to issues when event handlers are called
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class LimitedLicenseApplicationIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @Autowired
    private CommandGateway commandGateway;

    @BeforeEach
    void setUp() {
        // This method can be used to set up any common test data or configurations
        // before each test runs. Currently, it is empty.
    }

    @Test
    public void testOSApplicationSuccessful() throws Exception {
        org.openapitools.model.CreateLimitedLicenseRequestOS request = new CreateLimitedLicenseRequestOS();

        request.setConsentInfo(new ConsentInfo());

        final String requestJson = getOSApplicationBody();

        final ResultActions result = mvc.perform(MockMvcRequestBuilders
                .put("http://localhost:8080/os/v1/fishing-license/limited")
                .content(requestJson)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
    }

    private static String getOSApplicationBody() {
        return """
                {
                    "fee": {
                        "federalState": "SH",
                        "paymentInfo": {
                            "amount": 50.0,
                            "type": "ONLINE"
                        }
                    },
                    "federalState": "SH",
                    "licenseNumber": null,
                    "transactionId": "12345",
                    "disabilityCertificateFileURL": "https://example.com/certificate.pdf",
                    "serviceAccountId": "service-account-id",
                    "inboxReference": "inbox-reference",
                    "person": {
                        "firstname": "Sonder",
                        "lastname": "Schein",
                        "birthname": "via OS",
                        "birthdate": "01.01.1990",
                        "birthplace": "Hamburg",
                        "address": {
                            "street": "Musterstraße",
                            "streetNumber": "1",
                            "postcode": "20095",
                            "city": "Hamburg"
                        },
                        "nationality": "Deutsch"
                    },
                    "consentInfo": {
                        "submittedByThirdParty": false,
                        "gdprAccepted": true,
                        "selfDisclosureAccepted": true
                    }
                }
                """;
    }

    @Test
    public void testOSApplicationApprovedSuccessful() throws Exception {
        final UUID registerEntryId = UUID.randomUUID();

        final OSCreateLimitedLicenseApplicationCommand command = getLimitedLicenseApplicationCreatedEvent(registerEntryId);
        commandGateway.send(command).get();

        // MVC should be able to handle limited license creation without throwing an error
        String url = "http://localhost:8080/register-entries/{registerEntryId}/fishing-licenses/limited";
        url = url.replace("{registerEntryId}", registerEntryId.toString());

        final String requestBody = getCreateLicenseRequestBody();

        final ResultActions result = mvc.perform(MockMvcRequestBuilders
                .post(url)
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isCreated());
    }

    private static String getCreateLicenseRequestBody() {
        String requestBody = """
                {
                    "consentInfo": {
                        "gdprAccepted": true,
                        "selfDisclosureAccepted": true,
                        "submittedByThirdParty": false,
                        "disablityCertificateVerified": true
                    },
                    "taxes": [],
                    "person": {
                        "firstname": "Sonder",
                        "lastname": "Schein approved",
                        "birthname": "via OS",
                        "birthdate": "01.01.1990",
                        "birthplace": "Hamburg",
                        "address": {
                            "street": "Musterstraße",
                            "streetNumber": "1",
                            "postcode": "20095",
                            "city": "Hamburg"
                        },
                        "nationality": "Deutsch"
                    },
                    "validityPeriod": {
                        "validFrom": "{{validFrom}}",
                        "validTo": null
                    },
                    "limitedLicenseApproval": {
                        "signingEmployee": {
                            "name": "string",
                            "email": "<EMAIL>",
                            "phone": "string",
                            "personalSign": "string"
                        },
                        "createdAt": "{{validFrom}}",
                        "fileNumber": "string",
                        "cashRegisterSign": "string",
                        "justificationForLimitedDurationNotice": null
                    }
                }
                """;

        // Replace {{validFrom}} with the actual date
        String validFrom = LocalDate.now().format(DateTimeFormatter.ISO_DATE);
        requestBody = requestBody.replace("{{validFrom}}", validFrom);

        return requestBody;
    }

    @Test
    public void testOSApplicationRejectedSuccessful() throws Exception {
        final UUID registerEntryId = UUID.randomUUID();

        final OSCreateLimitedLicenseApplicationCommand command = getLimitedLicenseApplicationCreatedEvent(registerEntryId);
        commandGateway.send(command).get();

        // MVC should be able to handle limited license creation without throwing an error
        String url = "http://localhost:8080/register-entries/{registerEntryId}/limited-license-application:reject";
        url = url.replace("{registerEntryId}", registerEntryId.toString());

        final ResultActions result = mvc.perform(MockMvcRequestBuilders
                .post(url)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
    }

    private static OSCreateLimitedLicenseApplicationCommand getLimitedLicenseApplicationCreatedEvent(UUID registerEntryId) {
        return new OSCreateLimitedLicenseApplicationCommand(
                registerEntryId,
                FederalState.SH,
                DomainTestData.createPersonWithAddress(),
                DomainTestData.createDigitalFee(),
                DomainTestData.createConsentInfo(),
                "disabilityCertificateFileURL",
                "inboxReference",
                "serviceAccountId",
                "transactionId"
        );
    }
}
