package de.adesso.fischereiregister.registerservice.fishing_license_export;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewHandler;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.openapitools.model.ExportType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.containsString;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FishingLicenseExportReadIntegrationTest {

    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_SALT = "testSalt";
    private static final String TEST_OFFICE = "Test Office";

    private static final UUID REGISTER_ENTRY_ID = UUID.randomUUID();

    private static final UUID REGULAR_FISHING_LICENSE_DOCUMENT_ID = UUID.randomUUID();

    private static final UUID TAX_DOCUMENT_ID = UUID.randomUUID();
    private static final UUID TAX_DOCUMENT_ID_NO_TAX = UUID.randomUUID();

    private static final UUID LIMITED_LICENSE_DOCUMENT_ID = UUID.randomUUID();

    @Autowired
    private RegisterEntryViewHandler registerEntryViewHandler;

    @Autowired
    private IdentificationDocumentViewHandler identificationDocumentViewHandler;

    @Autowired
    private MockMvc mvc;

    @BeforeAll
    void setUp() {
        // Create regular fishing license digitized event
        RegularLicenseDigitizedEvent regularLicenseDigitizedEvent = createRegularLicenseDigitizedEvent(
                REGISTER_ENTRY_ID, REGULAR_FISHING_LICENSE_DOCUMENT_ID.toString());

        registerEntryViewHandler.on(regularLicenseDigitizedEvent);
        identificationDocumentViewHandler.on(regularLicenseDigitizedEvent);

        // Create tax event with tax information
        FishingTaxPayedEvent taxEvent = createTaxEvent(
                REGISTER_ENTRY_ID, TAX_DOCUMENT_ID.toString());
        registerEntryViewHandler.on(taxEvent);
        identificationDocumentViewHandler.on(taxEvent);

        // Create tax event without tax information (for negative test)
        FishingTaxPayedEvent taxEventNoTax = createTaxEventWithoutTax(
                REGISTER_ENTRY_ID, TAX_DOCUMENT_ID_NO_TAX.toString());
        registerEntryViewHandler.on(taxEventNoTax);
        identificationDocumentViewHandler.on(taxEventNoTax);

        // Create limited license event
        LimitedLicenseCreatedEvent limitedLicenseEvent = createLimitedLicenseEvent(
                REGISTER_ENTRY_ID, LIMITED_LICENSE_DOCUMENT_ID.toString());
        registerEntryViewHandler.on(limitedLicenseEvent);
        identificationDocumentViewHandler.on(limitedLicenseEvent);
    }

    private RegularLicenseDigitizedEvent createRegularLicenseDigitizedEvent(UUID registerEntryId, String documentId) {
        // Create Person
        Person person = DomainTestData.createPerson();

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");

        // Create fishing license
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("SH12345678901234");
        fishingLicense.setType(LicenseType.REGULAR);
        fishingLicense.setIssuingFederalState(FederalState.SH);
        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod());

        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(documentId);
        identificationDocument.setType(IdentificationDocumentType.PDF);
        identificationDocument.setIssuedDate(LocalDate.now());
        identificationDocument.setFishingLicense(fishingLicense);
        identificationDocument.setValidFrom(LocalDate.now());
        identificationDocument.setValidTo(LocalDate.now().plusYears(1));

        // Create RegularLicenseDigitizedEvent
        return new RegularLicenseDigitizedEvent(
                registerEntryId,
                "salt",
                person,
                jurisdiction,
                fishingLicense,
                List.of(), // fees
                List.of(), // taxes
                List.of(), // qualificationsProofs
                List.of(identificationDocument), // identificationDocuments
                null, // consentInfo
                "Test Office", // issuedByOffice
                "Test Address" // issuedByAddress
        );
    }

    private FishingTaxPayedEvent createTaxEvent(UUID registerId, String documentId) {
        // Create person
        Person person = DomainTestData.createPerson();

        // Create tax
        Tax tax = DomainTestData.createAnalogTax();

        // Create identification document with tax
        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(documentId);
        identificationDocument.setType(IdentificationDocumentType.PDF);
        identificationDocument.setIssuedDate(LocalDate.now());
        identificationDocument.setTax(tax);
        identificationDocument.setValidFrom(LocalDate.now());
        identificationDocument.setValidTo(LocalDate.now().plusYears(1));

        List<IdentificationDocument> identificationDocuments = new ArrayList<>();
        identificationDocuments.add(identificationDocument);

        // Create tax consent info
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        return new FishingTaxPayedEvent(
                registerId,
                consentInfo,
                person,
                DomainTestData.createAnalogTaxesWithOneTax(),
                TEST_SALT,
                identificationDocuments,
                TEST_OFFICE,
                null, // inboxReference
                null, // serviceAccountId
                null, // transactionId
                SubmissionType.ANALOG
        );
    }

    private FishingTaxPayedEvent createTaxEventWithoutTax(UUID registerId, String documentId) {
        // Create person
        Person person = DomainTestData.createPerson();

        // Create identification document WITHOUT tax (for negative test)
        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(documentId);
        identificationDocument.setType(IdentificationDocumentType.PDF);
        identificationDocument.setIssuedDate(LocalDate.now());
        // Note: no tax set on this document
        identificationDocument.setValidFrom(LocalDate.now());
        identificationDocument.setValidTo(LocalDate.now().plusYears(1));

        List<IdentificationDocument> identificationDocuments = new ArrayList<>();
        identificationDocuments.add(identificationDocument);

        // Create tax consent info
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        return new FishingTaxPayedEvent(
                registerId,
                consentInfo,
                person,
                DomainTestData.createAnalogTaxesWithOneTax(),
                TEST_SALT,
                identificationDocuments,
                TEST_OFFICE,
                null, // inboxReference
                null, // serviceAccountId
                null, // transactionId
                SubmissionType.ANALOG
        );
    }

    private LimitedLicenseCreatedEvent createLimitedLicenseEvent(UUID registerId, String documentId) {
        // Create person
        Person person = DomainTestData.createPersonWithAddress();

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(TEST_FEDERAL_STATE_SH);

        // Create limited license approval
        LimitedLicenseApproval limitedLicenseApproval = DomainTestData.createLimitedLicenseApproval();

        // Create limited fishing license
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("****************");
        fishingLicense.setType(LicenseType.LIMITED);
        fishingLicense.setIssuingFederalState(FederalState.SH);
        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod());
        fishingLicense.setLimitedLicenseApproval(limitedLicenseApproval);

        // Create identification document with limited license approval
        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(documentId);
        identificationDocument.setType(IdentificationDocumentType.PDF);
        identificationDocument.setIssuedDate(LocalDate.now());
        identificationDocument.setLimitedLicenseApproval(limitedLicenseApproval);
        identificationDocument.setValidFrom(LocalDate.now());
        identificationDocument.setValidTo(LocalDate.now().plusYears(1));

        List<IdentificationDocument> identificationDocuments = new ArrayList<>();
        identificationDocuments.add(identificationDocument);

        return new LimitedLicenseCreatedEvent(
                registerId,
                TEST_SALT,
                DomainTestData.createLimitedLicenseConsentInfo(),
                person,
                DomainTestData.createAnalogFeesWithOneFee(),
                DomainTestData.createAnalogTaxesWithOneTax(),
                fishingLicense,
                identificationDocuments,
                jurisdiction,
                TEST_OFFICE,
                "Test Address",
                null,
                null,
                null,
                SubmissionType.ANALOG
        );
    }

    @Test
    @DisplayName("""
            GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=FISHING_LICENSE
            Verify that the PDF for FISHING_LICENSE is exported successfully.
            """)
    void testExportDigitizedFishingLicenseSuccess() throws Exception {
        // given - test data is set up in @BeforeAll

        // when
        mvc.perform(MockMvcRequestBuilders
                        .get("/register-entries/" + REGISTER_ENTRY_ID + "/identification-documents/"
                                + REGULAR_FISHING_LICENSE_DOCUMENT_ID
                                + "/pdf?type=" + ExportType.FISHING_LICENSE.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                // then
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_PDF))
                .andExpect(header().string("Content-Disposition",
                    containsString("attachment; filename=")));
    }

    @Test
    @DisplayName("""
            GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=LIMITED_LICENSE_APPROVAL
            Verify that the PDF for LIMITED_LICENSE_APPROVAL is exported successfully.
            """)
    void testExportLimitedLicenseApprovalSuccess() throws Exception {
        // given - test data is set up in @BeforeAll

        // when
        mvc.perform(MockMvcRequestBuilders
                        .get("/register-entries/" + REGISTER_ENTRY_ID + "/identification-documents/"
                                + LIMITED_LICENSE_DOCUMENT_ID
                                + "/pdf?type=" + ExportType.LIMITED_LICENSE_APPROVAL.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                // then
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_PDF))
                .andExpect(header().string("Content-Disposition",
                    containsString("attachment; filename=")));
    }

    @Test
    @DisplayName("""
            GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=FISHING_TAXES
            Verify that the PDF for FISHING_TAXES is exported successfully.
            """)
    void testExportDigitizedFishingTaxSuccess() throws Exception {
        // given - test data is set up in @BeforeAll

        // when
        mvc.perform(MockMvcRequestBuilders
                        .get("/register-entries/" + REGISTER_ENTRY_ID +
                                "/identification-documents/" + TAX_DOCUMENT_ID +
                                "/pdf?type=" + ExportType.FISHING_TAXES.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                // then
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_PDF))
                .andExpect(header().string("Content-Disposition",
                    containsString("attachment; filename=")));
    }

    @Test
    @DisplayName("""
            GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf?type=FISHING_TAXES
            Verify that the PDF export fails when document is found but contains no tax information.
            """)
    void testExportPDFFailsDocumentFoundButNoTaxesInform() throws Exception {

        // when
        mvc.perform(MockMvcRequestBuilders
                        .get("/register-entries/" + REGISTER_ENTRY_ID +
                                "/identification-documents/" + TAX_DOCUMENT_ID_NO_TAX +
                                "/pdf?type=" + ExportType.FISHING_TAXES.name())
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_PDF))
                // then
                .andExpect(status().isNotFound());
    }


    @Test
    @DisplayName("""
            GET /api/register-entries/{registerId}/identification-documents/{documentId}/pdf
            Verify that the API returns a not found status code for non-existing ids.
            """)
    void testExportPDFFailsWithEntityNotFoundException() throws Exception {
        // given - random non-existing IDs
        final UUID registerId = UUID.randomUUID();
        final String documentId = UUID.randomUUID().toString();

        // when
        mvc.perform(get("/register-entries/" + registerId +
                        "/identification-documents/" + documentId +
                        "/pdf?type=" + ExportType.FISHING_LICENSE.name()))
                // then
                .andExpect(status().isNotFound());
    }
}
