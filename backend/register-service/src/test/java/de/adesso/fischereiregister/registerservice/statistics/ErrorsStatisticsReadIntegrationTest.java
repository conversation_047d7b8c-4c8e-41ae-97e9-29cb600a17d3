package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.JsonParser;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.registerservice.online_services.determination.OSRegisterDeterminationService;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRegisterDeterminationResult;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRequestStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.openapitools.model.OrderFishingLicenseRequestOS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ErrorsStatisticsReadIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private OSRegisterDeterminationService osRegisterDeterminationService;

    /**
     * This test verifies that the errors statistics endpoint returns a 200 OK status code and a list of errors.
     * It also tests that the SQL select query for errors statistics returns correct data using sample data,
     * which covers the edge case of the sum of errors when no federal state is given.
     */
    @Test
    @DisplayName("""
            	GET /api/statistics/errors
            	Verify that the errors statistics endpoint returns a 200 OK status code and a list of errors.
            	( Also tests that sql select query for errors statistics returns correct data using sample data which covers following edge case:
            	- sum of errors when no federal state is given)
            """)
    @Sql(scripts = "/config/liquibase/non-axon-test-data/errors_protocol/setup-error-statistics-data.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(scripts = "/config/liquibase/non-axon-test-data/errors_protocol/cleanup-error-statistics-data.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void testExportDigitizedFishingCertificateSuccess() throws Exception {

        mvc.perform(MockMvcRequestBuilders
                        .get("http://localhost:8080/statistics/errors")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", Matchers.hasSize(5)))
                .andExpect(jsonPath("$[?(@.year == 2025)].data.system").value(2))
                .andExpect(jsonPath("$[?(@.year == 2021)].data.onlineService").value(0))
                .andExpect(jsonPath("$[?(@.year == 2021)].data.cardPrinterService").value(6))
                .andExpect(jsonPath("$[?(@.year == 2021)].data.system").value(4))
                .andExpect(jsonPath("$[?(@.year == 2023)].data.onlineService").value(5))
                .andExpect(jsonPath("$[?(@.year == 2023)].data.cardPrinterService").value(0))
                .andExpect(jsonPath("$[?(@.year == 2023)].data.system").value(3))
                .andExpect(jsonPath("$[?(@.year == 2026)].data.onlineService").value(0))
                .andExpect(jsonPath("$[?(@.year == 2026)].data.cardPrinterService").value(0))
                .andExpect(jsonPath("$[?(@.year == 2026)].data.system").value(8))
                .andExpect(jsonPath("$[?(@.year == 2022)].data.onlineService").value(2))
                .andExpect(jsonPath("$[?(@.year == 2022)].data.cardPrinterService").value(7))
                .andExpect(jsonPath("$[?(@.year == 2022)].data.system").value(0));
    }


    @Test
    @DisplayName("""
             GET /api/os/v1/fishing-license
             verify that an error is written in the error protocol when the online service fails
            """)
    void testProtocolUsingOnlineServiceFailure() throws Exception {
        // Mock behavior for this test
        OrderFishingLicenseRequestOS request = TestDataUtil.createFailingOrderFishingLicenseRequestOS();
        when(osRegisterDeterminationService.determineRegisterEntryId(any(), any()))
                .thenReturn(OSRegisterDeterminationResult.status(OSRequestStatus.MULTIPLE_PERSONS_FOUND));

        mvc.perform(MockMvcRequestBuilders
                        .post("http://localhost:8080/os/v1/fishing-license")
                        .content(JsonParser.asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is4xxClientError())
                .andExpect(jsonPath("$.errorDescription").value("MULTIPLE_PERSONS_FOUND"));

        String yearOfToday = String.valueOf(java.time.LocalDate.now().getYear());
        mvc.perform(MockMvcRequestBuilders
                        .get("http://localhost:8080/statistics/errors")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", Matchers.hasSize(1)))
                .andExpect(jsonPath("$[?(@.year == " + yearOfToday + ")].data.onlineService").value(1));
    }

}
