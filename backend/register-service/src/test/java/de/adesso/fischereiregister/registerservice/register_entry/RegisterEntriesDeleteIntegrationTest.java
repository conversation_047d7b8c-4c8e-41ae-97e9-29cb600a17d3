
package de.adesso.fischereiregister.registerservice.register_entry;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;
import java.util.UUID;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
// Use PER_METHOD, since double document ids in test-data csv lead to issues when event handlers are called (can be removed later)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class RegisterEntriesDeleteIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @Autowired
    private CommandGateway commandGateway;

    @Test
    @DisplayName("""
				GET /api/register-entries/{registerEntryId}}?deletionReason=GDPR_REQUEST
				verify that the register entry can be deleted successfully
			""")
    public void testDeleteRegisterEntrySuccessful() throws Exception {
        // Arrange
        UUID registerEntryId = UUID.randomUUID();

        final DigitizeRegularLicenseCommand command = getDigitizeLicenseCommand(registerEntryId);
        commandGateway.send(command).get();


        // Act
        ResultActions result = mvc.perform(MockMvcRequestBuilders
                .delete("http://localhost:8080/register-entries/{registerEntryId}?deletionReason=GDPR_REQUEST", registerEntryId.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        // Assert
        result.andExpect(status().isOk());
    }

    private DigitizeRegularLicenseCommand getDigitizeLicenseCommand(UUID registerEntryId) {

        QualificationsProof qualificationsProof = TestDataUtil.createQualificationsProof();
        qualificationsProof.setIssuedBy("IssuedBy");

        Tax tax = TestDataUtil.createAnalogTax();
        tax.getPaymentInfo().setAmount(17.0);
        Fee fee = TestDataUtil.createAnalogFee();
        fee.getPaymentInfo().setAmount(32.0);

        return new DigitizeRegularLicenseCommand(
                registerEntryId,
                "salt",
                TestDataUtil.createPersonWithAddress(),
                List.of(fee),
                List.of(tax),
                List.of(),
                List.of(qualificationsProof),
                TestDataUtil.createConsentInfo(),
                TestDataUtil.createUserDetails(UserRole.OFFICIAL));
    }

    @Test
    @DisplayName("""
				GET /api/register-entries/{registerEntryId}}?deletionReason=GDPR_REQUEST
				verify that the register entry cannot be deleted if the register entry does not exist
			""")
    public void testDeleteRegisterEntryMissingUserDetails() throws Exception {
        // Arrange
        UUID registerEntryId = UUID.randomUUID();

        // Act
        ResultActions result = mvc.perform(MockMvcRequestBuilders
                .delete("http://localhost:8080/register-entries/{registerEntryId}?deletionReason=GDPR_REQUEST", registerEntryId.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        // Assert
        result.andExpect(status().isNotFound());
    }
}