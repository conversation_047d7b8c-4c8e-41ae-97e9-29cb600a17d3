package de.adesso.fischereiregister.card_orders.eventhandling;

import de.adesso.fischereiregister.card_orders.services.CardOrderService;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.eventhandling.ReplayStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class CardOrderHandlerTest {

    @InjectMocks
    CardOrderHandler cardOrderHandler;

    @Mock
    CardOrderService cardOrderService;

    @Test
    @DisplayName("ReplacementCardOrderedHandler.on ReplacementCardOrderedEvent should not do anything if its a replay")
    public void testReplacementCardOrderedEventDoesNotTriggerOnReplay() {
        // GIVEN
        // WHEN
        cardOrderHandler.on(new ReplacementCardOrderedEvent(
                UUID.randomUUID(),
                DomainTestData.createLicense(),
                DomainTestData.createPerson(),
                List.of(),
                "salt",
                FederalState.BE,
                "issuedByOffice",
                "issuedByAddress",
                List.of(),
                List.of(),
                DomainTestData.createConsentInfo(),
                null,
                null,
                null,
                SubmissionType.ANALOG
        ), ReplayStatus.REPLAY);

        verify(cardOrderService, times(0)).issueCardOrder(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("ReplacementCardOrderedHandler.on ReplacementCardOrderedEvent should issue a card order if it is not a replay.")
    public void testReplacementCardOrderedEventTriggersSuccessfully() {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final FishingLicense license = DomainTestData.createLicense();
        final Person person = DomainTestData.createPerson();
        final List<IdentificationDocument> documents = List.of(getCardDocument());
        final String salt = "salt";
        final FederalState federalState = FederalState.BE;
        final String issuedByAddress = "issuedByAddress";
        final String issuedByOffice = "issuedByOffice";
        final List<Fee> fees = DomainTestData.createAnalogFeesWithOneFee();
        final List<Tax> taxes = DomainTestData.createAnalogTaxesWithOneTax();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        // WHEN
        cardOrderHandler.on(new ReplacementCardOrderedEvent(
                registerEntryId,
                license,
                person,
                documents,
                salt,
                federalState,
                issuedByOffice,
                issuedByAddress,
                fees,
                taxes,
                consentInfo,
                null,
                null,
                null,
                SubmissionType.ANALOG
        ), ReplayStatus.REGULAR);

        verify(cardOrderService, times(1)).issueCardOrder(registerEntryId, person, license, documents.getFirst(), salt, issuedByAddress, issuedByOffice);
    }

    @Test
    @DisplayName("ReplacementCardOrderedHandler.on RegularLicenseCreatedEvent should not do anything if its a replay")
    public void testRegularLicenseCreatedEventDoesNotTriggerOnReplay() {
        // GIVEN
        // WHEN
        cardOrderHandler.on(new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                "salt",
                DomainTestData.createConsentInfo(),
                DomainTestData.createPerson(),
                List.of(),
                List.of(),
                DomainTestData.createLicense(),
                List.of(),
                DomainTestData.createJurisdiction(),
                "issuedByOffice",
                "issuedByAddress",
                null,
                null,
                null,
                SubmissionType.ANALOG
        ), ReplayStatus.REPLAY);

        verify(cardOrderService, times(0)).issueCardOrder(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("ReplacementCardOrderedHandler.on RegularLicenseCreatedEvent should issue a card order if it is not a replay.")
    public void testRegularLicenseCreatedEventTriggersSuccessfully() {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "salt";
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();
        final Person person = DomainTestData.createPerson();
        final FishingLicense license = DomainTestData.createLicense();
        final List<IdentificationDocument> documents = List.of(getCardDocument());
        final String issuedByAddress = "issuedByAddress";
        final String issuedByOffice = "issuedByOffice";
        // WHEN
        cardOrderHandler.on(new RegularLicenseCreatedEvent(
                registerEntryId,
                salt,
                consentInfo,
                person,
                List.of(),
                List.of(),
                license,
                documents,
                null,
                issuedByOffice,
                issuedByAddress,
                null,
                null,
                null,
                SubmissionType.ANALOG
        ), ReplayStatus.REGULAR);

        verify(cardOrderService, times(1)).issueCardOrder(registerEntryId, person, license, documents.getFirst(), salt, issuedByAddress, issuedByOffice);
    }


    @Test
    @DisplayName("ReplacementCardOrderedHandler.on RegularLicenseDigitizedEvent should not do anything if its a replay")
    public void testRegularLicenseDigitizedEventDoesNotTriggerOnReplay() {
        // GIVEN
        // WHEN
        cardOrderHandler.on(new RegularLicenseDigitizedEvent(
                UUID.randomUUID(),
                "salt",
                DomainTestData.createPerson(),
                DomainTestData.createJurisdiction(),
                DomainTestData.createLicense(),
                List.of(),
                List.of(),
                List.of(),
                List.of(),
                DomainTestData.createConsentInfo(),
                "issuedByOffice",
                "issuedByAddress"
        ), ReplayStatus.REPLAY);

        verify(cardOrderService, times(0)).issueCardOrder(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("ReplacementCardOrderedHandler.on RegularLicenseDigitizedEvent should issue a card order if it is not a replay.")
    public void testRegularLicenseDigitizedEventTriggersSuccessfully() {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final Person person = DomainTestData.createPerson();
        final FishingLicense license = DomainTestData.createLicense();
        final List<IdentificationDocument> documents = List.of(getCardDocument());
        final String salt = "salt";
        final String issuedByAddress = "issuedByAddress";
        final String issuedByOffice = "issuedByOffice";

        // WHEN
        cardOrderHandler.on(new RegularLicenseDigitizedEvent(
                registerEntryId,
                salt,
                person,
                DomainTestData.createJurisdiction(),
                license,
                List.of(),
                List.of(),
                List.of(),
                documents,
                DomainTestData.createConsentInfo(),
                issuedByOffice,
                issuedByAddress
        ), ReplayStatus.REGULAR);

        // THEN
        verify(cardOrderService, times(1)).issueCardOrder(registerEntryId, person, license, documents.getFirst(), salt, issuedByAddress, issuedByOffice);
    }


    private IdentificationDocument getCardDocument() {
        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setType(IdentificationDocumentType.CARD);
        identificationDocument.setDocumentId(UUID.randomUUID().toString());
        identificationDocument.setFishingLicense(DomainTestData.createLicense());
        identificationDocument.setIssuedDate(LocalDate.MIN);

        return identificationDocument;
    }

    @Test
    @DisplayName("LimitedLicenseEvent should not do anything if its a replay")
    public void testLimitedLicenseEventDoesNotTriggerOnReplay() {
        // GIVEN
        FishingLicense fishingLicense =DomainTestData.createLicense();
        Jurisdiction jurisdiction = DomainTestData.createJurisdiction();
        //GIVEN

        final LimitedLicenseCreatedEvent event = new LimitedLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(),
                fishingLicense,
                List.of(),
                jurisdiction,
                 "issuedByOffice",
                "",
                null,
                null,
                null,
                SubmissionType.ANALOG
        );

        // WHEN
        cardOrderHandler.on(event, ReplayStatus.REPLAY);

        verify(cardOrderService, times(0)).issueCardOrder(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("LimitedLicenseEvent should trigger Order")
    public void testLimitedLicenseEventDoesTriggersCardOrder() {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final Person person = DomainTestData.createPerson();
        final String salt = "salt";
        final String issuedByAddress = "issuedByAddress";
        final String issuedByOffice = "issuedByOffice";
        FishingLicense fishingLicense = DomainTestData.createLicense();
        Jurisdiction jurisdiction = DomainTestData.createJurisdiction();
        final List<IdentificationDocument> documents = List.of(getCardDocument());

        final LimitedLicenseCreatedEvent event = new LimitedLicenseCreatedEvent(
                registerEntryId,
                salt,
                null,
                person,
                List.of(),
                List.of(),
                fishingLicense,
                documents,
                jurisdiction,
                issuedByOffice,
                issuedByAddress,
                null,
                null,
                null,
                SubmissionType.ANALOG
        );

        // WHEN
        cardOrderHandler.on(event, ReplayStatus.REGULAR);

        verify(cardOrderService, times(1)).issueCardOrder(registerEntryId, person, fishingLicense, documents.getFirst(), salt, issuedByAddress, issuedByOffice);
    }
}