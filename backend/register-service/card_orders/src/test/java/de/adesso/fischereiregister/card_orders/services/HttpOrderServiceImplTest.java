package de.adesso.fischereiregister.card_orders.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.adesso.fischereiregister.card_orders.config.CardOrderConfig;
import de.adesso.fischereiregister.card_orders.ports.HashingPort;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class HttpOrderServiceImplTest {
    @Mock
    private CardOrderConfig cardOrderConfig;

    @Mock
    private WebClient webClient;

    @Spy
    private ObjectMapper objectMapper = new ObjectMapper();

    @Mock
    private HashingPort hashingPort;

    @InjectMocks
    private HttpOrderServiceImpl httpOrderServiceImpl;

    @Test
    @DisplayName("HttpOrderServiceImpl.registerOrder throws exception when all inputs are null.")
    public void testRegisterOrderThrowsExceptionOnNullInput() {
        // When
        assertThrows(IllegalStateException.class, () -> httpOrderServiceImpl.registerOrder(
                null,
                null,
                new RegisterOrderInformation(null, null, null, null),
                null,
                null));
    }

    @Test
    @DisplayName("HttpOrderServiceImpl.registerOrder throws exception when all inputs are empty.")
    public void testRegisterOrderThrowsExceptionOnEmptyLicense() {
        // When
        assertThrows(IllegalStateException.class, () -> httpOrderServiceImpl.registerOrder(
                UUID.randomUUID(),
                LocalDate.now(),
                new RegisterOrderInformation(new FishingLicense(), DomainTestData.createPersonWithAddress(), UUID.randomUUID(), new IdentificationDocument()),
                "salt",
                "address"));
    }

    @Test
    @DisplayName("HttpOrderServiceImpl.registerOrder throws exception when person is empty.")
    public void testRegisterOrderThrowsExceptionOnEmptyPerson() {
        // When
        assertThrows(IllegalStateException.class, () -> httpOrderServiceImpl.registerOrder(
                UUID.randomUUID(),
                LocalDate.now(),
                new RegisterOrderInformation(DomainTestData.createLicense(), new Person(), UUID.randomUUID(), new IdentificationDocument()),
                "salt",
                "address"));
    }

    @Test
    @DisplayName("HttpOrderServiceImpl.registerOrder throws exception when address is empty.")
    public void testRegisterOrderThrowsExceptionOnEmptyAddress() {
        // When
        assertThrows(IllegalStateException.class, () -> httpOrderServiceImpl.registerOrder(
                UUID.randomUUID(),
                LocalDate.now(),
                new RegisterOrderInformation(DomainTestData.createLicense(), DomainTestData.createPerson(), UUID.randomUUID(), new IdentificationDocument()),
                "salt",
                "address"));
    }

    @Test
    @DisplayName("HTTPOrderServiceImpl.registerOrder is successful when config option enabled is false")
    public void testRegisterOrderIsSuccessfulWhenConfigOptionEnabledIsFalse() {
        when(cardOrderConfig.isEnabled()).thenReturn(false);

        assertDoesNotThrow(() -> httpOrderServiceImpl.registerOrder(
                UUID.randomUUID(),
                LocalDate.now(),
                new RegisterOrderInformation(DomainTestData.createLicense(), DomainTestData.createPersonWithAddress(), UUID.randomUUID(), new IdentificationDocument()),
                "salt",
                "address"));
    }

    @Test
    @DisplayName("HTTPOrderServiceImpl.registerOrder throws exception when config option enabled is true")
    public void testRegisterOrderIsSuccessfulWhenConfigOptionEnabledIsTrue() {
        when(cardOrderConfig.isEnabled()).thenReturn(true);

        assertThrows(UnsupportedOperationException.class, () -> httpOrderServiceImpl.registerOrder(
                UUID.randomUUID(),
                LocalDate.now(),
                new RegisterOrderInformation(DomainTestData.createLicense(), DomainTestData.createPersonWithAddress(), UUID.randomUUID(), new IdentificationDocument()),
                "salt",
                "address"));
    }
}
