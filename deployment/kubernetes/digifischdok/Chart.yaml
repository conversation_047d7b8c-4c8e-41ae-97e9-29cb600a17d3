apiVersion: v2
name: digifischdok
description: Umbrella chart for all digifischdok components
type: application

# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 2.1.2

# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# It is recommended to use it with quotes.
appVersion: "1.0.0"

dependencies:
  - name: register-service
    version: "2.1.2"
    repository: "oci://digifischdok-oci-dev.repo-ex.zcdi.dataport.de/artifactory/digifischdok-oci-dev"
    #repository: "file://../register-service" # This is only for local testing.
  - name: web-app
    version: "2.1.2"
    repository: "oci://digifischdok-oci-dev.repo-ex.zcdi.dataport.de/artifactory/digifischdok-oci-dev"
    #repository: "file://../web-app" # This is only for local testing.